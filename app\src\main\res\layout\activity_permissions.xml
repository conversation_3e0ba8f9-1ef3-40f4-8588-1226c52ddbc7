<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/gradient_header_background"
    android:padding="24dp">

    <!-- Header Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:layout_marginTop="40dp"
        android:layout_marginBottom="40dp">

        <!-- App Icon -->
        <ImageView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:src="@drawable/icon_no_background"
            android:layout_marginBottom="16dp" />

        <!-- Welcome Title -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Welcome to SaveIt!"
            android:textSize="28sp"
            android:textStyle="bold"
            android:textColor="@color/white"
            android:gravity="center"
            android:layout_marginBottom="8dp" />

        <!-- Subtitle -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Download Instagram content easily"
            android:textSize="16sp"
            android:textColor="@color/white"
            android:alpha="0.9"
            android:gravity="center" />

    </LinearLayout>

    <!-- Permission Card -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="16dp"
        app:cardElevation="8dp"
        android:layout_marginBottom="24dp"
        app:cardBackgroundColor="@color/white">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- Storage Icon -->
            <ImageView
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:src="@drawable/ic_storage"
                android:layout_gravity="center"
                android:layout_marginBottom="16dp"
                android:tint="@color/insta_red" />

            <!-- Permission Title -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Storage Permission Required"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="@color/black"
                android:layout_gravity="center"
                android:layout_marginBottom="12dp" />

            <!-- Permission Description -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="To download and save Instagram content to your device, we need access to your storage."
                android:textSize="14sp"
                android:textColor="@color/black"
                android:alpha="0.8"
                android:gravity="center"
                android:lineSpacingExtra="4dp"
                android:layout_marginBottom="16dp" />

            <!-- Features List -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginBottom="16dp">

                <!-- Feature 1 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/ic_check"
                        android:tint="@color/green"
                        android:layout_marginEnd="12dp"
                        android:layout_gravity="center_vertical" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Save photos and videos"
                        android:textSize="14sp"
                        android:textColor="@color/black"
                        android:alpha="0.8" />

                </LinearLayout>

                <!-- Feature 2 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/ic_check"
                        android:tint="@color/green"
                        android:layout_marginEnd="12dp"
                        android:layout_gravity="center_vertical" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Access your downloads"
                        android:textSize="14sp"
                        android:textColor="@color/black"
                        android:alpha="0.8" />

                </LinearLayout>

                <!-- Feature 3 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/ic_check"
                        android:tint="@color/green"
                        android:layout_marginEnd="12dp"
                        android:layout_gravity="center_vertical" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Preview downloaded content"
                        android:textSize="14sp"
                        android:textColor="@color/black"
                        android:alpha="0.8" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Spacer -->
    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <!-- Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Allow Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnAllowPermission"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="Allow Storage Access"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/white"
            app:backgroundTint="@color/insta_red"
            app:cornerRadius="28dp"
            app:icon="@drawable/ic_check"
            app:iconTint="@color/white"
            android:layout_marginBottom="12dp" />

        <!-- Skip Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnSkipPermission"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:text="Skip for now"
            android:textSize="14sp"
            android:textColor="@color/white"
            android:alpha="0.8"
            style="@style/Widget.Material3.Button.TextButton"
            app:rippleColor="@color/white" />

    </LinearLayout>

</LinearLayout>
