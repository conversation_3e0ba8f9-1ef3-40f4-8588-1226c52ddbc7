package com.app.videofbdownloadfree

import android.content.Context
import android.util.Log
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.interstitial.InterstitialAd
import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback
import com.google.android.gms.ads.rewarded.RewardedAd
import com.google.android.gms.ads.rewarded.RewardedAdLoadCallback
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * Preloads ads in background for better user experience
 */
object AdPreloader {
    private const val TAG = "AdPreloader"
    private var preloadedInterstitial: InterstitialAd? = null
    private var preloadedRewarded: RewardedAd? = null
    private var isPreloadingInterstitial = false
    private var isPreloadingRewarded = false

    // Background preloading queue
    private var backgroundPreloadingEnabled = true
    private var lastPreloadTime = 0L
    private const val PRELOAD_INTERVAL = 30000L // 30 seconds between preloads

    /**
     * Preload all ad types in background
     */
    fun preloadAds(context: Context, forcePreload: Boolean = false) {
        if (!AdConfig.adsEnabled) {
            Log.d(TAG, "Ads disabled, skipping preload")
            return
        }

        // Check if context is valid
        if (context is android.app.Activity && (context.isFinishing || context.isDestroyed)) {
            Log.w(TAG, "Activity is finishing/destroyed, skipping preload")
            return
        }

        // Throttle preloading to avoid excessive requests
        val currentTime = System.currentTimeMillis()
        if (!forcePreload && (currentTime - lastPreloadTime) < PRELOAD_INTERVAL) {
            Log.d(TAG, "Preload throttled, too soon since last preload")
            return
        }

        lastPreloadTime = currentTime

        try {
            // Use Main dispatcher since AdMob requires main thread
            CoroutineScope(Dispatchers.Main).launch {
                if (backgroundPreloadingEnabled) {
                    preloadInterstitialAd(context)
                    preloadRewardedAd(context)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start preloading ads: ${e.message}")
        }
    }

    /**
     * Enable/disable background preloading
     */
    fun setBackgroundPreloadingEnabled(enabled: Boolean) {
        backgroundPreloadingEnabled = enabled
        Log.d(TAG, "Background preloading ${if (enabled) "enabled" else "disabled"}")
    }

    /**
     * Preload interstitial ad
     */
    private fun preloadInterstitialAd(context: Context) {
        if (isPreloadingInterstitial || preloadedInterstitial != null) return

        try {
            isPreloadingInterstitial = true
            Log.d(TAG, "Preloading interstitial ad")

            val adRequest = AdRequest.Builder().build()
            InterstitialAd.load(context, AdConfig.interstitialAdUnitId, adRequest,
                object : InterstitialAdLoadCallback() {
                    override fun onAdLoaded(ad: InterstitialAd) {
                        preloadedInterstitial = ad
                        isPreloadingInterstitial = false
                        Log.d(TAG, "Interstitial ad preloaded successfully")
                    }

                    override fun onAdFailedToLoad(error: LoadAdError) {
                        isPreloadingInterstitial = false
                        Log.e(TAG, "Failed to preload interstitial ad: ${error.message}")
                    }
                })
        } catch (e: Exception) {
            isPreloadingInterstitial = false
            Log.e(TAG, "Exception preloading interstitial ad: ${e.message}")
        }
    }

    /**
     * Preload rewarded ad
     */
    private fun preloadRewardedAd(context: Context) {
        if (isPreloadingRewarded || preloadedRewarded != null) return

        try {
            isPreloadingRewarded = true
            Log.d(TAG, "Preloading rewarded ad")

            val adRequest = AdRequest.Builder().build()
            RewardedAd.load(context, AdConfig.rewardedAdUnitId, adRequest,
                object : RewardedAdLoadCallback() {
                    override fun onAdLoaded(ad: RewardedAd) {
                        preloadedRewarded = ad
                        isPreloadingRewarded = false
                        Log.d(TAG, "Rewarded ad preloaded successfully")
                    }

                    override fun onAdFailedToLoad(error: LoadAdError) {
                        isPreloadingRewarded = false
                        Log.e(TAG, "Failed to preload rewarded ad: ${error.message}")
                    }
                })
        } catch (e: Exception) {
            isPreloadingRewarded = false
            Log.e(TAG, "Exception preloading rewarded ad: ${e.message}")
        }
    }

    /**
     * Get preloaded interstitial ad
     */
    fun getPreloadedInterstitial(context: Context): InterstitialAd? {
        val ad = preloadedInterstitial
        preloadedInterstitial = null
        
        // Preload next ad
        preloadInterstitialAd(context)
        
        return ad
    }

    /**
     * Get preloaded rewarded ad
     */
    fun getPreloadedRewarded(context: Context): RewardedAd? {
        val ad = preloadedRewarded
        preloadedRewarded = null
        
        // Preload next ad
        preloadRewardedAd(context)
        
        return ad
    }

    /**
     * Check if interstitial ad is ready
     */
    fun isInterstitialReady(): Boolean = preloadedInterstitial != null

    /**
     * Check if rewarded ad is ready
     */
    fun isRewardedReady(): Boolean = preloadedRewarded != null

    /**
     * Clear all preloaded ads
     */
    fun clearAds() {
        preloadedInterstitial = null
        preloadedRewarded = null
        isPreloadingInterstitial = false
        isPreloadingRewarded = false
        Log.d(TAG, "All preloaded ads cleared")
    }
}
