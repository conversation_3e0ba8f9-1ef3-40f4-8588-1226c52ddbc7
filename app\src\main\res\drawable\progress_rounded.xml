<!-- res/drawable/progress_rounded.xml -->
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- Background -->
    <item android:id="@android:id/background">
        <shape android:shape="rectangle">
            <solid android:color="#E0E0E0" /> <!-- Light gray background -->
            <corners android:radius="8dp" /> <!-- Rounded corners -->
        </shape>
    </item>

    <!-- Secondary progress (optional, for buffering etc) -->
    <item android:id="@android:id/secondaryProgress">
        <clip>
            <shape android:shape="rectangle">
                <solid android:color="#B0BEC5" /> <!-- Slightly darker gray -->
                <corners android:radius="8dp" />
            </shape>
        </clip>
    </item>

    <!-- Progress -->
    <item android:id="@android:id/progress">
        <clip>
            <shape android:shape="rectangle">
                <solid android:color="?attr/colorPrimary" /> <!-- Use your primary color -->
                <corners android:radius="8dp" />
            </shape>
        </clip>
    </item>

</layer-list>
