<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:ads="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    tools:context=".MainActivity">

    <!-- App Bar with Collapsing Toolbar -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        android:background="@drawable/gradient_header_background"
        android:fitsSystemWindows="true"
        app:elevation="0dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingHorizontal="10dp">

            <TextView
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:text="SaveIt"
                android:textSize="20sp"
                android:fontFamily="@font/poppins_bold"
                android:textColor="@android:color/white"
                android:maxLines="1"
                android:ellipsize="end" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Easy • Fast • Secure"
                android:textSize="16sp"
                android:fontFamily="@font/poppins_regular"
                android:textColor="@android:color/white"
                android:maxLines="1"
                android:ellipsize="end"
                android:paddingStart="8dp" />

        </LinearLayout>

    </com.google.android.material.appbar.AppBarLayout>



    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:id="@+id/main_scroll_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fillViewport="true"
        android:clipToPadding="false"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp"
            android:paddingBottom="180dp">

            <!-- URL Input Card -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/inputCard"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="20dp"
                app:cardElevation="8dp"
                app:cardBackgroundColor="@android:color/white">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- Input Section Title -->
                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content">
                        <ImageView
                            android:layout_width="25dp"
                            android:layout_height="25dp"
                            android:layout_marginStart="12dp"
                            android:layout_marginEnd="12dp"
                            android:src="@drawable/link_icon"/>
                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Paste Instagram URL"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@android:color/black"
                            android:layout_marginBottom="16dp"
                            android:fontFamily="@font/poppins_bold" />

                    </LinearLayout>

                    <!-- Input Layout -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:background="@drawable/input_background"
                        android:padding="8dp"
                        android:minHeight="56dp">

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            app:boxBackgroundMode="none"
                            app:hintEnabled="false">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/inputBox"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:minHeight="40dp"
                                android:gravity="center_vertical|start"
                                android:hint="@string/paste_url_here"
                                android:inputType="textUri"
                                android:imeOptions="actionDone"
                                android:textColor="@android:color/black"
                                android:textColorHint="@android:color/darker_gray"
                                android:textSize="17sp"
                                android:fontFamily="@font/poppins_regular"
                                android:background="@null"
                                android:paddingHorizontal="12dp"
                                android:paddingVertical="8dp"
                                android:focusableInTouchMode="true" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnPaste"
                            android:layout_width="wrap_content"
                            android:layout_height="40dp"
                            android:minWidth="80dp"
                            android:text="@string/paste"
                            android:textColor="@android:color/white"
                            android:textAllCaps="false"
                            android:textStyle="bold"
                            android:textSize="12sp"
                            android:layout_marginStart="8dp"
                            android:backgroundTint="#667eea"
                            app:cornerRadius="12dp"
                            app:icon="@drawable/contentpaste"
                            style="@style/Widget.MaterialComponents.Button" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Download Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/button"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="@string/download_now"
                android:textAllCaps="false"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@android:color/white"
                android:layout_marginBottom="8dp"
                android:background="@drawable/download_button_selector"
                app:cornerRadius="28dp"
                app:iconGravity="textStart"
                app:icon="@drawable/download_btn"
                style="@style/Widget.MaterialComponents.Button" />

            <!-- Download Counter -->
            <TextView
                android:id="@+id/downloadCounter"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/free_downloads_5_5"
                android:textAlignment="center"
                android:textSize="14sp"
                android:textColor="@color/insta_purple"
                android:layout_marginBottom="16dp"
                android:background="@android:color/transparent"
                android:padding="8dp" />

            <!-- Native Ad Container (after download button) -->
            <FrameLayout
                android:id="@+id/nativeAdContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:visibility="gone" />

            <!-- Progress Section -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/progressCard"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:visibility="gone"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@android:color/white">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/downloading"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@android:color/black"
                        android:layout_marginBottom="12dp" />

                    <ProgressBar
                        android:id="@+id/progressBar"
                        android:layout_width="match_parent"
                        android:layout_height="8dp"
                        android:progressTint="@color/insta_purple"
                        android:progressBackgroundTint="#E9ECEF"
                        android:indeterminate="false"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/StatusText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text=""
                        android:textAlignment="center"
                        android:textColor="@color/insta_purple"
                        android:textSize="14sp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Quick Actions -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@android:color/white">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Quick Actions"
                        android:textSize="18sp"
                        android:textAlignment="center"
                        android:textStyle="bold"
                        android:textColor="@android:color/black"
                        android:layout_marginBottom="16dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnRecent"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:layout_height="48dp"
                            android:textAllCaps="false"
                            android:layout_marginEnd="8dp"
                            android:background="@drawable/rounded_button"
                            app:icon="@drawable/folder"
                            android:text="Recent"
                            style="@style/Widget.MaterialComponents.Button"/>


                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnHelp"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:layout_height="48dp"
                            android:text="@string/how_to_use"
                            android:textAllCaps="false"
                            android:textColor="@color/insta_orange"
                            android:layout_marginStart="8dp"
                            android:backgroundTint="@android:color/transparent"
                            app:strokeColor="@color/insta_orange"
                            app:strokeWidth="2dp"
                            app:cornerRadius="12dp"
                            app:icon="@drawable/help"
                            app:iconTint="@color/insta_orange"
                            style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>



            <!-- Last 5 Downloads Section -->
            <LinearLayout
                android:id="@+id/recentDownloadsSection"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="16dp"
                android:visibility="gone">

                <!-- Section Header -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:paddingHorizontal="16dp"
                    android:paddingVertical="12dp">

                    <ImageView
                        android:layout_width="28dp"
                        android:layout_height="28dp"
                        android:src="@drawable/folder"
                        app:tint="@color/insta_purple"
                        android:layout_marginEnd="12dp" />

                    <TextView
                        android:id="@+id/sectionTitle"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Last 5 Downloads"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:textColor="@color/black" />

                    <TextView
                        android:id="@+id/seeAllButton"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="See All"
                        android:textSize="14sp"
                        android:textColor="@color/insta_purple"
                        android:background="?attr/selectableItemBackground"
                        android:padding="8dp"
                        android:textStyle="bold" />

                </LinearLayout>

                <!-- Last 5 Downloads RecyclerView -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recentDownloadsRecyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false"
                    android:clipToPadding="false"
                    android:paddingTop="8dp"
                    android:paddingBottom="8dp" />



            </LinearLayout>

            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:background="#fff"
                android:elevation="0dp"
                app:cardCornerRadius="0dp"
                android:backgroundTint="@color/white"
                app:strokeColor="@color/white"
                app:strokeWidth="0dp"
                android:layout_height="200dp"/>
        </LinearLayout>

    </androidx.core.widget.NestedScrollView>
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@android:color/white">
        <!-- Banner Ad above bottom navigation -->
        <LinearLayout
            android:id="@+id/adContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:elevation="8dp"
            android:layout_gravity="bottom"
            android:layout_marginBottom="80dp"
            android:gravity="center" />

        <!-- Bottom Navigation Bar - 3 Items Only -->
        <LinearLayout
            android:id="@+id/bottomNavigation"
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:background="#fff"
            android:orientation="horizontal"
            android:layout_gravity="bottom"
            android:gravity="bottom"
            android:paddingHorizontal="16dp">

            <!-- Home -->
            <LinearLayout
                android:id="@+id/navHome"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="10dp"
                android:background="@drawable/nav_item_background">

                <ImageView
                    android:layout_width="28dp"
                    android:layout_height="28dp"
                    android:src="@drawable/menu_home"
                    app:tint="@color/insta_purple"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Home"
                    android:fontFamily="@font/poppins_bold"
                    android:textSize="12sp"
                    android:textColor="@color/insta_purple"
                    android:textStyle="bold"
                    android:layout_marginTop="4dp" />

            </LinearLayout>

            <!-- Downloads -->
            <LinearLayout
                android:id="@+id/navDownloads"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="10dp"
                android:background="@drawable/nav_item_background">

                <ImageView
                    android:layout_width="28dp"
                    android:layout_height="28dp"
                    android:src="@drawable/menu_downloads"
                    app:tint="@android:color/darker_gray" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Downloads"
                    android:fontFamily="@font/poppins_regular"

                    android:textSize="12sp"
                    android:textColor="@color/gray"
                    android:layout_marginTop="4dp" />

            </LinearLayout>

            <!-- Settings -->
            <LinearLayout
                android:id="@+id/navSettings"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="10dp"
                android:background="@drawable/nav_item_background">

                <ImageView
                    android:layout_width="28dp"
                    android:layout_height="28dp"
                    android:src="@drawable/setting"
                    app:tint="@color/gray"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Settings"
                    android:textSize="12sp"
                    android:textColor="@color/gray"
                    android:fontFamily="@font/poppins_bold"
                    android:layout_marginTop="4dp" />

            </LinearLayout>

        </LinearLayout>

    </FrameLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>
