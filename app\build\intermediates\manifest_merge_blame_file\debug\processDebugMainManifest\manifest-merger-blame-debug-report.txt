1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.app.videofbdownloadfree"
4    android:versionCode="19"
5    android:versionName="4.5.1" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10    <!-- Make features optional to increase device compatibility -->
11    <uses-feature
11-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:113:5-85
12        android:name="android.hardware.camera"
12-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:113:19-57
13        android:required="false" />
13-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:113:58-82
14    <uses-feature
14-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:114:5-88
15        android:name="android.hardware.telephony"
15-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:114:19-60
16        android:required="false" />
16-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:114:61-85
17    <uses-feature
17-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:115:5-91
18        android:name="android.hardware.location.gps"
18-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:115:19-63
19        android:required="false" />
19-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:115:64-88
20    <uses-feature
20-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:116:5-99
21        android:name="android.hardware.sensor.accelerometer"
21-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:116:19-71
22        android:required="false" />
22-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:116:72-96
23
24    <!-- Support additional form factors -->
25    <uses-permission android:name="android.permission.INTERNET" />
25-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:119:5-67
25-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:119:22-64
26    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
26-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:120:5-79
26-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:120:22-76
27
28    <!-- Firebase Cloud Messaging permissions -->
29    <uses-permission android:name="android.permission.WAKE_LOCK" />
29-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:123:5-68
29-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:123:22-65
30    <uses-permission android:name="android.permission.VIBRATE" />
30-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:124:5-66
30-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:124:22-63
31
32    <!-- Notification permissions for Android 13+ -->
33    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
33-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:127:5-77
33-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:127:22-74
34    <uses-permission
34-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:128:5-129:38
35        android:name="android.permission.READ_EXTERNAL_STORAGE"
35-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:128:22-77
36        android:maxSdkVersion="32" />
36-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:129:9-35
37    <uses-permission
37-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:130:5-131:38
38        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
38-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:130:22-78
39        android:maxSdkVersion="28" />
39-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:131:9-35
40    <!-- For Android 13+ (API 33+) media permissions -->
41    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
41-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:133:5-76
41-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:133:22-73
42    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
42-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:134:5-75
42-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:134:22-72
43    <!-- For Android 14+ (API 34+) partial media access -->
44    <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED" />
44-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:136:5-90
44-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:136:22-87
45    <!-- Support Android TV -->
46    <uses-feature
46-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:138:5-87
47        android:name="android.software.leanback"
47-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:138:19-59
48        android:required="false" />
48-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:138:60-84
49    <!-- Support Android Auto / Automotive -->
50    <uses-feature
50-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:140:5-94
51        android:name="android.hardware.type.automotive"
51-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:140:19-66
52        android:required="false" />
52-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:140:67-91
53    <!-- Support Chromebook -->
54    <uses-feature
54-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:142:5-78
55        android:name="org.chromium.arc"
55-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:142:19-50
56        android:required="false" />
56-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:142:51-75
57    <uses-feature
57-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:143:5-145:36
58        android:name="android.hardware.touchscreen"
58-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:144:9-52
59        android:required="false" />
59-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:145:9-33
60
61    <queries>
61-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:11:5-13:15
62        <package android:name="com.facebook.katana" />
62-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:12:9-55
62-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:12:18-52
63        <!-- For browser content -->
64        <intent>
64-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:38:9-44:18
65            <action android:name="android.intent.action.VIEW" />
65-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:39:13-65
65-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:39:21-62
66
67            <category android:name="android.intent.category.BROWSABLE" />
67-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:41:13-74
67-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:41:23-71
68
69            <data android:scheme="https" />
69-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:43:13-44
69-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:43:19-41
70        </intent> <!-- End of browser content -->
71        <!-- For CustomTabsService -->
72        <intent>
72-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:47:9-49:18
73            <action android:name="android.support.customtabs.action.CustomTabsService" />
73-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:48:13-90
73-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:48:21-87
74        </intent> <!-- End of CustomTabsService -->
75        <!-- For MRAID capabilities -->
76        <intent>
76-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:52:9-56:18
77            <action android:name="android.intent.action.INSERT" />
77-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:53:13-67
77-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:53:21-64
78
79            <data android:mimeType="vnd.android.cursor.dir/event" />
79-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:43:13-44
80        </intent>
81        <intent>
81-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:57:9-61:18
82            <action android:name="android.intent.action.VIEW" />
82-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:39:13-65
82-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:39:21-62
83
84            <data android:scheme="sms" />
84-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:43:13-44
84-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:43:19-41
85        </intent>
86        <intent>
86-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:62:9-66:18
87            <action android:name="android.intent.action.DIAL" />
87-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:63:13-65
87-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:63:21-62
88
89            <data android:path="tel:" />
89-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:43:13-44
90        </intent>
91    </queries>
92
93    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
93-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:17:5-79
93-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:17:22-76
94    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
94-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:26:5-88
94-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:26:22-85
95    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
95-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:27:5-82
95-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:27:22-79
96    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" />
96-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:29:5-83
96-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:29:22-80
97    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" /> <!-- Required by older versions of Google Play services to create IID tokens -->
97-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:26:5-110
97-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:26:22-107
98    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
98-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:26:5-82
98-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:26:22-79
99    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
99-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
99-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
100
101    <permission
101-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d58a419118e34eadf0fdff30391c321\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
102        android:name="com.app.videofbdownloadfree.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
102-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d58a419118e34eadf0fdff30391c321\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
103        android:protectionLevel="signature" />
103-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d58a419118e34eadf0fdff30391c321\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
104
105    <uses-permission android:name="com.app.videofbdownloadfree.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
105-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d58a419118e34eadf0fdff30391c321\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
105-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d58a419118e34eadf0fdff30391c321\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
106
107    <application
107-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:6:5-111:19
108        android:name="com.app.videofbdownloadfree.MyApplication"
108-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:7:9-38
109        android:allowBackup="true"
109-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:8:9-35
110        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
110-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d58a419118e34eadf0fdff30391c321\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
111        android:dataExtractionRules="@xml/data_extraction_rules"
111-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:9:9-65
112        android:debuggable="true"
113        android:extractNativeLibs="false"
114        android:fullBackupContent="@xml/backup_rules"
114-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:10:9-54
115        android:icon="@drawable/icon"
115-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:11:9-38
116        android:label="@string/app_name"
116-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:12:9-41
117        android:preserveLegacyExternalStorage="true"
117-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:16:9-53
118        android:requestLegacyExternalStorage="true"
118-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:15:9-52
119        android:supportsRtl="true"
119-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:13:9-35
120        android:testOnly="true"
121        android:theme="@style/Theme.InstaLoader" >
121-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:14:9-49
122        <meta-data
122-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:18:9-20:47
123            android:name="com.google.android.gms.ads.APPLICATION_ID"
123-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:19:13-69
124            android:value="@string/admob_id" />
124-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:20:13-45
125
126        <!-- Facebook Audience Network for Bidding -->
127        <meta-data
127-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:23:9-25:55
128            android:name="com.facebook.sdk.ApplicationId"
128-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:24:13-58
129            android:value="@string/facebook_app_id" />
129-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:25:13-52
130        <meta-data
130-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:26:9-28:61
131            android:name="com.facebook.sdk.ClientToken"
131-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:27:13-56
132            android:value="@string/facebook_client_token" />
132-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:28:13-58
133
134        <!-- Fix for AD_SERVICES_CONFIG conflict -->
135        <property
135-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:31:9-34:48
136            android:name="android.adservices.AD_SERVICES_CONFIG"
136-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:32:13-65
137            android:resource="@xml/gma_ad_services_config" />
137-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:33:13-59
138
139        <!-- Splash Activity -->
140        <activity
140-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:37:9-44:20
141            android:name="com.app.videofbdownloadfree.SplashActivity"
141-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:38:13-43
142            android:exported="true" >
142-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:39:13-36
143            <intent-filter>
143-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:40:13-43:29
144                <action android:name="android.intent.action.MAIN" />
144-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:41:17-69
144-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:41:25-66
145
146                <category android:name="android.intent.category.LAUNCHER" />
146-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:42:17-77
146-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:42:27-74
147            </intent-filter>
148        </activity>
149        <!-- No Internet Activity -->
150        <activity
150-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:46:9-48:40
151            android:name="com.app.videofbdownloadfree.NoInternetActivity"
151-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:47:13-47
152            android:exported="false" />
152-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:48:13-37
153        <!-- Main Activity -->
154        <activity
154-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:50:9-53:70
155            android:name="com.app.videofbdownloadfree.MainActivity"
155-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:51:13-41
156            android:exported="false"
156-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:52:13-37
157            android:windowSoftInputMode="stateHidden|adjustResize" />
157-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:53:13-67
158
159        <!-- Recent Downloads Activity -->
160        <activity
160-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:56:9-58:40
161            android:name="com.app.videofbdownloadfree.RecentDownloadsActivity"
161-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:57:13-52
162            android:exported="false" />
162-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:58:13-37
163
164        <!-- Permissions Activity -->
165        <activity
165-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:61:9-64:56
166            android:name="com.app.videofbdownloadfree.PermissionsActivity"
166-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:62:13-48
167            android:exported="false"
167-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:63:13-37
168            android:theme="@style/Theme.InstaLoader" />
168-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:64:13-53
169
170        <!-- Ads Consent Activity -->
171        <activity
171-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:67:9-70:56
172            android:name="com.app.videofbdownloadfree.AdsConsentActivity"
172-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:68:13-47
173            android:exported="false"
173-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:69:13-37
174            android:theme="@style/Theme.InstaLoader" />
174-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:70:13-53
175        <activity android:name="com.app.videofbdownloadfree.SettingsActivity" />
175-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:72:9-54
175-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:72:19-51
176        <activity android:name="com.app.videofbdownloadfree.VideoPlayerActivity" />
176-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:73:9-57
176-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:73:19-54
177
178        <!-- File Provider for sharing files -->
179        <provider
180            android:name="androidx.core.content.FileProvider"
180-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:78:13-62
181            android:authorities="com.app.videofbdownloadfree.fileprovider"
181-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:79:13-64
182            android:exported="false"
182-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:80:13-37
183            android:grantUriPermissions="true" >
183-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:81:13-47
184            <meta-data
184-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:82:13-84:54
185                android:name="android.support.FILE_PROVIDER_PATHS"
185-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:83:17-67
186                android:resource="@xml/file_paths" />
186-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:84:17-51
187        </provider>
188
189        <!-- Firebase Cloud Messaging Service -->
190        <service
190-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:88:9-94:19
191            android:name="com.app.videofbdownloadfree.SaveItFirebaseMessagingService"
191-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:89:13-59
192            android:exported="false" >
192-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:90:13-37
193            <intent-filter>
193-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:91:13-93:29
194                <action android:name="com.google.firebase.MESSAGING_EVENT" />
194-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:92:17-78
194-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:92:25-75
195            </intent-filter>
196        </service>
197
198        <!-- Firebase Cloud Messaging default notification icon -->
199        <meta-data
199-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:97:9-99:49
200            android:name="com.google.firebase.messaging.default_notification_icon"
200-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:98:13-83
201            android:resource="@drawable/icon" />
201-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:99:13-46
202
203        <!-- Firebase Cloud Messaging default notification color -->
204        <meta-data
204-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:102:9-104:54
205            android:name="com.google.firebase.messaging.default_notification_color"
205-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:103:13-84
206            android:resource="@color/insta_purple" />
206-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:104:13-51
207
208        <!-- Firebase Cloud Messaging default notification channel -->
209        <meta-data
209-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:107:9-109:52
210            android:name="com.google.firebase.messaging.default_notification_channel_id"
210-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:108:13-89
211            android:value="saveit_notifications" />
211-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:109:13-49
212
213        <service
213-->[com.google.firebase:firebase-perf-ktx:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7e9769f3c4b2af8270ad0d6acca89ac\transformed\jetified-firebase-perf-ktx-21.0.2\AndroidManifest.xml:9:9-15:19
214            android:name="com.google.firebase.components.ComponentDiscoveryService"
214-->[com.google.firebase:firebase-perf-ktx:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7e9769f3c4b2af8270ad0d6acca89ac\transformed\jetified-firebase-perf-ktx-21.0.2\AndroidManifest.xml:10:13-84
215            android:directBootAware="true"
215-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
216            android:exported="false" >
216-->[com.google.firebase:firebase-perf-ktx:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7e9769f3c4b2af8270ad0d6acca89ac\transformed\jetified-firebase-perf-ktx-21.0.2\AndroidManifest.xml:11:13-37
217            <meta-data
217-->[com.google.firebase:firebase-perf-ktx:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7e9769f3c4b2af8270ad0d6acca89ac\transformed\jetified-firebase-perf-ktx-21.0.2\AndroidManifest.xml:12:13-14:85
218                android:name="com.google.firebase.components:com.google.firebase.perf.ktx.FirebasePerfLegacyRegistrar"
218-->[com.google.firebase:firebase-perf-ktx:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7e9769f3c4b2af8270ad0d6acca89ac\transformed\jetified-firebase-perf-ktx-21.0.2\AndroidManifest.xml:13:17-119
219                android:value="com.google.firebase.components.ComponentRegistrar" />
219-->[com.google.firebase:firebase-perf-ktx:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7e9769f3c4b2af8270ad0d6acca89ac\transformed\jetified-firebase-perf-ktx-21.0.2\AndroidManifest.xml:14:17-82
220            <meta-data
220-->[com.google.firebase:firebase-perf:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2329f23c4c3a4bf92b4344d445f02482\transformed\jetified-firebase-perf-21.0.2\AndroidManifest.xml:14:13-16:85
221                android:name="com.google.firebase.components:com.google.firebase.perf.FirebasePerfKtxRegistrar"
221-->[com.google.firebase:firebase-perf:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2329f23c4c3a4bf92b4344d445f02482\transformed\jetified-firebase-perf-21.0.2\AndroidManifest.xml:15:17-112
222                android:value="com.google.firebase.components.ComponentRegistrar" />
222-->[com.google.firebase:firebase-perf:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2329f23c4c3a4bf92b4344d445f02482\transformed\jetified-firebase-perf-21.0.2\AndroidManifest.xml:16:17-82
223            <meta-data
223-->[com.google.firebase:firebase-perf:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2329f23c4c3a4bf92b4344d445f02482\transformed\jetified-firebase-perf-21.0.2\AndroidManifest.xml:17:13-19:85
224                android:name="com.google.firebase.components:com.google.firebase.perf.FirebasePerfRegistrar"
224-->[com.google.firebase:firebase-perf:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2329f23c4c3a4bf92b4344d445f02482\transformed\jetified-firebase-perf-21.0.2\AndroidManifest.xml:18:17-109
225                android:value="com.google.firebase.components.ComponentRegistrar" />
225-->[com.google.firebase:firebase-perf:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2329f23c4c3a4bf92b4344d445f02482\transformed\jetified-firebase-perf-21.0.2\AndroidManifest.xml:19:17-82
226            <meta-data
226-->[com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec49ca28e4aa5b972b86f27aa77e6aa7\transformed\jetified-firebase-analytics-ktx-22.1.2\AndroidManifest.xml:11:13-13:85
227                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
227-->[com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec49ca28e4aa5b972b86f27aa77e6aa7\transformed\jetified-firebase-analytics-ktx-22.1.2\AndroidManifest.xml:12:17-129
228                android:value="com.google.firebase.components.ComponentRegistrar" />
228-->[com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec49ca28e4aa5b972b86f27aa77e6aa7\transformed\jetified-firebase-analytics-ktx-22.1.2\AndroidManifest.xml:13:17-82
229            <meta-data
229-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:37:13-39:85
230                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
230-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:38:17-139
231                android:value="com.google.firebase.components.ComponentRegistrar" />
231-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:39:17-82
232            <meta-data
232-->[com.google.firebase:firebase-config:22.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc2b22665e024b976aa2bd68e6e79f9\transformed\jetified-firebase-config-22.0.1\AndroidManifest.xml:29:13-31:85
233                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.FirebaseRemoteConfigKtxRegistrar"
233-->[com.google.firebase:firebase-config:22.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc2b22665e024b976aa2bd68e6e79f9\transformed\jetified-firebase-config-22.0.1\AndroidManifest.xml:30:17-128
234                android:value="com.google.firebase.components.ComponentRegistrar" />
234-->[com.google.firebase:firebase-config:22.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc2b22665e024b976aa2bd68e6e79f9\transformed\jetified-firebase-config-22.0.1\AndroidManifest.xml:31:17-82
235            <meta-data
235-->[com.google.firebase:firebase-config:22.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc2b22665e024b976aa2bd68e6e79f9\transformed\jetified-firebase-config-22.0.1\AndroidManifest.xml:32:13-34:85
236                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.RemoteConfigRegistrar"
236-->[com.google.firebase:firebase-config:22.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc2b22665e024b976aa2bd68e6e79f9\transformed\jetified-firebase-config-22.0.1\AndroidManifest.xml:33:17-117
237                android:value="com.google.firebase.components.ComponentRegistrar" />
237-->[com.google.firebase:firebase-config:22.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc2b22665e024b976aa2bd68e6e79f9\transformed\jetified-firebase-config-22.0.1\AndroidManifest.xml:34:17-82
238            <meta-data
238-->[com.google.firebase:firebase-appcheck-playintegrity:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430b373a0231a5ac5a64f84d80e35563\transformed\jetified-firebase-appcheck-playintegrity-18.0.0\AndroidManifest.xml:25:13-27:85
239                android:name="com.google.firebase.components:com.google.firebase.appcheck.playintegrity.FirebaseAppCheckPlayIntegrityRegistrar"
239-->[com.google.firebase:firebase-appcheck-playintegrity:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430b373a0231a5ac5a64f84d80e35563\transformed\jetified-firebase-appcheck-playintegrity-18.0.0\AndroidManifest.xml:26:17-144
240                android:value="com.google.firebase.components.ComponentRegistrar" />
240-->[com.google.firebase:firebase-appcheck-playintegrity:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430b373a0231a5ac5a64f84d80e35563\transformed\jetified-firebase-appcheck-playintegrity-18.0.0\AndroidManifest.xml:27:17-82
241            <meta-data
241-->[com.google.firebase:firebase-crashlytics-ktx:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2af3c9a2590e755876f4d3a775a327e7\transformed\jetified-firebase-crashlytics-ktx-19.2.1\AndroidManifest.xml:24:13-26:85
242                android:name="com.google.firebase.components:com.google.firebase.crashlytics.ktx.FirebaseCrashlyticsKtxRegistrar"
242-->[com.google.firebase:firebase-crashlytics-ktx:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2af3c9a2590e755876f4d3a775a327e7\transformed\jetified-firebase-crashlytics-ktx-19.2.1\AndroidManifest.xml:25:17-130
243                android:value="com.google.firebase.components.ComponentRegistrar" />
243-->[com.google.firebase:firebase-crashlytics-ktx:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2af3c9a2590e755876f4d3a775a327e7\transformed\jetified-firebase-crashlytics-ktx-19.2.1\AndroidManifest.xml:26:17-82
244            <meta-data
244-->[com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07a2722339bfa0219c25a815d830727d\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
245                android:name="com.google.firebase.components:com.google.firebase.database.ktx.FirebaseDatabaseLegacyRegistrar"
245-->[com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07a2722339bfa0219c25a815d830727d\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:13:17-127
246                android:value="com.google.firebase.components.ComponentRegistrar" />
246-->[com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07a2722339bfa0219c25a815d830727d\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:14:17-82
247            <meta-data
247-->[com.google.firebase:firebase-appcheck-ktx:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8aed30e267a3f7957cac475003383c4c\transformed\jetified-firebase-appcheck-ktx-18.0.0\AndroidManifest.xml:24:13-26:85
248                android:name="com.google.firebase.components:com.google.firebase.appcheck.ktx.FirebaseAppcheckLegacyRegistrar"
248-->[com.google.firebase:firebase-appcheck-ktx:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8aed30e267a3f7957cac475003383c4c\transformed\jetified-firebase-appcheck-ktx-18.0.0\AndroidManifest.xml:25:17-127
249                android:value="com.google.firebase.components.ComponentRegistrar" />
249-->[com.google.firebase:firebase-appcheck-ktx:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8aed30e267a3f7957cac475003383c4c\transformed\jetified-firebase-appcheck-ktx-18.0.0\AndroidManifest.xml:26:17-82
250            <meta-data
250-->[com.google.firebase:firebase-messaging-ktx:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7d62d36630b8c2669020ef927747254\transformed\jetified-firebase-messaging-ktx-24.1.0\AndroidManifest.xml:26:13-28:85
251                android:name="com.google.firebase.components:com.google.firebase.messaging.ktx.FirebaseMessagingLegacyRegistrar"
251-->[com.google.firebase:firebase-messaging-ktx:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7d62d36630b8c2669020ef927747254\transformed\jetified-firebase-messaging-ktx-24.1.0\AndroidManifest.xml:27:17-129
252                android:value="com.google.firebase.components.ComponentRegistrar" />
252-->[com.google.firebase:firebase-messaging-ktx:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7d62d36630b8c2669020ef927747254\transformed\jetified-firebase-messaging-ktx-24.1.0\AndroidManifest.xml:28:17-82
253            <meta-data
253-->[com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd549eab9cdb581d5cd730ab22aa1b6\transformed\jetified-firebase-crashlytics-19.2.1\AndroidManifest.xml:15:13-17:85
254                android:name="com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar"
254-->[com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd549eab9cdb581d5cd730ab22aa1b6\transformed\jetified-firebase-crashlytics-19.2.1\AndroidManifest.xml:16:17-126
255                android:value="com.google.firebase.components.ComponentRegistrar" />
255-->[com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd549eab9cdb581d5cd730ab22aa1b6\transformed\jetified-firebase-crashlytics-19.2.1\AndroidManifest.xml:17:17-82
256            <meta-data
256-->[com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd549eab9cdb581d5cd730ab22aa1b6\transformed\jetified-firebase-crashlytics-19.2.1\AndroidManifest.xml:18:13-20:85
257                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
257-->[com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd549eab9cdb581d5cd730ab22aa1b6\transformed\jetified-firebase-crashlytics-19.2.1\AndroidManifest.xml:19:17-115
258                android:value="com.google.firebase.components.ComponentRegistrar" />
258-->[com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd549eab9cdb581d5cd730ab22aa1b6\transformed\jetified-firebase-crashlytics-19.2.1\AndroidManifest.xml:20:17-82
259            <meta-data
259-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:57:13-59:85
260                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
260-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:58:17-122
261                android:value="com.google.firebase.components.ComponentRegistrar" />
261-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:59:17-82
262            <meta-data
262-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:60:13-62:85
263                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
263-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:61:17-119
264                android:value="com.google.firebase.components.ComponentRegistrar" />
264-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:62:17-82
265            <meta-data
265-->[com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34162c290514c368f494368d6102f2e8\transformed\jetified-firebase-sessions-2.0.6\AndroidManifest.xml:29:13-31:85
266                android:name="com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar"
266-->[com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34162c290514c368f494368d6102f2e8\transformed\jetified-firebase-sessions-2.0.6\AndroidManifest.xml:30:17-117
267                android:value="com.google.firebase.components.ComponentRegistrar" />
267-->[com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34162c290514c368f494368d6102f2e8\transformed\jetified-firebase-sessions-2.0.6\AndroidManifest.xml:31:17-82
268            <meta-data
268-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ce1bda87d7244e8b25ef2bc59b4ecef\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
269                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
269-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ce1bda87d7244e8b25ef2bc59b4ecef\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
270                android:value="com.google.firebase.components.ComponentRegistrar" />
270-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ce1bda87d7244e8b25ef2bc59b4ecef\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
271            <meta-data
271-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ce1bda87d7244e8b25ef2bc59b4ecef\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
272                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
272-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ce1bda87d7244e8b25ef2bc59b4ecef\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
273                android:value="com.google.firebase.components.ComponentRegistrar" />
273-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ce1bda87d7244e8b25ef2bc59b4ecef\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
274            <meta-data
274-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82e68bdc17783eab343147c24d5a4c00\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
275                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
275-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82e68bdc17783eab343147c24d5a4c00\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:30:17-120
276                android:value="com.google.firebase.components.ComponentRegistrar" />
276-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82e68bdc17783eab343147c24d5a4c00\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:31:17-82
277            <meta-data
277-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82e68bdc17783eab343147c24d5a4c00\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
278                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
278-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82e68bdc17783eab343147c24d5a4c00\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:33:17-109
279                android:value="com.google.firebase.components.ComponentRegistrar" />
279-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82e68bdc17783eab343147c24d5a4c00\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:34:17-82
280            <meta-data
280-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e0e1853c5f0749217e440541350556b\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
281                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
281-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e0e1853c5f0749217e440541350556b\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
282                android:value="com.google.firebase.components.ComponentRegistrar" />
282-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e0e1853c5f0749217e440541350556b\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
283            <meta-data
283-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e0e1853c5f0749217e440541350556b\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
284                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
284-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e0e1853c5f0749217e440541350556b\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
285                android:value="com.google.firebase.components.ComponentRegistrar" />
285-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e0e1853c5f0749217e440541350556b\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
286            <meta-data
286-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25a61a6d44aa83fddee0cb806da6dac6\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
287                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
287-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25a61a6d44aa83fddee0cb806da6dac6\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
288                android:value="com.google.firebase.components.ComponentRegistrar" />
288-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25a61a6d44aa83fddee0cb806da6dac6\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
289            <meta-data
289-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
290                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
290-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
291                android:value="com.google.firebase.components.ComponentRegistrar" />
291-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
292            <meta-data
292-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c9f1b0cfb3dce6ae7cf4b0f010a4436\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:12:13-14:85
293                android:name="com.google.firebase.components:com.google.firebase.abt.component.AbtRegistrar"
293-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c9f1b0cfb3dce6ae7cf4b0f010a4436\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:13:17-109
294                android:value="com.google.firebase.components.ComponentRegistrar" />
294-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c9f1b0cfb3dce6ae7cf4b0f010a4436\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:14:17-82
295            <meta-data
295-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf01dc2d83636955506f1575030bb524\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:25:13-27:85
296                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
296-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf01dc2d83636955506f1575030bb524\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:26:17-115
297                android:value="com.google.firebase.components.ComponentRegistrar" />
297-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf01dc2d83636955506f1575030bb524\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:27:17-82
298        </service>
299
300        <activity
300-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:20:9-24:75
301            android:name="com.facebook.ads.AudienceNetworkActivity"
301-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:21:13-68
302            android:configChanges="keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
302-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:22:13-106
303            android:exported="false"
303-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:23:13-37
304            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
304-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:24:13-72
305
306        <provider
306-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:26:9-29:40
307            android:name="com.facebook.ads.AudienceNetworkContentProvider"
307-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:27:13-75
308            android:authorities="com.app.videofbdownloadfree.AudienceNetworkContentProvider"
308-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:28:13-82
309            android:exported="false" /> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
309-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:29:13-37
310        <activity
310-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:73:9-78:43
311            android:name="com.google.android.gms.ads.AdActivity"
311-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:74:13-65
312            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
312-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:75:13-122
313            android:exported="false"
313-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:76:13-37
314            android:theme="@android:style/Theme.Translucent" />
314-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:77:13-61
315
316        <provider
316-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:80:9-85:43
317            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
317-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:81:13-76
318            android:authorities="com.app.videofbdownloadfree.mobileadsinitprovider"
318-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:82:13-73
319            android:exported="false"
319-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:83:13-37
320            android:initOrder="100" />
320-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:84:13-36
321
322        <service
322-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:87:9-91:43
323            android:name="com.google.android.gms.ads.AdService"
323-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:88:13-64
324            android:enabled="true"
324-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:89:13-35
325            android:exported="false" />
325-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:90:13-37
326
327        <activity
327-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:93:9-97:43
328            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
328-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:94:13-82
329            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
329-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:95:13-122
330            android:exported="false" />
330-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:96:13-37
331        <activity
331-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:98:9-105:43
332            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
332-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:99:13-82
333            android:excludeFromRecents="true"
333-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:100:13-46
334            android:exported="false"
334-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:101:13-37
335            android:launchMode="singleTask"
335-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:102:13-44
336            android:taskAffinity=""
336-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:103:13-36
337            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
337-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:104:13-72
338
339        <receiver
339-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:29:9-33:20
340            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
340-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:30:13-85
341            android:enabled="true"
341-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:31:13-35
342            android:exported="false" >
342-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:32:13-37
343        </receiver>
344
345        <service
345-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:35:9-38:40
346            android:name="com.google.android.gms.measurement.AppMeasurementService"
346-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:36:13-84
347            android:enabled="true"
347-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:37:13-35
348            android:exported="false" />
348-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:38:13-37
349        <service
349-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:39:9-43:72
350            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
350-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:40:13-87
351            android:enabled="true"
351-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:41:13-35
352            android:exported="false"
352-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:42:13-37
353            android:permission="android.permission.BIND_JOB_SERVICE" />
353-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:43:13-69
354
355        <receiver
355-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:29:9-40:20
356            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
356-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:30:13-78
357            android:exported="true"
357-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:31:13-36
358            android:permission="com.google.android.c2dm.permission.SEND" >
358-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:32:13-73
359            <intent-filter>
359-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:33:13-35:29
360                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
360-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:34:17-81
360-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:34:25-78
361            </intent-filter>
362
363            <meta-data
363-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:37:13-39:40
364                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
364-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:38:17-92
365                android:value="true" />
365-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:39:17-37
366        </receiver>
367        <!--
368             FirebaseMessagingService performs security checks at runtime,
369             but set to not exported to explicitly avoid allowing another app to call it.
370        -->
371        <service
371-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:46:9-53:19
372            android:name="com.google.firebase.messaging.FirebaseMessagingService"
372-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:47:13-82
373            android:directBootAware="true"
373-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:48:13-43
374            android:exported="false" >
374-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:49:13-37
375            <intent-filter android:priority="-500" >
375-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:91:13-93:29
376                <action android:name="com.google.firebase.MESSAGING_EVENT" />
376-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:92:17-78
376-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:92:25-75
377            </intent-filter>
378        </service>
379        <service
379-->[com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34162c290514c368f494368d6102f2e8\transformed\jetified-firebase-sessions-2.0.6\AndroidManifest.xml:22:9-25:40
380            android:name="com.google.firebase.sessions.SessionLifecycleService"
380-->[com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34162c290514c368f494368d6102f2e8\transformed\jetified-firebase-sessions-2.0.6\AndroidManifest.xml:23:13-80
381            android:enabled="true"
381-->[com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34162c290514c368f494368d6102f2e8\transformed\jetified-firebase-sessions-2.0.6\AndroidManifest.xml:24:13-35
382            android:exported="false" />
382-->[com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34162c290514c368f494368d6102f2e8\transformed\jetified-firebase-sessions-2.0.6\AndroidManifest.xml:25:13-37
383
384        <provider
384-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
385            android:name="com.google.firebase.provider.FirebaseInitProvider"
385-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
386            android:authorities="com.app.videofbdownloadfree.firebaseinitprovider"
386-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
387            android:directBootAware="true"
387-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
388            android:exported="false"
388-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
389            android:initOrder="100" />
389-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
390        <provider
390-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\071658833db178a8621052ffa0849746\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
391            android:name="androidx.startup.InitializationProvider"
391-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\071658833db178a8621052ffa0849746\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
392            android:authorities="com.app.videofbdownloadfree.androidx-startup"
392-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\071658833db178a8621052ffa0849746\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
393            android:exported="false" >
393-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\071658833db178a8621052ffa0849746\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
394            <meta-data
394-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\071658833db178a8621052ffa0849746\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
395                android:name="androidx.emoji2.text.EmojiCompatInitializer"
395-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\071658833db178a8621052ffa0849746\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
396                android:value="androidx.startup" />
396-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\071658833db178a8621052ffa0849746\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
397            <meta-data
397-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
398                android:name="androidx.work.WorkManagerInitializer"
398-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
399                android:value="androidx.startup" />
399-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
400            <meta-data
400-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd4dde74fe4031e7f9743f34c05041e2\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
401                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
401-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd4dde74fe4031e7f9743f34c05041e2\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
402                android:value="androidx.startup" />
402-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd4dde74fe4031e7f9743f34c05041e2\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
403            <meta-data
403-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
404                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
404-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
405                android:value="androidx.startup" />
405-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
406        </provider>
407
408        <service
408-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
409            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
409-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
410            android:directBootAware="false"
410-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
411            android:enabled="@bool/enable_system_alarm_service_default"
411-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
412            android:exported="false" />
412-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
413        <service
413-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
414            android:name="androidx.work.impl.background.systemjob.SystemJobService"
414-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
415            android:directBootAware="false"
415-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
416            android:enabled="@bool/enable_system_job_service_default"
416-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
417            android:exported="true"
417-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
418            android:permission="android.permission.BIND_JOB_SERVICE" />
418-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
419        <service
419-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
420            android:name="androidx.work.impl.foreground.SystemForegroundService"
420-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
421            android:directBootAware="false"
421-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
422            android:enabled="@bool/enable_system_foreground_service_default"
422-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
423            android:exported="false" />
423-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
424
425        <receiver
425-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
426            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
426-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
427            android:directBootAware="false"
427-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
428            android:enabled="true"
428-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
429            android:exported="false" />
429-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
430        <receiver
430-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
431            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
431-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
432            android:directBootAware="false"
432-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
433            android:enabled="false"
433-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
434            android:exported="false" >
434-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
435            <intent-filter>
435-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
436                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
436-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
436-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
437                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
437-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
437-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
438            </intent-filter>
439        </receiver>
440        <receiver
440-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
441            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
441-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
442            android:directBootAware="false"
442-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
443            android:enabled="false"
443-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
444            android:exported="false" >
444-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
445            <intent-filter>
445-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
446                <action android:name="android.intent.action.BATTERY_OKAY" />
446-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
446-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
447                <action android:name="android.intent.action.BATTERY_LOW" />
447-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
447-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
448            </intent-filter>
449        </receiver>
450        <receiver
450-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
451            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
451-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
452            android:directBootAware="false"
452-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
453            android:enabled="false"
453-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
454            android:exported="false" >
454-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
455            <intent-filter>
455-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
456                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
456-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
456-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
457                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
457-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
457-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
458            </intent-filter>
459        </receiver>
460        <receiver
460-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
461            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
461-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
462            android:directBootAware="false"
462-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
463            android:enabled="false"
463-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
464            android:exported="false" >
464-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
465            <intent-filter>
465-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
466                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
466-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
466-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
467            </intent-filter>
468        </receiver>
469        <receiver
469-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
470            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
470-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
471            android:directBootAware="false"
471-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
472            android:enabled="false"
472-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
473            android:exported="false" >
473-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
474            <intent-filter>
474-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
475                <action android:name="android.intent.action.BOOT_COMPLETED" />
475-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:17-79
475-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:25-76
476                <action android:name="android.intent.action.TIME_SET" />
476-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
476-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
477                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
477-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
477-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
478            </intent-filter>
479        </receiver>
480        <receiver
480-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
481            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
481-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
482            android:directBootAware="false"
482-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
483            android:enabled="@bool/enable_system_alarm_service_default"
483-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
484            android:exported="false" >
484-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
485            <intent-filter>
485-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
486                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
486-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
486-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
487            </intent-filter>
488        </receiver>
489        <receiver
489-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
490            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
490-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
491            android:directBootAware="false"
491-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
492            android:enabled="true"
492-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
493            android:exported="true"
493-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
494            android:permission="android.permission.DUMP" >
494-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
495            <intent-filter>
495-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
496                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
496-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
496-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
497            </intent-filter>
498        </receiver>
499
500        <uses-library
500-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23c7b3148a83432ca440c27e9e880ef9\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
501            android:name="androidx.window.extensions"
501-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23c7b3148a83432ca440c27e9e880ef9\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
502            android:required="false" />
502-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23c7b3148a83432ca440c27e9e880ef9\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
503        <uses-library
503-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23c7b3148a83432ca440c27e9e880ef9\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
504            android:name="androidx.window.sidecar"
504-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23c7b3148a83432ca440c27e9e880ef9\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
505            android:required="false" />
505-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23c7b3148a83432ca440c27e9e880ef9\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
506        <uses-library
506-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\903e092fc049e07eb9802b2b1d837d55\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
507            android:name="android.ext.adservices"
507-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\903e092fc049e07eb9802b2b1d837d55\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
508            android:required="false" />
508-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\903e092fc049e07eb9802b2b1d837d55\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
509
510        <activity
510-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64d1cc8f3e9c59c96e0297d996aad4c8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
511            android:name="com.google.android.gms.common.api.GoogleApiActivity"
511-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64d1cc8f3e9c59c96e0297d996aad4c8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
512            android:exported="false"
512-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64d1cc8f3e9c59c96e0297d996aad4c8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
513            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
513-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64d1cc8f3e9c59c96e0297d996aad4c8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
514
515        <meta-data
515-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c96b0d8b72c615a79b6ffc473f6a8013\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
516            android:name="com.google.android.gms.version"
516-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c96b0d8b72c615a79b6ffc473f6a8013\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
517            android:value="@integer/google_play_services_version" />
517-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c96b0d8b72c615a79b6ffc473f6a8013\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
518
519        <service
519-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85713e3337aa4c8e7156fef507f40140\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:26:9-32:19
520            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
520-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85713e3337aa4c8e7156fef507f40140\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:27:13-103
521            android:exported="false" >
521-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85713e3337aa4c8e7156fef507f40140\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:28:13-37
522            <meta-data
522-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85713e3337aa4c8e7156fef507f40140\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:29:13-31:39
523                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
523-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85713e3337aa4c8e7156fef507f40140\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:30:17-94
524                android:value="cct" />
524-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85713e3337aa4c8e7156fef507f40140\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:31:17-36
525        </service>
526
527        <receiver
527-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
528            android:name="androidx.profileinstaller.ProfileInstallReceiver"
528-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
529            android:directBootAware="false"
529-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
530            android:enabled="true"
530-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
531            android:exported="true"
531-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
532            android:permission="android.permission.DUMP" >
532-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
533            <intent-filter>
533-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
534                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
534-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
534-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
535            </intent-filter>
536            <intent-filter>
536-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
537                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
537-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
537-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
538            </intent-filter>
539            <intent-filter>
539-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
540                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
540-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
540-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
541            </intent-filter>
542            <intent-filter>
542-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
543                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
543-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
543-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
544            </intent-filter>
545        </receiver>
546
547        <service
547-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdc16e1b8883ecf192e9948d1c13071a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:24:9-28:19
548            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
548-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdc16e1b8883ecf192e9948d1c13071a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:25:13-117
549            android:exported="false"
549-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdc16e1b8883ecf192e9948d1c13071a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:26:13-37
550            android:permission="android.permission.BIND_JOB_SERVICE" >
550-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdc16e1b8883ecf192e9948d1c13071a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:27:13-69
551        </service>
552
553        <receiver
553-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdc16e1b8883ecf192e9948d1c13071a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:30:9-32:40
554            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
554-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdc16e1b8883ecf192e9948d1c13071a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:31:13-132
555            android:exported="false" />
555-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdc16e1b8883ecf192e9948d1c13071a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:32:13-37
556
557        <service
557-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23645d451edec15b499b2c0a1186f68f\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
558            android:name="androidx.room.MultiInstanceInvalidationService"
558-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23645d451edec15b499b2c0a1186f68f\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
559            android:directBootAware="true"
559-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23645d451edec15b499b2c0a1186f68f\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
560            android:exported="false" />
560-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23645d451edec15b499b2c0a1186f68f\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
561    </application>
562
563</manifest>
