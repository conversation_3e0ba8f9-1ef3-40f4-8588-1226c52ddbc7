1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.app.videofbdownloadfree"
4    android:versionCode="19"
5    android:versionName="4.5.1" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10    <!-- Make features optional to increase device compatibility -->
11    <uses-feature
11-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:89:5-85
12        android:name="android.hardware.camera"
12-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:89:19-57
13        android:required="false" />
13-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:89:58-82
14    <uses-feature
14-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:90:5-88
15        android:name="android.hardware.telephony"
15-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:90:19-60
16        android:required="false" />
16-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:90:61-85
17    <uses-feature
17-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:91:5-91
18        android:name="android.hardware.location.gps"
18-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:91:19-63
19        android:required="false" />
19-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:91:64-88
20    <uses-feature
20-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:92:5-99
21        android:name="android.hardware.sensor.accelerometer"
21-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:92:19-71
22        android:required="false" />
22-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:92:72-96
23
24    <!-- Support additional form factors -->
25    <uses-permission android:name="android.permission.INTERNET" />
25-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:95:5-67
25-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:95:22-64
26    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
26-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:96:5-79
26-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:96:22-76
27    <uses-permission
27-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:97:5-98:38
28        android:name="android.permission.READ_EXTERNAL_STORAGE"
28-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:97:22-77
29        android:maxSdkVersion="32" />
29-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:98:9-35
30    <uses-permission
30-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:99:5-100:38
31        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
31-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:99:22-78
32        android:maxSdkVersion="28" />
32-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:100:9-35
33    <!-- For Android 11+ manage all files permission -->
34    <uses-permission
34-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:97:5-98:38
35        android:name="android.permission.READ_EXTERNAL_STORAGE"
35-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:97:22-77
36        android:maxSdkVersion="32" />
36-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:98:9-35
37    <uses-permission
37-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:99:5-100:38
38        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
38-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:99:22-78
39        android:maxSdkVersion="28" />
39-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:100:9-35
40    <!-- For Android 13+ (API 33+) media permissions -->
41    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
41-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:107:5-76
41-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:107:22-73
42    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
42-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:108:5-75
42-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:108:22-72
43    <!-- For Android 14+ (API 34+) partial media access -->
44    <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED" />
44-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:110:5-90
44-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:110:22-87
45
46    <uses-feature
46-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:89:5-85
47        android:name="android.hardware.camera"
47-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:89:19-57
48        android:required="false" />
48-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:89:58-82
49    <uses-feature
49-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:90:5-88
50        android:name="android.hardware.telephony"
50-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:90:19-60
51        android:required="false" />
51-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:90:61-85
52    <uses-feature
52-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:91:5-91
53        android:name="android.hardware.location.gps"
53-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:91:19-63
54        android:required="false" />
54-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:91:64-88
55    <uses-feature
55-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:92:5-99
56        android:name="android.hardware.sensor.accelerometer"
56-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:92:19-71
57        android:required="false" />
57-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:92:72-96
58    <!-- Support Android TV -->
59    <uses-feature
59-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:116:5-87
60        android:name="android.software.leanback"
60-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:116:19-59
61        android:required="false" />
61-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:116:60-84
62    <!-- Support Android Auto / Automotive -->
63    <uses-feature
63-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:118:5-94
64        android:name="android.hardware.type.automotive"
64-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:118:19-66
65        android:required="false" />
65-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:118:67-91
66    <!-- Support Chromebook -->
67    <uses-feature
67-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:120:5-78
68        android:name="org.chromium.arc"
68-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:120:19-50
69        android:required="false" />
69-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:120:51-75
70    <uses-feature
70-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:121:5-123:36
71        android:name="android.hardware.touchscreen"
71-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:122:9-52
72        android:required="false" />
72-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:123:9-33
73
74    <queries>
74-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:11:5-13:15
75        <package android:name="com.facebook.katana" />
75-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:12:9-55
75-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:12:18-52
76        <!-- For browser content -->
77        <intent>
77-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:38:9-44:18
78            <action android:name="android.intent.action.VIEW" />
78-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:39:13-65
78-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:39:21-62
79
80            <category android:name="android.intent.category.BROWSABLE" />
80-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:41:13-74
80-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:41:23-71
81
82            <data android:scheme="https" />
82-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:43:13-44
82-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:43:19-41
83        </intent> <!-- End of browser content -->
84        <!-- For CustomTabsService -->
85        <intent>
85-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:47:9-49:18
86            <action android:name="android.support.customtabs.action.CustomTabsService" />
86-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:48:13-90
86-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:48:21-87
87        </intent> <!-- End of CustomTabsService -->
88        <!-- For MRAID capabilities -->
89        <intent>
89-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:52:9-56:18
90            <action android:name="android.intent.action.INSERT" />
90-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:53:13-67
90-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:53:21-64
91
92            <data android:mimeType="vnd.android.cursor.dir/event" />
92-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:43:13-44
93        </intent>
94        <intent>
94-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:57:9-61:18
95            <action android:name="android.intent.action.VIEW" />
95-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:39:13-65
95-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:39:21-62
96
97            <data android:scheme="sms" />
97-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:43:13-44
97-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:43:19-41
98        </intent>
99        <intent>
99-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:62:9-66:18
100            <action android:name="android.intent.action.DIAL" />
100-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:63:13-65
100-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:63:21-62
101
102            <data android:path="tel:" />
102-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:43:13-44
103        </intent>
104    </queries>
105
106    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
106-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:17:5-79
106-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:17:22-76
107    <uses-permission android:name="android.permission.WAKE_LOCK" />
107-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:24:5-68
107-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:24:22-65
108    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
108-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:26:5-88
108-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:26:22-85
109    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
109-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:27:5-82
109-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:27:22-79
110    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" />
110-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:29:5-83
110-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:29:22-80
111    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
111-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:26:5-110
111-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:26:22-107
112    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
112-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
112-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
113
114    <permission
114-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d58a419118e34eadf0fdff30391c321\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
115        android:name="com.app.videofbdownloadfree.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
115-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d58a419118e34eadf0fdff30391c321\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
116        android:protectionLevel="signature" />
116-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d58a419118e34eadf0fdff30391c321\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
117
118    <uses-permission android:name="com.app.videofbdownloadfree.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
118-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d58a419118e34eadf0fdff30391c321\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
118-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d58a419118e34eadf0fdff30391c321\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
119
120    <application
120-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:6:5-87:19
121        android:name="com.app.videofbdownloadfree.MyApplication"
121-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:7:9-38
122        android:allowBackup="true"
122-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:8:9-35
123        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
123-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d58a419118e34eadf0fdff30391c321\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
124        android:dataExtractionRules="@xml/data_extraction_rules"
124-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:9:9-65
125        android:debuggable="true"
126        android:extractNativeLibs="false"
127        android:fullBackupContent="@xml/backup_rules"
127-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:10:9-54
128        android:icon="@drawable/icon"
128-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:11:9-38
129        android:label="@string/app_name"
129-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:12:9-41
130        android:preserveLegacyExternalStorage="true"
130-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:16:9-53
131        android:requestLegacyExternalStorage="true"
131-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:15:9-52
132        android:supportsRtl="true"
132-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:13:9-35
133        android:testOnly="true"
134        android:theme="@style/Theme.InstaLoader" >
134-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:14:9-49
135        <meta-data
135-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:18:9-20:47
136            android:name="com.google.android.gms.ads.APPLICATION_ID"
136-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:19:13-69
137            android:value="@string/admob_id" />
137-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:20:13-45
138
139        <!-- Facebook Audience Network for Bidding -->
140        <meta-data
140-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:23:9-25:55
141            android:name="com.facebook.sdk.ApplicationId"
141-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:24:13-58
142            android:value="@string/facebook_app_id" />
142-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:25:13-52
143        <meta-data
143-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:26:9-28:61
144            android:name="com.facebook.sdk.ClientToken"
144-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:27:13-56
145            android:value="@string/facebook_client_token" />
145-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:28:13-58
146
147        <!-- Fix for AD_SERVICES_CONFIG conflict -->
148        <property
148-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:31:9-34:48
149            android:name="android.adservices.AD_SERVICES_CONFIG"
149-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:32:13-65
150            android:resource="@xml/gma_ad_services_config" />
150-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:33:13-59
151
152        <!-- Splash Activity -->
153        <activity
153-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:37:9-44:20
154            android:name="com.app.videofbdownloadfree.SplashActivity"
154-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:38:13-43
155            android:exported="true" >
155-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:39:13-36
156            <intent-filter>
156-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:40:13-43:29
157                <action android:name="android.intent.action.MAIN" />
157-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:41:17-69
157-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:41:25-66
158
159                <category android:name="android.intent.category.LAUNCHER" />
159-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:42:17-77
159-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:42:27-74
160            </intent-filter>
161        </activity>
162        <!-- No Internet Activity -->
163        <activity
163-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:46:9-48:40
164            android:name="com.app.videofbdownloadfree.NoInternetActivity"
164-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:47:13-47
165            android:exported="false" />
165-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:48:13-37
166        <!-- Main Activity -->
167        <activity
167-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:50:9-53:70
168            android:name="com.app.videofbdownloadfree.MainActivity"
168-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:51:13-41
169            android:exported="false"
169-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:52:13-37
170            android:windowSoftInputMode="stateHidden|adjustResize" />
170-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:53:13-67
171
172        <!-- Recent Downloads Activity -->
173        <activity
173-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:56:9-58:40
174            android:name="com.app.videofbdownloadfree.RecentDownloadsActivity"
174-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:57:13-52
175            android:exported="false" />
175-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:58:13-37
176
177        <!-- Permissions Activity -->
178        <activity
178-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:61:9-64:56
179            android:name="com.app.videofbdownloadfree.PermissionsActivity"
179-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:62:13-48
180            android:exported="false"
180-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:63:13-37
181            android:theme="@style/Theme.InstaLoader" />
181-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:64:13-53
182
183        <!-- Ads Consent Activity -->
184        <activity
184-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:67:9-70:56
185            android:name="com.app.videofbdownloadfree.AdsConsentActivity"
185-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:68:13-47
186            android:exported="false"
186-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:69:13-37
187            android:theme="@style/Theme.InstaLoader" />
187-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:70:13-53
188        <activity android:name="com.app.videofbdownloadfree.SettingsActivity" />
188-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:72:9-54
188-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:72:19-51
189        <activity android:name="com.app.videofbdownloadfree.VideoPlayerActivity" />
189-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:73:9-57
189-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:73:19-54
190
191        <!-- File Provider for sharing files -->
192        <provider
193            android:name="androidx.core.content.FileProvider"
193-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:78:13-62
194            android:authorities="com.app.videofbdownloadfree.fileprovider"
194-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:79:13-64
195            android:exported="false"
195-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:80:13-37
196            android:grantUriPermissions="true" >
196-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:81:13-47
197            <meta-data
197-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:82:13-84:54
198                android:name="android.support.FILE_PROVIDER_PATHS"
198-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:83:17-67
199                android:resource="@xml/file_paths" />
199-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:84:17-51
200        </provider>
201
202        <service
202-->[com.google.firebase:firebase-perf-ktx:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7e9769f3c4b2af8270ad0d6acca89ac\transformed\jetified-firebase-perf-ktx-21.0.2\AndroidManifest.xml:9:9-15:19
203            android:name="com.google.firebase.components.ComponentDiscoveryService"
203-->[com.google.firebase:firebase-perf-ktx:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7e9769f3c4b2af8270ad0d6acca89ac\transformed\jetified-firebase-perf-ktx-21.0.2\AndroidManifest.xml:10:13-84
204            android:directBootAware="true"
204-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
205            android:exported="false" >
205-->[com.google.firebase:firebase-perf-ktx:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7e9769f3c4b2af8270ad0d6acca89ac\transformed\jetified-firebase-perf-ktx-21.0.2\AndroidManifest.xml:11:13-37
206            <meta-data
206-->[com.google.firebase:firebase-perf-ktx:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7e9769f3c4b2af8270ad0d6acca89ac\transformed\jetified-firebase-perf-ktx-21.0.2\AndroidManifest.xml:12:13-14:85
207                android:name="com.google.firebase.components:com.google.firebase.perf.ktx.FirebasePerfLegacyRegistrar"
207-->[com.google.firebase:firebase-perf-ktx:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7e9769f3c4b2af8270ad0d6acca89ac\transformed\jetified-firebase-perf-ktx-21.0.2\AndroidManifest.xml:13:17-119
208                android:value="com.google.firebase.components.ComponentRegistrar" />
208-->[com.google.firebase:firebase-perf-ktx:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7e9769f3c4b2af8270ad0d6acca89ac\transformed\jetified-firebase-perf-ktx-21.0.2\AndroidManifest.xml:14:17-82
209            <meta-data
209-->[com.google.firebase:firebase-perf:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2329f23c4c3a4bf92b4344d445f02482\transformed\jetified-firebase-perf-21.0.2\AndroidManifest.xml:14:13-16:85
210                android:name="com.google.firebase.components:com.google.firebase.perf.FirebasePerfKtxRegistrar"
210-->[com.google.firebase:firebase-perf:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2329f23c4c3a4bf92b4344d445f02482\transformed\jetified-firebase-perf-21.0.2\AndroidManifest.xml:15:17-112
211                android:value="com.google.firebase.components.ComponentRegistrar" />
211-->[com.google.firebase:firebase-perf:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2329f23c4c3a4bf92b4344d445f02482\transformed\jetified-firebase-perf-21.0.2\AndroidManifest.xml:16:17-82
212            <meta-data
212-->[com.google.firebase:firebase-perf:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2329f23c4c3a4bf92b4344d445f02482\transformed\jetified-firebase-perf-21.0.2\AndroidManifest.xml:17:13-19:85
213                android:name="com.google.firebase.components:com.google.firebase.perf.FirebasePerfRegistrar"
213-->[com.google.firebase:firebase-perf:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2329f23c4c3a4bf92b4344d445f02482\transformed\jetified-firebase-perf-21.0.2\AndroidManifest.xml:18:17-109
214                android:value="com.google.firebase.components.ComponentRegistrar" />
214-->[com.google.firebase:firebase-perf:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2329f23c4c3a4bf92b4344d445f02482\transformed\jetified-firebase-perf-21.0.2\AndroidManifest.xml:19:17-82
215            <meta-data
215-->[com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec49ca28e4aa5b972b86f27aa77e6aa7\transformed\jetified-firebase-analytics-ktx-22.1.2\AndroidManifest.xml:11:13-13:85
216                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
216-->[com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec49ca28e4aa5b972b86f27aa77e6aa7\transformed\jetified-firebase-analytics-ktx-22.1.2\AndroidManifest.xml:12:17-129
217                android:value="com.google.firebase.components.ComponentRegistrar" />
217-->[com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec49ca28e4aa5b972b86f27aa77e6aa7\transformed\jetified-firebase-analytics-ktx-22.1.2\AndroidManifest.xml:13:17-82
218            <meta-data
218-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:37:13-39:85
219                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
219-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:38:17-139
220                android:value="com.google.firebase.components.ComponentRegistrar" />
220-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:39:17-82
221            <meta-data
221-->[com.google.firebase:firebase-config:22.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc2b22665e024b976aa2bd68e6e79f9\transformed\jetified-firebase-config-22.0.1\AndroidManifest.xml:29:13-31:85
222                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.FirebaseRemoteConfigKtxRegistrar"
222-->[com.google.firebase:firebase-config:22.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc2b22665e024b976aa2bd68e6e79f9\transformed\jetified-firebase-config-22.0.1\AndroidManifest.xml:30:17-128
223                android:value="com.google.firebase.components.ComponentRegistrar" />
223-->[com.google.firebase:firebase-config:22.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc2b22665e024b976aa2bd68e6e79f9\transformed\jetified-firebase-config-22.0.1\AndroidManifest.xml:31:17-82
224            <meta-data
224-->[com.google.firebase:firebase-config:22.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc2b22665e024b976aa2bd68e6e79f9\transformed\jetified-firebase-config-22.0.1\AndroidManifest.xml:32:13-34:85
225                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.RemoteConfigRegistrar"
225-->[com.google.firebase:firebase-config:22.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc2b22665e024b976aa2bd68e6e79f9\transformed\jetified-firebase-config-22.0.1\AndroidManifest.xml:33:17-117
226                android:value="com.google.firebase.components.ComponentRegistrar" />
226-->[com.google.firebase:firebase-config:22.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc2b22665e024b976aa2bd68e6e79f9\transformed\jetified-firebase-config-22.0.1\AndroidManifest.xml:34:17-82
227            <meta-data
227-->[com.google.firebase:firebase-crashlytics-ktx:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2af3c9a2590e755876f4d3a775a327e7\transformed\jetified-firebase-crashlytics-ktx-19.2.1\AndroidManifest.xml:24:13-26:85
228                android:name="com.google.firebase.components:com.google.firebase.crashlytics.ktx.FirebaseCrashlyticsKtxRegistrar"
228-->[com.google.firebase:firebase-crashlytics-ktx:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2af3c9a2590e755876f4d3a775a327e7\transformed\jetified-firebase-crashlytics-ktx-19.2.1\AndroidManifest.xml:25:17-130
229                android:value="com.google.firebase.components.ComponentRegistrar" />
229-->[com.google.firebase:firebase-crashlytics-ktx:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2af3c9a2590e755876f4d3a775a327e7\transformed\jetified-firebase-crashlytics-ktx-19.2.1\AndroidManifest.xml:26:17-82
230            <meta-data
230-->[com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07a2722339bfa0219c25a815d830727d\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
231                android:name="com.google.firebase.components:com.google.firebase.database.ktx.FirebaseDatabaseLegacyRegistrar"
231-->[com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07a2722339bfa0219c25a815d830727d\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:13:17-127
232                android:value="com.google.firebase.components.ComponentRegistrar" />
232-->[com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07a2722339bfa0219c25a815d830727d\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:14:17-82
233            <meta-data
233-->[com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd549eab9cdb581d5cd730ab22aa1b6\transformed\jetified-firebase-crashlytics-19.2.1\AndroidManifest.xml:15:13-17:85
234                android:name="com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar"
234-->[com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd549eab9cdb581d5cd730ab22aa1b6\transformed\jetified-firebase-crashlytics-19.2.1\AndroidManifest.xml:16:17-126
235                android:value="com.google.firebase.components.ComponentRegistrar" />
235-->[com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd549eab9cdb581d5cd730ab22aa1b6\transformed\jetified-firebase-crashlytics-19.2.1\AndroidManifest.xml:17:17-82
236            <meta-data
236-->[com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd549eab9cdb581d5cd730ab22aa1b6\transformed\jetified-firebase-crashlytics-19.2.1\AndroidManifest.xml:18:13-20:85
237                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
237-->[com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd549eab9cdb581d5cd730ab22aa1b6\transformed\jetified-firebase-crashlytics-19.2.1\AndroidManifest.xml:19:17-115
238                android:value="com.google.firebase.components.ComponentRegistrar" />
238-->[com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd549eab9cdb581d5cd730ab22aa1b6\transformed\jetified-firebase-crashlytics-19.2.1\AndroidManifest.xml:20:17-82
239            <meta-data
239-->[com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34162c290514c368f494368d6102f2e8\transformed\jetified-firebase-sessions-2.0.6\AndroidManifest.xml:29:13-31:85
240                android:name="com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar"
240-->[com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34162c290514c368f494368d6102f2e8\transformed\jetified-firebase-sessions-2.0.6\AndroidManifest.xml:30:17-117
241                android:value="com.google.firebase.components.ComponentRegistrar" />
241-->[com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34162c290514c368f494368d6102f2e8\transformed\jetified-firebase-sessions-2.0.6\AndroidManifest.xml:31:17-82
242            <meta-data
242-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ce1bda87d7244e8b25ef2bc59b4ecef\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
243                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
243-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ce1bda87d7244e8b25ef2bc59b4ecef\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
244                android:value="com.google.firebase.components.ComponentRegistrar" />
244-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ce1bda87d7244e8b25ef2bc59b4ecef\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
245            <meta-data
245-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ce1bda87d7244e8b25ef2bc59b4ecef\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
246                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
246-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ce1bda87d7244e8b25ef2bc59b4ecef\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
247                android:value="com.google.firebase.components.ComponentRegistrar" />
247-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ce1bda87d7244e8b25ef2bc59b4ecef\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
248            <meta-data
248-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82e68bdc17783eab343147c24d5a4c00\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
249                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
249-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82e68bdc17783eab343147c24d5a4c00\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:30:17-120
250                android:value="com.google.firebase.components.ComponentRegistrar" />
250-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82e68bdc17783eab343147c24d5a4c00\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:31:17-82
251            <meta-data
251-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82e68bdc17783eab343147c24d5a4c00\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
252                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
252-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82e68bdc17783eab343147c24d5a4c00\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:33:17-109
253                android:value="com.google.firebase.components.ComponentRegistrar" />
253-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82e68bdc17783eab343147c24d5a4c00\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:34:17-82
254            <meta-data
254-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25a61a6d44aa83fddee0cb806da6dac6\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
255                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
255-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25a61a6d44aa83fddee0cb806da6dac6\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
256                android:value="com.google.firebase.components.ComponentRegistrar" />
256-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25a61a6d44aa83fddee0cb806da6dac6\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
257            <meta-data
257-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
258                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
258-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
259                android:value="com.google.firebase.components.ComponentRegistrar" />
259-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
260            <meta-data
260-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c9f1b0cfb3dce6ae7cf4b0f010a4436\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:12:13-14:85
261                android:name="com.google.firebase.components:com.google.firebase.abt.component.AbtRegistrar"
261-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c9f1b0cfb3dce6ae7cf4b0f010a4436\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:13:17-109
262                android:value="com.google.firebase.components.ComponentRegistrar" />
262-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c9f1b0cfb3dce6ae7cf4b0f010a4436\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:14:17-82
263            <meta-data
263-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf01dc2d83636955506f1575030bb524\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:25:13-27:85
264                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
264-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf01dc2d83636955506f1575030bb524\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:26:17-115
265                android:value="com.google.firebase.components.ComponentRegistrar" />
265-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf01dc2d83636955506f1575030bb524\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:27:17-82
266        </service>
267
268        <activity
268-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:20:9-24:75
269            android:name="com.facebook.ads.AudienceNetworkActivity"
269-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:21:13-68
270            android:configChanges="keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
270-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:22:13-106
271            android:exported="false"
271-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:23:13-37
272            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
272-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:24:13-72
273
274        <provider
274-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:26:9-29:40
275            android:name="com.facebook.ads.AudienceNetworkContentProvider"
275-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:27:13-75
276            android:authorities="com.app.videofbdownloadfree.AudienceNetworkContentProvider"
276-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:28:13-82
277            android:exported="false" /> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
277-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:29:13-37
278        <activity
278-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:73:9-78:43
279            android:name="com.google.android.gms.ads.AdActivity"
279-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:74:13-65
280            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
280-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:75:13-122
281            android:exported="false"
281-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:76:13-37
282            android:theme="@android:style/Theme.Translucent" />
282-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:77:13-61
283
284        <provider
284-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:80:9-85:43
285            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
285-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:81:13-76
286            android:authorities="com.app.videofbdownloadfree.mobileadsinitprovider"
286-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:82:13-73
287            android:exported="false"
287-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:83:13-37
288            android:initOrder="100" />
288-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:84:13-36
289
290        <service
290-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:87:9-91:43
291            android:name="com.google.android.gms.ads.AdService"
291-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:88:13-64
292            android:enabled="true"
292-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:89:13-35
293            android:exported="false" />
293-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:90:13-37
294
295        <activity
295-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:93:9-97:43
296            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
296-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:94:13-82
297            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
297-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:95:13-122
298            android:exported="false" />
298-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:96:13-37
299        <activity
299-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:98:9-105:43
300            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
300-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:99:13-82
301            android:excludeFromRecents="true"
301-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:100:13-46
302            android:exported="false"
302-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:101:13-37
303            android:launchMode="singleTask"
303-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:102:13-44
304            android:taskAffinity=""
304-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:103:13-36
305            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
305-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:104:13-72
306
307        <receiver
307-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:29:9-33:20
308            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
308-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:30:13-85
309            android:enabled="true"
309-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:31:13-35
310            android:exported="false" >
310-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:32:13-37
311        </receiver>
312
313        <service
313-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:35:9-38:40
314            android:name="com.google.android.gms.measurement.AppMeasurementService"
314-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:36:13-84
315            android:enabled="true"
315-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:37:13-35
316            android:exported="false" />
316-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:38:13-37
317        <service
317-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:39:9-43:72
318            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
318-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:40:13-87
319            android:enabled="true"
319-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:41:13-35
320            android:exported="false"
320-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:42:13-37
321            android:permission="android.permission.BIND_JOB_SERVICE" />
321-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:43:13-69
322        <service
322-->[com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34162c290514c368f494368d6102f2e8\transformed\jetified-firebase-sessions-2.0.6\AndroidManifest.xml:22:9-25:40
323            android:name="com.google.firebase.sessions.SessionLifecycleService"
323-->[com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34162c290514c368f494368d6102f2e8\transformed\jetified-firebase-sessions-2.0.6\AndroidManifest.xml:23:13-80
324            android:enabled="true"
324-->[com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34162c290514c368f494368d6102f2e8\transformed\jetified-firebase-sessions-2.0.6\AndroidManifest.xml:24:13-35
325            android:exported="false" />
325-->[com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34162c290514c368f494368d6102f2e8\transformed\jetified-firebase-sessions-2.0.6\AndroidManifest.xml:25:13-37
326
327        <provider
327-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
328            android:name="com.google.firebase.provider.FirebaseInitProvider"
328-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
329            android:authorities="com.app.videofbdownloadfree.firebaseinitprovider"
329-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
330            android:directBootAware="true"
330-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
331            android:exported="false"
331-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
332            android:initOrder="100" />
332-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
333        <provider
333-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\071658833db178a8621052ffa0849746\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
334            android:name="androidx.startup.InitializationProvider"
334-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\071658833db178a8621052ffa0849746\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
335            android:authorities="com.app.videofbdownloadfree.androidx-startup"
335-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\071658833db178a8621052ffa0849746\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
336            android:exported="false" >
336-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\071658833db178a8621052ffa0849746\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
337            <meta-data
337-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\071658833db178a8621052ffa0849746\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
338                android:name="androidx.emoji2.text.EmojiCompatInitializer"
338-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\071658833db178a8621052ffa0849746\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
339                android:value="androidx.startup" />
339-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\071658833db178a8621052ffa0849746\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
340            <meta-data
340-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
341                android:name="androidx.work.WorkManagerInitializer"
341-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
342                android:value="androidx.startup" />
342-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
343            <meta-data
343-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd4dde74fe4031e7f9743f34c05041e2\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
344                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
344-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd4dde74fe4031e7f9743f34c05041e2\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
345                android:value="androidx.startup" />
345-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd4dde74fe4031e7f9743f34c05041e2\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
346            <meta-data
346-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
347                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
347-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
348                android:value="androidx.startup" />
348-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
349        </provider>
350
351        <service
351-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
352            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
352-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
353            android:directBootAware="false"
353-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
354            android:enabled="@bool/enable_system_alarm_service_default"
354-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
355            android:exported="false" />
355-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
356        <service
356-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
357            android:name="androidx.work.impl.background.systemjob.SystemJobService"
357-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
358            android:directBootAware="false"
358-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
359            android:enabled="@bool/enable_system_job_service_default"
359-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
360            android:exported="true"
360-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
361            android:permission="android.permission.BIND_JOB_SERVICE" />
361-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
362        <service
362-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
363            android:name="androidx.work.impl.foreground.SystemForegroundService"
363-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
364            android:directBootAware="false"
364-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
365            android:enabled="@bool/enable_system_foreground_service_default"
365-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
366            android:exported="false" />
366-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
367
368        <receiver
368-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
369            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
369-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
370            android:directBootAware="false"
370-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
371            android:enabled="true"
371-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
372            android:exported="false" />
372-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
373        <receiver
373-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
374            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
374-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
375            android:directBootAware="false"
375-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
376            android:enabled="false"
376-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
377            android:exported="false" >
377-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
378            <intent-filter>
378-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
379                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
379-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
379-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
380                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
380-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
380-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
381            </intent-filter>
382        </receiver>
383        <receiver
383-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
384            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
384-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
385            android:directBootAware="false"
385-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
386            android:enabled="false"
386-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
387            android:exported="false" >
387-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
388            <intent-filter>
388-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
389                <action android:name="android.intent.action.BATTERY_OKAY" />
389-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
389-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
390                <action android:name="android.intent.action.BATTERY_LOW" />
390-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
390-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
391            </intent-filter>
392        </receiver>
393        <receiver
393-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
394            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
394-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
395            android:directBootAware="false"
395-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
396            android:enabled="false"
396-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
397            android:exported="false" >
397-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
398            <intent-filter>
398-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
399                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
399-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
399-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
400                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
400-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
400-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
401            </intent-filter>
402        </receiver>
403        <receiver
403-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
404            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
404-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
405            android:directBootAware="false"
405-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
406            android:enabled="false"
406-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
407            android:exported="false" >
407-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
408            <intent-filter>
408-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
409                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
409-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
409-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
410            </intent-filter>
411        </receiver>
412        <receiver
412-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
413            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
413-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
414            android:directBootAware="false"
414-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
415            android:enabled="false"
415-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
416            android:exported="false" >
416-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
417            <intent-filter>
417-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
418                <action android:name="android.intent.action.BOOT_COMPLETED" />
418-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:17-79
418-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:25-76
419                <action android:name="android.intent.action.TIME_SET" />
419-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
419-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
420                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
420-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
420-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
421            </intent-filter>
422        </receiver>
423        <receiver
423-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
424            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
424-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
425            android:directBootAware="false"
425-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
426            android:enabled="@bool/enable_system_alarm_service_default"
426-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
427            android:exported="false" >
427-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
428            <intent-filter>
428-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
429                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
429-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
429-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
430            </intent-filter>
431        </receiver>
432        <receiver
432-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
433            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
433-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
434            android:directBootAware="false"
434-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
435            android:enabled="true"
435-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
436            android:exported="true"
436-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
437            android:permission="android.permission.DUMP" >
437-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
438            <intent-filter>
438-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
439                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
439-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
439-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
440            </intent-filter>
441        </receiver>
442
443        <uses-library
443-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23c7b3148a83432ca440c27e9e880ef9\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
444            android:name="androidx.window.extensions"
444-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23c7b3148a83432ca440c27e9e880ef9\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
445            android:required="false" />
445-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23c7b3148a83432ca440c27e9e880ef9\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
446        <uses-library
446-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23c7b3148a83432ca440c27e9e880ef9\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
447            android:name="androidx.window.sidecar"
447-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23c7b3148a83432ca440c27e9e880ef9\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
448            android:required="false" />
448-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23c7b3148a83432ca440c27e9e880ef9\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
449        <uses-library
449-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\903e092fc049e07eb9802b2b1d837d55\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
450            android:name="android.ext.adservices"
450-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\903e092fc049e07eb9802b2b1d837d55\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
451            android:required="false" />
451-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\903e092fc049e07eb9802b2b1d837d55\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
452
453        <activity
453-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64d1cc8f3e9c59c96e0297d996aad4c8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
454            android:name="com.google.android.gms.common.api.GoogleApiActivity"
454-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64d1cc8f3e9c59c96e0297d996aad4c8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
455            android:exported="false"
455-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64d1cc8f3e9c59c96e0297d996aad4c8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
456            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
456-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64d1cc8f3e9c59c96e0297d996aad4c8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
457
458        <meta-data
458-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c96b0d8b72c615a79b6ffc473f6a8013\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
459            android:name="com.google.android.gms.version"
459-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c96b0d8b72c615a79b6ffc473f6a8013\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
460            android:value="@integer/google_play_services_version" />
460-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c96b0d8b72c615a79b6ffc473f6a8013\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
461
462        <service
462-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85713e3337aa4c8e7156fef507f40140\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:26:9-32:19
463            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
463-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85713e3337aa4c8e7156fef507f40140\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:27:13-103
464            android:exported="false" >
464-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85713e3337aa4c8e7156fef507f40140\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:28:13-37
465            <meta-data
465-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85713e3337aa4c8e7156fef507f40140\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:29:13-31:39
466                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
466-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85713e3337aa4c8e7156fef507f40140\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:30:17-94
467                android:value="cct" />
467-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85713e3337aa4c8e7156fef507f40140\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:31:17-36
468        </service>
469
470        <receiver
470-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
471            android:name="androidx.profileinstaller.ProfileInstallReceiver"
471-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
472            android:directBootAware="false"
472-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
473            android:enabled="true"
473-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
474            android:exported="true"
474-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
475            android:permission="android.permission.DUMP" >
475-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
476            <intent-filter>
476-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
477                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
477-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
477-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
478            </intent-filter>
479            <intent-filter>
479-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
480                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
480-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
480-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
481            </intent-filter>
482            <intent-filter>
482-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
483                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
483-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
483-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
484            </intent-filter>
485            <intent-filter>
485-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
486                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
486-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
486-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
487            </intent-filter>
488        </receiver>
489
490        <service
490-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdc16e1b8883ecf192e9948d1c13071a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:24:9-28:19
491            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
491-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdc16e1b8883ecf192e9948d1c13071a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:25:13-117
492            android:exported="false"
492-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdc16e1b8883ecf192e9948d1c13071a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:26:13-37
493            android:permission="android.permission.BIND_JOB_SERVICE" >
493-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdc16e1b8883ecf192e9948d1c13071a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:27:13-69
494        </service>
495
496        <receiver
496-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdc16e1b8883ecf192e9948d1c13071a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:30:9-32:40
497            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
497-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdc16e1b8883ecf192e9948d1c13071a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:31:13-132
498            android:exported="false" />
498-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdc16e1b8883ecf192e9948d1c13071a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:32:13-37
499
500        <service
500-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23645d451edec15b499b2c0a1186f68f\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
501            android:name="androidx.room.MultiInstanceInvalidationService"
501-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23645d451edec15b499b2c0a1186f68f\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
502            android:directBootAware="true"
502-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23645d451edec15b499b2c0a1186f68f\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
503            android:exported="false" />
503-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23645d451edec15b499b2c0a1186f68f\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
504    </application>
505
506</manifest>
