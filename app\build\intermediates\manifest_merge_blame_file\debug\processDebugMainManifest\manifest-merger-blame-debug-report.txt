1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.app.videofbdownloadfree"
4    android:versionCode="19"
5    android:versionName="4.5.1" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10    <!-- Make features optional to increase device compatibility -->
11    <uses-feature
11-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:89:5-85
12        android:name="android.hardware.camera"
12-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:89:19-57
13        android:required="false" />
13-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:89:58-82
14    <uses-feature
14-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:90:5-88
15        android:name="android.hardware.telephony"
15-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:90:19-60
16        android:required="false" />
16-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:90:61-85
17    <uses-feature
17-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:91:5-91
18        android:name="android.hardware.location.gps"
18-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:91:19-63
19        android:required="false" />
19-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:91:64-88
20    <uses-feature
20-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:92:5-99
21        android:name="android.hardware.sensor.accelerometer"
21-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:92:19-71
22        android:required="false" />
22-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:92:72-96
23
24    <!-- Support additional form factors -->
25    <uses-permission android:name="android.permission.INTERNET" />
25-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:95:5-67
25-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:95:22-64
26    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
26-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:96:5-79
26-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:96:22-76
27    <uses-permission
27-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:97:5-98:38
28        android:name="android.permission.READ_EXTERNAL_STORAGE"
28-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:97:22-77
29        android:maxSdkVersion="32" />
29-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:98:9-35
30    <uses-permission
30-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:99:5-100:38
31        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
31-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:99:22-78
32        android:maxSdkVersion="28" />
32-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:100:9-35
33    <!-- For Android 11+ manage all files permission -->
34    <uses-permission
34-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:97:5-98:38
35        android:name="android.permission.READ_EXTERNAL_STORAGE"
35-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:97:22-77
36        android:maxSdkVersion="32" />
36-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:98:9-35
37    <uses-permission
37-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:99:5-100:38
38        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
38-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:99:22-78
39        android:maxSdkVersion="28" />
39-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:100:9-35
40    <!-- For Android 13+ (API 33+) media permissions -->
41    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
41-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:107:5-76
41-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:107:22-73
42    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
42-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:108:5-75
42-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:108:22-72
43    <!-- For Android 14+ (API 34+) partial media access -->
44    <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED" />
44-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:110:5-90
44-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:110:22-87
45
46    <uses-feature
46-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:89:5-85
47        android:name="android.hardware.camera"
47-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:89:19-57
48        android:required="false" />
48-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:89:58-82
49    <uses-feature
49-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:90:5-88
50        android:name="android.hardware.telephony"
50-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:90:19-60
51        android:required="false" />
51-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:90:61-85
52    <uses-feature
52-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:91:5-91
53        android:name="android.hardware.location.gps"
53-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:91:19-63
54        android:required="false" />
54-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:91:64-88
55    <uses-feature
55-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:92:5-99
56        android:name="android.hardware.sensor.accelerometer"
56-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:92:19-71
57        android:required="false" />
57-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:92:72-96
58    <!-- Support Android TV -->
59    <uses-feature
59-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:116:5-87
60        android:name="android.software.leanback"
60-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:116:19-59
61        android:required="false" />
61-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:116:60-84
62    <!-- Support Android Auto / Automotive -->
63    <uses-feature
63-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:118:5-94
64        android:name="android.hardware.type.automotive"
64-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:118:19-66
65        android:required="false" />
65-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:118:67-91
66    <!-- Support Chromebook -->
67    <uses-feature
67-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:120:5-78
68        android:name="org.chromium.arc"
68-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:120:19-50
69        android:required="false" />
69-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:120:51-75
70    <uses-feature
70-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:121:5-123:36
71        android:name="android.hardware.touchscreen"
71-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:122:9-52
72        android:required="false" />
72-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:123:9-33
73
74    <queries>
74-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:11:5-13:15
75        <package android:name="com.facebook.katana" />
75-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:12:9-55
75-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:12:18-52
76        <!-- For browser content -->
77        <intent>
77-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:38:9-44:18
78            <action android:name="android.intent.action.VIEW" />
78-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:39:13-65
78-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:39:21-62
79
80            <category android:name="android.intent.category.BROWSABLE" />
80-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:41:13-74
80-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:41:23-71
81
82            <data android:scheme="https" />
82-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:43:13-44
82-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:43:19-41
83        </intent> <!-- End of browser content -->
84        <!-- For CustomTabsService -->
85        <intent>
85-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:47:9-49:18
86            <action android:name="android.support.customtabs.action.CustomTabsService" />
86-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:48:13-90
86-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:48:21-87
87        </intent> <!-- End of CustomTabsService -->
88        <!-- For MRAID capabilities -->
89        <intent>
89-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:52:9-56:18
90            <action android:name="android.intent.action.INSERT" />
90-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:53:13-67
90-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:53:21-64
91
92            <data android:mimeType="vnd.android.cursor.dir/event" />
92-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:43:13-44
93        </intent>
94        <intent>
94-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:57:9-61:18
95            <action android:name="android.intent.action.VIEW" />
95-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:39:13-65
95-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:39:21-62
96
97            <data android:scheme="sms" />
97-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:43:13-44
97-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:43:19-41
98        </intent>
99        <intent>
99-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:62:9-66:18
100            <action android:name="android.intent.action.DIAL" />
100-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:63:13-65
100-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:63:21-62
101
102            <data android:path="tel:" />
102-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:43:13-44
103        </intent>
104    </queries>
105
106    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
106-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:17:5-79
106-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:17:22-76
107    <uses-permission android:name="android.permission.WAKE_LOCK" />
107-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:24:5-68
107-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:24:22-65
108    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
108-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:26:5-88
108-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:26:22-85
109    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
109-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:27:5-82
109-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:27:22-79
110    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" />
110-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:29:5-83
110-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:29:22-80
111    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
111-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:26:5-110
111-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:26:22-107
112    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
112-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
112-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
113
114    <permission
114-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d58a419118e34eadf0fdff30391c321\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
115        android:name="com.app.videofbdownloadfree.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
115-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d58a419118e34eadf0fdff30391c321\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
116        android:protectionLevel="signature" />
116-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d58a419118e34eadf0fdff30391c321\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
117
118    <uses-permission android:name="com.app.videofbdownloadfree.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
118-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d58a419118e34eadf0fdff30391c321\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
118-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d58a419118e34eadf0fdff30391c321\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
119
120    <application
120-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:6:5-87:19
121        android:name="com.app.videofbdownloadfree.MyApplication"
121-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:7:9-38
122        android:allowBackup="true"
122-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:8:9-35
123        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
123-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d58a419118e34eadf0fdff30391c321\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
124        android:dataExtractionRules="@xml/data_extraction_rules"
124-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:9:9-65
125        android:debuggable="true"
126        android:extractNativeLibs="false"
127        android:fullBackupContent="@xml/backup_rules"
127-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:10:9-54
128        android:icon="@drawable/icon"
128-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:11:9-38
129        android:label="@string/app_name"
129-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:12:9-41
130        android:preserveLegacyExternalStorage="true"
130-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:16:9-53
131        android:requestLegacyExternalStorage="true"
131-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:15:9-52
132        android:supportsRtl="true"
132-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:13:9-35
133        android:theme="@style/Theme.InstaLoader" >
133-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:14:9-49
134        <meta-data
134-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:18:9-20:47
135            android:name="com.google.android.gms.ads.APPLICATION_ID"
135-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:19:13-69
136            android:value="@string/admob_id" />
136-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:20:13-45
137
138        <!-- Facebook Audience Network for Bidding -->
139        <meta-data
139-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:23:9-25:55
140            android:name="com.facebook.sdk.ApplicationId"
140-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:24:13-58
141            android:value="@string/facebook_app_id" />
141-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:25:13-52
142        <meta-data
142-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:26:9-28:61
143            android:name="com.facebook.sdk.ClientToken"
143-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:27:13-56
144            android:value="@string/facebook_client_token" />
144-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:28:13-58
145
146        <!-- Fix for AD_SERVICES_CONFIG conflict -->
147        <property
147-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:31:9-34:48
148            android:name="android.adservices.AD_SERVICES_CONFIG"
148-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:32:13-65
149            android:resource="@xml/gma_ad_services_config" />
149-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:33:13-59
150
151        <!-- Splash Activity -->
152        <activity
152-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:37:9-44:20
153            android:name="com.app.videofbdownloadfree.SplashActivity"
153-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:38:13-43
154            android:exported="true" >
154-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:39:13-36
155            <intent-filter>
155-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:40:13-43:29
156                <action android:name="android.intent.action.MAIN" />
156-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:41:17-69
156-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:41:25-66
157
158                <category android:name="android.intent.category.LAUNCHER" />
158-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:42:17-77
158-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:42:27-74
159            </intent-filter>
160        </activity>
161        <!-- No Internet Activity -->
162        <activity
162-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:46:9-48:40
163            android:name="com.app.videofbdownloadfree.NoInternetActivity"
163-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:47:13-47
164            android:exported="false" />
164-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:48:13-37
165        <!-- Main Activity -->
166        <activity
166-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:50:9-53:70
167            android:name="com.app.videofbdownloadfree.MainActivity"
167-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:51:13-41
168            android:exported="false"
168-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:52:13-37
169            android:windowSoftInputMode="stateHidden|adjustResize" />
169-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:53:13-67
170
171        <!-- Recent Downloads Activity -->
172        <activity
172-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:56:9-58:40
173            android:name="com.app.videofbdownloadfree.RecentDownloadsActivity"
173-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:57:13-52
174            android:exported="false" />
174-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:58:13-37
175
176        <!-- Permissions Activity -->
177        <activity
177-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:61:9-64:56
178            android:name="com.app.videofbdownloadfree.PermissionsActivity"
178-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:62:13-48
179            android:exported="false"
179-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:63:13-37
180            android:theme="@style/Theme.InstaLoader" />
180-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:64:13-53
181
182        <!-- Ads Consent Activity -->
183        <activity
183-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:67:9-70:56
184            android:name="com.app.videofbdownloadfree.AdsConsentActivity"
184-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:68:13-47
185            android:exported="false"
185-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:69:13-37
186            android:theme="@style/Theme.InstaLoader" />
186-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:70:13-53
187        <activity android:name="com.app.videofbdownloadfree.SettingsActivity" />
187-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:72:9-54
187-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:72:19-51
188        <activity android:name="com.app.videofbdownloadfree.VideoPlayerActivity" />
188-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:73:9-57
188-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:73:19-54
189
190        <!-- File Provider for sharing files -->
191        <provider
192            android:name="androidx.core.content.FileProvider"
192-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:78:13-62
193            android:authorities="com.app.videofbdownloadfree.fileprovider"
193-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:79:13-64
194            android:exported="false"
194-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:80:13-37
195            android:grantUriPermissions="true" >
195-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:81:13-47
196            <meta-data
196-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:82:13-84:54
197                android:name="android.support.FILE_PROVIDER_PATHS"
197-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:83:17-67
198                android:resource="@xml/file_paths" />
198-->C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:84:17-51
199        </provider>
200
201        <service
201-->[com.google.firebase:firebase-perf-ktx:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7e9769f3c4b2af8270ad0d6acca89ac\transformed\jetified-firebase-perf-ktx-21.0.2\AndroidManifest.xml:9:9-15:19
202            android:name="com.google.firebase.components.ComponentDiscoveryService"
202-->[com.google.firebase:firebase-perf-ktx:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7e9769f3c4b2af8270ad0d6acca89ac\transformed\jetified-firebase-perf-ktx-21.0.2\AndroidManifest.xml:10:13-84
203            android:directBootAware="true"
203-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
204            android:exported="false" >
204-->[com.google.firebase:firebase-perf-ktx:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7e9769f3c4b2af8270ad0d6acca89ac\transformed\jetified-firebase-perf-ktx-21.0.2\AndroidManifest.xml:11:13-37
205            <meta-data
205-->[com.google.firebase:firebase-perf-ktx:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7e9769f3c4b2af8270ad0d6acca89ac\transformed\jetified-firebase-perf-ktx-21.0.2\AndroidManifest.xml:12:13-14:85
206                android:name="com.google.firebase.components:com.google.firebase.perf.ktx.FirebasePerfLegacyRegistrar"
206-->[com.google.firebase:firebase-perf-ktx:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7e9769f3c4b2af8270ad0d6acca89ac\transformed\jetified-firebase-perf-ktx-21.0.2\AndroidManifest.xml:13:17-119
207                android:value="com.google.firebase.components.ComponentRegistrar" />
207-->[com.google.firebase:firebase-perf-ktx:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7e9769f3c4b2af8270ad0d6acca89ac\transformed\jetified-firebase-perf-ktx-21.0.2\AndroidManifest.xml:14:17-82
208            <meta-data
208-->[com.google.firebase:firebase-perf:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2329f23c4c3a4bf92b4344d445f02482\transformed\jetified-firebase-perf-21.0.2\AndroidManifest.xml:14:13-16:85
209                android:name="com.google.firebase.components:com.google.firebase.perf.FirebasePerfKtxRegistrar"
209-->[com.google.firebase:firebase-perf:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2329f23c4c3a4bf92b4344d445f02482\transformed\jetified-firebase-perf-21.0.2\AndroidManifest.xml:15:17-112
210                android:value="com.google.firebase.components.ComponentRegistrar" />
210-->[com.google.firebase:firebase-perf:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2329f23c4c3a4bf92b4344d445f02482\transformed\jetified-firebase-perf-21.0.2\AndroidManifest.xml:16:17-82
211            <meta-data
211-->[com.google.firebase:firebase-perf:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2329f23c4c3a4bf92b4344d445f02482\transformed\jetified-firebase-perf-21.0.2\AndroidManifest.xml:17:13-19:85
212                android:name="com.google.firebase.components:com.google.firebase.perf.FirebasePerfRegistrar"
212-->[com.google.firebase:firebase-perf:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2329f23c4c3a4bf92b4344d445f02482\transformed\jetified-firebase-perf-21.0.2\AndroidManifest.xml:18:17-109
213                android:value="com.google.firebase.components.ComponentRegistrar" />
213-->[com.google.firebase:firebase-perf:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2329f23c4c3a4bf92b4344d445f02482\transformed\jetified-firebase-perf-21.0.2\AndroidManifest.xml:19:17-82
214            <meta-data
214-->[com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec49ca28e4aa5b972b86f27aa77e6aa7\transformed\jetified-firebase-analytics-ktx-22.1.2\AndroidManifest.xml:11:13-13:85
215                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
215-->[com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec49ca28e4aa5b972b86f27aa77e6aa7\transformed\jetified-firebase-analytics-ktx-22.1.2\AndroidManifest.xml:12:17-129
216                android:value="com.google.firebase.components.ComponentRegistrar" />
216-->[com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec49ca28e4aa5b972b86f27aa77e6aa7\transformed\jetified-firebase-analytics-ktx-22.1.2\AndroidManifest.xml:13:17-82
217            <meta-data
217-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:37:13-39:85
218                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
218-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:38:17-139
219                android:value="com.google.firebase.components.ComponentRegistrar" />
219-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:39:17-82
220            <meta-data
220-->[com.google.firebase:firebase-config:22.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc2b22665e024b976aa2bd68e6e79f9\transformed\jetified-firebase-config-22.0.1\AndroidManifest.xml:29:13-31:85
221                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.FirebaseRemoteConfigKtxRegistrar"
221-->[com.google.firebase:firebase-config:22.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc2b22665e024b976aa2bd68e6e79f9\transformed\jetified-firebase-config-22.0.1\AndroidManifest.xml:30:17-128
222                android:value="com.google.firebase.components.ComponentRegistrar" />
222-->[com.google.firebase:firebase-config:22.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc2b22665e024b976aa2bd68e6e79f9\transformed\jetified-firebase-config-22.0.1\AndroidManifest.xml:31:17-82
223            <meta-data
223-->[com.google.firebase:firebase-config:22.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc2b22665e024b976aa2bd68e6e79f9\transformed\jetified-firebase-config-22.0.1\AndroidManifest.xml:32:13-34:85
224                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.RemoteConfigRegistrar"
224-->[com.google.firebase:firebase-config:22.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc2b22665e024b976aa2bd68e6e79f9\transformed\jetified-firebase-config-22.0.1\AndroidManifest.xml:33:17-117
225                android:value="com.google.firebase.components.ComponentRegistrar" />
225-->[com.google.firebase:firebase-config:22.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc2b22665e024b976aa2bd68e6e79f9\transformed\jetified-firebase-config-22.0.1\AndroidManifest.xml:34:17-82
226            <meta-data
226-->[com.google.firebase:firebase-crashlytics-ktx:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2af3c9a2590e755876f4d3a775a327e7\transformed\jetified-firebase-crashlytics-ktx-19.2.1\AndroidManifest.xml:24:13-26:85
227                android:name="com.google.firebase.components:com.google.firebase.crashlytics.ktx.FirebaseCrashlyticsKtxRegistrar"
227-->[com.google.firebase:firebase-crashlytics-ktx:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2af3c9a2590e755876f4d3a775a327e7\transformed\jetified-firebase-crashlytics-ktx-19.2.1\AndroidManifest.xml:25:17-130
228                android:value="com.google.firebase.components.ComponentRegistrar" />
228-->[com.google.firebase:firebase-crashlytics-ktx:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2af3c9a2590e755876f4d3a775a327e7\transformed\jetified-firebase-crashlytics-ktx-19.2.1\AndroidManifest.xml:26:17-82
229            <meta-data
229-->[com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07a2722339bfa0219c25a815d830727d\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
230                android:name="com.google.firebase.components:com.google.firebase.database.ktx.FirebaseDatabaseLegacyRegistrar"
230-->[com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07a2722339bfa0219c25a815d830727d\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:13:17-127
231                android:value="com.google.firebase.components.ComponentRegistrar" />
231-->[com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07a2722339bfa0219c25a815d830727d\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:14:17-82
232            <meta-data
232-->[com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd549eab9cdb581d5cd730ab22aa1b6\transformed\jetified-firebase-crashlytics-19.2.1\AndroidManifest.xml:15:13-17:85
233                android:name="com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar"
233-->[com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd549eab9cdb581d5cd730ab22aa1b6\transformed\jetified-firebase-crashlytics-19.2.1\AndroidManifest.xml:16:17-126
234                android:value="com.google.firebase.components.ComponentRegistrar" />
234-->[com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd549eab9cdb581d5cd730ab22aa1b6\transformed\jetified-firebase-crashlytics-19.2.1\AndroidManifest.xml:17:17-82
235            <meta-data
235-->[com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd549eab9cdb581d5cd730ab22aa1b6\transformed\jetified-firebase-crashlytics-19.2.1\AndroidManifest.xml:18:13-20:85
236                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
236-->[com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd549eab9cdb581d5cd730ab22aa1b6\transformed\jetified-firebase-crashlytics-19.2.1\AndroidManifest.xml:19:17-115
237                android:value="com.google.firebase.components.ComponentRegistrar" />
237-->[com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd549eab9cdb581d5cd730ab22aa1b6\transformed\jetified-firebase-crashlytics-19.2.1\AndroidManifest.xml:20:17-82
238            <meta-data
238-->[com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34162c290514c368f494368d6102f2e8\transformed\jetified-firebase-sessions-2.0.6\AndroidManifest.xml:29:13-31:85
239                android:name="com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar"
239-->[com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34162c290514c368f494368d6102f2e8\transformed\jetified-firebase-sessions-2.0.6\AndroidManifest.xml:30:17-117
240                android:value="com.google.firebase.components.ComponentRegistrar" />
240-->[com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34162c290514c368f494368d6102f2e8\transformed\jetified-firebase-sessions-2.0.6\AndroidManifest.xml:31:17-82
241            <meta-data
241-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ce1bda87d7244e8b25ef2bc59b4ecef\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
242                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
242-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ce1bda87d7244e8b25ef2bc59b4ecef\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
243                android:value="com.google.firebase.components.ComponentRegistrar" />
243-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ce1bda87d7244e8b25ef2bc59b4ecef\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
244            <meta-data
244-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ce1bda87d7244e8b25ef2bc59b4ecef\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
245                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
245-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ce1bda87d7244e8b25ef2bc59b4ecef\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
246                android:value="com.google.firebase.components.ComponentRegistrar" />
246-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ce1bda87d7244e8b25ef2bc59b4ecef\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
247            <meta-data
247-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82e68bdc17783eab343147c24d5a4c00\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
248                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
248-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82e68bdc17783eab343147c24d5a4c00\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:30:17-120
249                android:value="com.google.firebase.components.ComponentRegistrar" />
249-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82e68bdc17783eab343147c24d5a4c00\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:31:17-82
250            <meta-data
250-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82e68bdc17783eab343147c24d5a4c00\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
251                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
251-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82e68bdc17783eab343147c24d5a4c00\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:33:17-109
252                android:value="com.google.firebase.components.ComponentRegistrar" />
252-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82e68bdc17783eab343147c24d5a4c00\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:34:17-82
253            <meta-data
253-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25a61a6d44aa83fddee0cb806da6dac6\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
254                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
254-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25a61a6d44aa83fddee0cb806da6dac6\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
255                android:value="com.google.firebase.components.ComponentRegistrar" />
255-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25a61a6d44aa83fddee0cb806da6dac6\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
256            <meta-data
256-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
257                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
257-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
258                android:value="com.google.firebase.components.ComponentRegistrar" />
258-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
259            <meta-data
259-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c9f1b0cfb3dce6ae7cf4b0f010a4436\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:12:13-14:85
260                android:name="com.google.firebase.components:com.google.firebase.abt.component.AbtRegistrar"
260-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c9f1b0cfb3dce6ae7cf4b0f010a4436\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:13:17-109
261                android:value="com.google.firebase.components.ComponentRegistrar" />
261-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c9f1b0cfb3dce6ae7cf4b0f010a4436\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:14:17-82
262            <meta-data
262-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf01dc2d83636955506f1575030bb524\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:25:13-27:85
263                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
263-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf01dc2d83636955506f1575030bb524\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:26:17-115
264                android:value="com.google.firebase.components.ComponentRegistrar" />
264-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf01dc2d83636955506f1575030bb524\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:27:17-82
265        </service>
266
267        <activity
267-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:20:9-24:75
268            android:name="com.facebook.ads.AudienceNetworkActivity"
268-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:21:13-68
269            android:configChanges="keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
269-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:22:13-106
270            android:exported="false"
270-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:23:13-37
271            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
271-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:24:13-72
272
273        <provider
273-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:26:9-29:40
274            android:name="com.facebook.ads.AudienceNetworkContentProvider"
274-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:27:13-75
275            android:authorities="com.app.videofbdownloadfree.AudienceNetworkContentProvider"
275-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:28:13-82
276            android:exported="false" /> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
276-->[com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:29:13-37
277        <activity
277-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:73:9-78:43
278            android:name="com.google.android.gms.ads.AdActivity"
278-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:74:13-65
279            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
279-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:75:13-122
280            android:exported="false"
280-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:76:13-37
281            android:theme="@android:style/Theme.Translucent" />
281-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:77:13-61
282
283        <provider
283-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:80:9-85:43
284            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
284-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:81:13-76
285            android:authorities="com.app.videofbdownloadfree.mobileadsinitprovider"
285-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:82:13-73
286            android:exported="false"
286-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:83:13-37
287            android:initOrder="100" />
287-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:84:13-36
288
289        <service
289-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:87:9-91:43
290            android:name="com.google.android.gms.ads.AdService"
290-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:88:13-64
291            android:enabled="true"
291-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:89:13-35
292            android:exported="false" />
292-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:90:13-37
293
294        <activity
294-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:93:9-97:43
295            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
295-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:94:13-82
296            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
296-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:95:13-122
297            android:exported="false" />
297-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:96:13-37
298        <activity
298-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:98:9-105:43
299            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
299-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:99:13-82
300            android:excludeFromRecents="true"
300-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:100:13-46
301            android:exported="false"
301-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:101:13-37
302            android:launchMode="singleTask"
302-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:102:13-44
303            android:taskAffinity=""
303-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:103:13-36
304            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
304-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:104:13-72
305
306        <receiver
306-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:29:9-33:20
307            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
307-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:30:13-85
308            android:enabled="true"
308-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:31:13-35
309            android:exported="false" >
309-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:32:13-37
310        </receiver>
311
312        <service
312-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:35:9-38:40
313            android:name="com.google.android.gms.measurement.AppMeasurementService"
313-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:36:13-84
314            android:enabled="true"
314-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:37:13-35
315            android:exported="false" />
315-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:38:13-37
316        <service
316-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:39:9-43:72
317            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
317-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:40:13-87
318            android:enabled="true"
318-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:41:13-35
319            android:exported="false"
319-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:42:13-37
320            android:permission="android.permission.BIND_JOB_SERVICE" />
320-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:43:13-69
321        <service
321-->[com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34162c290514c368f494368d6102f2e8\transformed\jetified-firebase-sessions-2.0.6\AndroidManifest.xml:22:9-25:40
322            android:name="com.google.firebase.sessions.SessionLifecycleService"
322-->[com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34162c290514c368f494368d6102f2e8\transformed\jetified-firebase-sessions-2.0.6\AndroidManifest.xml:23:13-80
323            android:enabled="true"
323-->[com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34162c290514c368f494368d6102f2e8\transformed\jetified-firebase-sessions-2.0.6\AndroidManifest.xml:24:13-35
324            android:exported="false" />
324-->[com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34162c290514c368f494368d6102f2e8\transformed\jetified-firebase-sessions-2.0.6\AndroidManifest.xml:25:13-37
325
326        <provider
326-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
327            android:name="com.google.firebase.provider.FirebaseInitProvider"
327-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
328            android:authorities="com.app.videofbdownloadfree.firebaseinitprovider"
328-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
329            android:directBootAware="true"
329-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
330            android:exported="false"
330-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
331            android:initOrder="100" />
331-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
332        <provider
332-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\071658833db178a8621052ffa0849746\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
333            android:name="androidx.startup.InitializationProvider"
333-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\071658833db178a8621052ffa0849746\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
334            android:authorities="com.app.videofbdownloadfree.androidx-startup"
334-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\071658833db178a8621052ffa0849746\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
335            android:exported="false" >
335-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\071658833db178a8621052ffa0849746\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
336            <meta-data
336-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\071658833db178a8621052ffa0849746\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
337                android:name="androidx.emoji2.text.EmojiCompatInitializer"
337-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\071658833db178a8621052ffa0849746\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
338                android:value="androidx.startup" />
338-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\071658833db178a8621052ffa0849746\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
339            <meta-data
339-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
340                android:name="androidx.work.WorkManagerInitializer"
340-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
341                android:value="androidx.startup" />
341-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
342            <meta-data
342-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd4dde74fe4031e7f9743f34c05041e2\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
343                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
343-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd4dde74fe4031e7f9743f34c05041e2\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
344                android:value="androidx.startup" />
344-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd4dde74fe4031e7f9743f34c05041e2\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
345            <meta-data
345-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
346                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
346-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
347                android:value="androidx.startup" />
347-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
348        </provider>
349
350        <service
350-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
351            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
351-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
352            android:directBootAware="false"
352-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
353            android:enabled="@bool/enable_system_alarm_service_default"
353-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
354            android:exported="false" />
354-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
355        <service
355-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
356            android:name="androidx.work.impl.background.systemjob.SystemJobService"
356-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
357            android:directBootAware="false"
357-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
358            android:enabled="@bool/enable_system_job_service_default"
358-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
359            android:exported="true"
359-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
360            android:permission="android.permission.BIND_JOB_SERVICE" />
360-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
361        <service
361-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
362            android:name="androidx.work.impl.foreground.SystemForegroundService"
362-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
363            android:directBootAware="false"
363-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
364            android:enabled="@bool/enable_system_foreground_service_default"
364-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
365            android:exported="false" />
365-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
366
367        <receiver
367-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
368            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
368-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
369            android:directBootAware="false"
369-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
370            android:enabled="true"
370-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
371            android:exported="false" />
371-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
372        <receiver
372-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
373            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
373-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
374            android:directBootAware="false"
374-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
375            android:enabled="false"
375-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
376            android:exported="false" >
376-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
377            <intent-filter>
377-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
378                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
378-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
378-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
379                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
379-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
379-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
380            </intent-filter>
381        </receiver>
382        <receiver
382-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
383            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
383-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
384            android:directBootAware="false"
384-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
385            android:enabled="false"
385-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
386            android:exported="false" >
386-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
387            <intent-filter>
387-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
388                <action android:name="android.intent.action.BATTERY_OKAY" />
388-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
388-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
389                <action android:name="android.intent.action.BATTERY_LOW" />
389-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
389-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
390            </intent-filter>
391        </receiver>
392        <receiver
392-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
393            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
393-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
394            android:directBootAware="false"
394-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
395            android:enabled="false"
395-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
396            android:exported="false" >
396-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
397            <intent-filter>
397-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
398                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
398-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
398-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
399                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
399-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
399-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
400            </intent-filter>
401        </receiver>
402        <receiver
402-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
403            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
403-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
404            android:directBootAware="false"
404-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
405            android:enabled="false"
405-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
406            android:exported="false" >
406-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
407            <intent-filter>
407-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
408                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
408-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
408-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
409            </intent-filter>
410        </receiver>
411        <receiver
411-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
412            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
412-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
413            android:directBootAware="false"
413-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
414            android:enabled="false"
414-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
415            android:exported="false" >
415-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
416            <intent-filter>
416-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
417                <action android:name="android.intent.action.BOOT_COMPLETED" />
417-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:17-79
417-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:25-76
418                <action android:name="android.intent.action.TIME_SET" />
418-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
418-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
419                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
419-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
419-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
420            </intent-filter>
421        </receiver>
422        <receiver
422-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
423            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
423-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
424            android:directBootAware="false"
424-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
425            android:enabled="@bool/enable_system_alarm_service_default"
425-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
426            android:exported="false" >
426-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
427            <intent-filter>
427-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
428                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
428-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
428-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
429            </intent-filter>
430        </receiver>
431        <receiver
431-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
432            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
432-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
433            android:directBootAware="false"
433-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
434            android:enabled="true"
434-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
435            android:exported="true"
435-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
436            android:permission="android.permission.DUMP" >
436-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
437            <intent-filter>
437-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
438                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
438-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
438-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
439            </intent-filter>
440        </receiver>
441
442        <uses-library
442-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23c7b3148a83432ca440c27e9e880ef9\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
443            android:name="androidx.window.extensions"
443-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23c7b3148a83432ca440c27e9e880ef9\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
444            android:required="false" />
444-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23c7b3148a83432ca440c27e9e880ef9\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
445        <uses-library
445-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23c7b3148a83432ca440c27e9e880ef9\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
446            android:name="androidx.window.sidecar"
446-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23c7b3148a83432ca440c27e9e880ef9\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
447            android:required="false" />
447-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23c7b3148a83432ca440c27e9e880ef9\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
448        <uses-library
448-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\903e092fc049e07eb9802b2b1d837d55\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
449            android:name="android.ext.adservices"
449-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\903e092fc049e07eb9802b2b1d837d55\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
450            android:required="false" />
450-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\903e092fc049e07eb9802b2b1d837d55\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
451
452        <activity
452-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64d1cc8f3e9c59c96e0297d996aad4c8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
453            android:name="com.google.android.gms.common.api.GoogleApiActivity"
453-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64d1cc8f3e9c59c96e0297d996aad4c8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
454            android:exported="false"
454-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64d1cc8f3e9c59c96e0297d996aad4c8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
455            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
455-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64d1cc8f3e9c59c96e0297d996aad4c8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
456
457        <meta-data
457-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c96b0d8b72c615a79b6ffc473f6a8013\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
458            android:name="com.google.android.gms.version"
458-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c96b0d8b72c615a79b6ffc473f6a8013\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
459            android:value="@integer/google_play_services_version" />
459-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c96b0d8b72c615a79b6ffc473f6a8013\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
460
461        <service
461-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85713e3337aa4c8e7156fef507f40140\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:26:9-32:19
462            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
462-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85713e3337aa4c8e7156fef507f40140\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:27:13-103
463            android:exported="false" >
463-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85713e3337aa4c8e7156fef507f40140\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:28:13-37
464            <meta-data
464-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85713e3337aa4c8e7156fef507f40140\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:29:13-31:39
465                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
465-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85713e3337aa4c8e7156fef507f40140\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:30:17-94
466                android:value="cct" />
466-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85713e3337aa4c8e7156fef507f40140\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:31:17-36
467        </service>
468
469        <receiver
469-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
470            android:name="androidx.profileinstaller.ProfileInstallReceiver"
470-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
471            android:directBootAware="false"
471-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
472            android:enabled="true"
472-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
473            android:exported="true"
473-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
474            android:permission="android.permission.DUMP" >
474-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
475            <intent-filter>
475-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
476                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
476-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
476-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
477            </intent-filter>
478            <intent-filter>
478-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
479                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
479-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
479-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
480            </intent-filter>
481            <intent-filter>
481-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
482                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
482-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
482-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
483            </intent-filter>
484            <intent-filter>
484-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
485                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
485-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
485-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
486            </intent-filter>
487        </receiver>
488
489        <service
489-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdc16e1b8883ecf192e9948d1c13071a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:24:9-28:19
490            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
490-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdc16e1b8883ecf192e9948d1c13071a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:25:13-117
491            android:exported="false"
491-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdc16e1b8883ecf192e9948d1c13071a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:26:13-37
492            android:permission="android.permission.BIND_JOB_SERVICE" >
492-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdc16e1b8883ecf192e9948d1c13071a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:27:13-69
493        </service>
494
495        <receiver
495-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdc16e1b8883ecf192e9948d1c13071a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:30:9-32:40
496            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
496-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdc16e1b8883ecf192e9948d1c13071a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:31:13-132
497            android:exported="false" />
497-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdc16e1b8883ecf192e9948d1c13071a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:32:13-37
498
499        <service
499-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23645d451edec15b499b2c0a1186f68f\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
500            android:name="androidx.room.MultiInstanceInvalidationService"
500-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23645d451edec15b499b2c0a1186f68f\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
501            android:directBootAware="true"
501-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23645d451edec15b499b2c0a1186f68f\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
502            android:exported="false" />
502-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23645d451edec15b499b2c0a1186f68f\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
503    </application>
504
505</manifest>
