package com.app.videofbdownloadfree

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.google.firebase.messaging.FirebaseMessaging
import kotlinx.coroutines.tasks.await

/**
 * Manager for Firebase Cloud Messaging
 * Handles push notifications and messaging
 */
object MessagingManager {
    private const val TAG = "MessagingManager"
    private const val PREFS_NAME = "messaging_prefs"
    private const val KEY_FCM_TOKEN = "fcm_token"
    private const val KEY_NOTIFICATIONS_ENABLED = "notifications_enabled"
    
    private var isInitialized = false
    private lateinit var prefs: SharedPreferences

    /**
     * Initialize Firebase Cloud Messaging
     * Call this in Application onCreate
     */
    fun initialize(context: Context) {
        if (isInitialized) {
            Log.d(TAG, "Messaging already initialized")
            return
        }

        try {
            Log.d(TAG, "Initializing Firebase Cloud Messaging...")
            
            prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            
            // Get FCM token
            getFCMToken { token ->
                if (token != null) {
                    saveFCMToken(token)
                    Log.d(TAG, "FCM token retrieved and saved")
                } else {
                    Log.w(TAG, "Failed to retrieve FCM token")
                }
            }
            
            // Subscribe to default topics
            subscribeToDefaultTopics()
            
            isInitialized = true
            Log.d(TAG, "Firebase Cloud Messaging initialized successfully")

        } catch (e: Exception) {
            Log.e(TAG, "Error initializing Firebase Cloud Messaging: ${e.message}")
            CrashlyticsManager.logException(e, "FCM initialization failed")
        }
    }

    /**
     * Get FCM token
     */
    fun getFCMToken(onComplete: (String?) -> Unit) {
        try {
            FirebaseMessaging.getInstance().token
                .addOnCompleteListener { task ->
                    if (!task.isSuccessful) {
                        Log.w(TAG, "Fetching FCM registration token failed", task.exception)
                        onComplete(null)
                        return@addOnCompleteListener
                    }

                    // Get new FCM registration token
                    val token = task.result
                    Log.d(TAG, "FCM Registration Token: $token")
                    onComplete(token)
                }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting FCM token: ${e.message}")
            onComplete(null)
        }
    }

    /**
     * Save FCM token to preferences
     */
    private fun saveFCMToken(token: String) {
        try {
            prefs.edit().putString(KEY_FCM_TOKEN, token).apply()
            Log.d(TAG, "FCM token saved to preferences")
            
            // Log to analytics
            AnalyticsManager.logEvent("fcm_token_saved", android.os.Bundle().apply {
                putString("token_length", token.length.toString())
            })
            
        } catch (e: Exception) {
            Log.e(TAG, "Error saving FCM token: ${e.message}")
        }
    }

    /**
     * Get saved FCM token
     */
    fun getSavedFCMToken(): String? {
        return try {
            prefs.getString(KEY_FCM_TOKEN, null)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting saved FCM token: ${e.message}")
            null
        }
    }

    /**
     * Subscribe to topic
     */
    fun subscribeToTopic(topic: String, onComplete: (Boolean) -> Unit = {}) {
        try {
            FirebaseMessaging.getInstance().subscribeToTopic(topic)
                .addOnCompleteListener { task ->
                    val success = task.isSuccessful
                    if (success) {
                        Log.d(TAG, "Subscribed to topic: $topic")
                    } else {
                        Log.w(TAG, "Failed to subscribe to topic: $topic", task.exception)
                    }
                    
                    // Log to analytics
                    AnalyticsManager.logEvent("fcm_topic_subscription", android.os.Bundle().apply {
                        putString("topic", topic)
                        putString("success", success.toString())
                    })
                    
                    onComplete(success)
                }
        } catch (e: Exception) {
            Log.e(TAG, "Error subscribing to topic $topic: ${e.message}")
            onComplete(false)
        }
    }

    /**
     * Unsubscribe from topic
     */
    fun unsubscribeFromTopic(topic: String, onComplete: (Boolean) -> Unit = {}) {
        try {
            FirebaseMessaging.getInstance().unsubscribeFromTopic(topic)
                .addOnCompleteListener { task ->
                    val success = task.isSuccessful
                    if (success) {
                        Log.d(TAG, "Unsubscribed from topic: $topic")
                    } else {
                        Log.w(TAG, "Failed to unsubscribe from topic: $topic", task.exception)
                    }
                    
                    // Log to analytics
                    AnalyticsManager.logEvent("fcm_topic_unsubscription", android.os.Bundle().apply {
                        putString("topic", topic)
                        putString("success", success.toString())
                    })
                    
                    onComplete(success)
                }
        } catch (e: Exception) {
            Log.e(TAG, "Error unsubscribing from topic $topic: ${e.message}")
            onComplete(false)
        }
    }

    /**
     * Subscribe to default topics
     */
    private fun subscribeToDefaultTopics() {
        val defaultTopics = listOf(
            "general_updates",
            "app_updates",
            "feature_announcements"
        )
        
        defaultTopics.forEach { topic ->
            subscribeToTopic(topic) { success ->
                Log.d(TAG, "Default topic subscription $topic: $success")
            }
        }
    }

    /**
     * Enable/disable notifications
     */
    fun setNotificationsEnabled(enabled: Boolean) {
        try {
            prefs.edit().putBoolean(KEY_NOTIFICATIONS_ENABLED, enabled).apply()
            Log.d(TAG, "Notifications ${if (enabled) "enabled" else "disabled"}")
            
            // Log to analytics
            AnalyticsManager.logEvent("notifications_setting_changed", android.os.Bundle().apply {
                putString("enabled", enabled.toString())
            })
            
        } catch (e: Exception) {
            Log.e(TAG, "Error setting notifications enabled: ${e.message}")
        }
    }

    /**
     * Check if notifications are enabled
     */
    fun areNotificationsEnabled(): Boolean {
        return try {
            prefs.getBoolean(KEY_NOTIFICATIONS_ENABLED, true) // Default to enabled
        } catch (e: Exception) {
            Log.e(TAG, "Error checking notifications enabled: ${e.message}")
            true
        }
    }

    /**
     * Check if messaging is initialized
     */
    fun isInitialized(): Boolean {
        return isInitialized
    }

    /**
     * Log messaging status for monitoring
     */
    fun logStatus() {
        try {
            val status = StringBuilder()
            status.append("Firebase Cloud Messaging Status:\n")
            status.append("- Initialized: $isInitialized\n")
            status.append("- Notifications Enabled: ${areNotificationsEnabled()}\n")
            status.append("- Has Token: ${getSavedFCMToken() != null}\n")
            
            Log.d(TAG, status.toString())
            
        } catch (e: Exception) {
            Log.e(TAG, "Error logging messaging status: ${e.message}")
        }
    }

    /**
     * Handle messaging errors
     */
    fun handleMessagingError(error: String, context: String) {
        try {
            Log.e(TAG, "Messaging error in $context: $error")
            
            // Log to crashlytics
            CrashlyticsManager.logException(
                Exception("Messaging error: $error"), 
                "FCM failed in $context"
            )
            
            // Log to analytics
            AnalyticsManager.logError("fcm_error", error, context)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error handling messaging error: ${e.message}")
        }
    }
}
