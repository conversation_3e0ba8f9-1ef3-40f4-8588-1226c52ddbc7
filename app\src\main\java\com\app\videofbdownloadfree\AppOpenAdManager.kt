package com.app.videofbdownloadfree

import android.app.Activity
import android.content.Context
import android.util.Log
import com.google.android.gms.ads.AdError
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.FullScreenContentCallback
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.appopen.AppOpenAd
import java.util.*

/**
 * Manages the loading and showing of app open ads.
 */
class AppOpenAdManager(private val context: Context) {

    private var appOpenAd: AppOpenAd? = null
    private var isLoadingAd = false
    private var loadTime: Long = 0
    
    companion object {
        private const val TAG = "AppOpenAdManager"
        private const val TIMEOUT_DURATION = 4 * 60 * 60 * 1000L // 4 hours in milliseconds
        private const val MIN_INTERVAL_BETWEEN_ADS = 10 * 1000L // 10 seconds minimum between ads
        private var isShowingAd = false
        private var isFirstLaunchGlobal = true // Global flag to track first launch
        private var lastAdShownTime = 0L // Track when last ad was shown
    }
    
    /**
     * Load an app open ad.
     */
    fun loadAd() {
        // Do not load ad if there is an unused ad or one is already loading.
        if (isLoadingAd || isAdAvailable()) {
            Log.d(TAG, "Ad already loading or available. Status: ${getAdStatus()}")
            return
        }

        Log.d(TAG, "Starting to load app open ad...")

        isLoadingAd = true
        // Configure AdRequest for Facebook Bidding
        val requestBuilder = AdRequest.Builder()
        val request = FacebookBiddingHelper.configureAdRequestForBidding(requestBuilder).build()
        val adUnitId = AdConfig.openAppAdUnitId
        AppOpenAd.load(
            context,
            adUnitId,
            request,
            object : AppOpenAd.AppOpenAdLoadCallback() {
                override fun onAdLoaded(ad: AppOpenAd) {
                    Log.d(TAG, "App open ad loaded successfully")
                    appOpenAd = ad
                    isLoadingAd = false
                    loadTime = Date().time
                }

                override fun onAdFailedToLoad(loadAdError: LoadAdError) {
                    Log.e(TAG, "App open ad failed to load: ${loadAdError.message}")
                    isLoadingAd = false
                }
            }
        )
    }
    
    /**
     * Check if ad was loaded more than n hours ago.
     */
    private fun wasLoadTimeLessThanNHoursAgo(): Boolean {
        val dateDifference = Date().time - loadTime
        return dateDifference < TIMEOUT_DURATION
    }
    
    /**
     * Check if ad exists and can be shown.
     */
    private fun isAdAvailable(): Boolean {
        return appOpenAd != null && wasLoadTimeLessThanNHoursAgo()
    }
    
    /**
     * Show the app open ad if available.
     */
    fun showAdIfAvailable(
        activity: Activity,
        onShowAdCompleteListener: OnShowAdCompleteListener
    ) {
        // Don't show app open ad on first launch
        if (isFirstLaunchGlobal) {
            Log.d(TAG, "Skipping app open ad on first launch.")
            isFirstLaunchGlobal = false
            onShowAdCompleteListener.onShowAdComplete()
            loadAd()
            return
        }

        // Check if enough time has passed since last ad
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastAdShownTime < MIN_INTERVAL_BETWEEN_ADS) {
            Log.d(TAG, "Too soon to show another ad. Last shown: ${(currentTime - lastAdShownTime) / 1000}s ago")
            onShowAdCompleteListener.onShowAdComplete()
            return
        }

        // If the app open ad is already showing, do not show the ad again.
        if (isShowingAd) {
            Log.d(TAG, "App open ad is already showing.")
            return
        }

        // If the app open ad is not available yet, invoke the callback then load the ad.
        if (!isAdAvailable()) {
            Log.d(TAG, "App open ad is not ready yet.")
            onShowAdCompleteListener.onShowAdComplete()
            loadAd()
            return
        }

        Log.d(TAG, "Will show app open ad.")

        // Check if activity is still valid and in foreground
        if (activity.isFinishing || activity.isDestroyed) {
            Log.w(TAG, "Activity is finishing or destroyed, cannot show ad")
            onShowAdCompleteListener.onShowAdComplete()
            return
        }

        appOpenAd?.fullScreenContentCallback = object : FullScreenContentCallback() {
            override fun onAdDismissedFullScreenContent() {
                // Called when fullscreen content is dismissed.
                Log.d(TAG, "App open ad dismissed.")
                appOpenAd = null
                isShowingAd = false
                
                onShowAdCompleteListener.onShowAdComplete()
                loadAd()
            }
            
            override fun onAdFailedToShowFullScreenContent(adError: AdError) {
                // Called when fullscreen content failed to show.
                Log.e(TAG, "App open ad failed to show: ${adError.message}")
                appOpenAd = null
                isShowingAd = false
                
                onShowAdCompleteListener.onShowAdComplete()
                loadAd()
            }
            
            override fun onAdShowedFullScreenContent() {
                // Called when fullscreen content is shown.
                Log.d(TAG, "App open ad showed fullscreen content.")
                lastAdShownTime = System.currentTimeMillis()
            }
        }
        
        isShowingAd = true
        appOpenAd?.show(activity)
    }
    
    /**
     * Interface definition for a callback to be invoked when an app open ad is complete
     * (i.e. dismissed or fails to show).
     */
    interface OnShowAdCompleteListener {
        fun onShowAdComplete()
    }

    /**
     * Utility method to check if an ad is currently available for debugging purposes.
     */
    fun isAdReady(): Boolean {
        return isAdAvailable()
    }

    /**
     * Utility method to get ad load status for debugging purposes.
     */
    fun getAdStatus(): String {
        return when {
            isLoadingAd -> "Loading"
            isAdAvailable() -> "Ready"
            appOpenAd == null -> "Not Loaded"
            !wasLoadTimeLessThanNHoursAgo() -> "Expired"
            else -> "Unknown"
        }
    }
}
