<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white">


    <!-- App Bar -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        android:background="@drawable/gradient_header_background"
        android:fitsSystemWindows="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingHorizontal="10dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Recent Downloads"
                android:textSize="20sp"
                android:fontFamily="@font/poppins_bold"
                android:textColor="@android:color/white"
                android:gravity="center"/>



        </LinearLayout>

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Filter Tabs Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@android:color/white">

                <LinearLayout
                    android:id="@+id/tabLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="8dp">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnPosts"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="48dp"
                        android:text="Images"
                        android:textSize="16sp"
                        android:textAllCaps="false"
                        android:layout_marginEnd="8dp"
                        app:icon="@drawable/images"
                        app:iconSize="32dp"
                        android:background="@drawable/tab_button_background_selector"
                        android:textColor="@color/tab_button_text_color_selector"
                        style="@style/Widget.MaterialComponents.Button.TextButton" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnReels"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="48dp"
                        android:text="ٍٍVideos"
                        android:textSize="16sp"
                        android:textAllCaps="false"
                        android:layout_marginStart="8dp"
                        android:background="@drawable/tab_button_background_selector"
                        android:textColor="@color/tab_button_text_color_selector"
                        app:icon="@drawable/reels"
                        app:iconSize="32dp"
                        style="@style/Widget.MaterialComponents.Button.TextButton" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Empty State -->
            <LinearLayout
                android:id="@+id/emptyStateLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="32dp"
                android:visibility="gone">

                <!-- Empty Icon -->
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="150dp"
                    android:src="@drawable/welcome_cats"
                    android:alpha="0.8"
                    android:layout_marginBottom="16dp" />

                <!-- Empty Title -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Nothing Here!"
                    android:textSize="20sp"
                    android:fontFamily="@font/poppins_bold"
                    android:textStyle="bold"
                    android:textColor="@android:color/black"
                    android:layout_marginBottom="8dp" />

                <!-- Empty Message -->
                <TextView
                    android:id="@+id/emptyTextView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Start downloading content to see it here"
                    android:textSize="16sp"
                    android:fontFamily="@font/poppins_regular"
                    android:textColor="@android:color/darker_gray"
                    android:textAlignment="center"
                    android:alpha="0.7" />

            </LinearLayout>

            <!-- Content Grid -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/downloadsRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false"
                android:clipToPadding="false"
                android:paddingBottom="16dp"
                tools:listitem="@layout/item_media" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>
    <!-- Bottom Navigation Bar - 3 Items Only -->
    <LinearLayout
        android:id="@+id/bottomNavigation"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:background="#fff"
        android:orientation="horizontal"
        android:layout_gravity="bottom"
        android:gravity="bottom"
        android:paddingHorizontal="16dp">

        <!-- Home -->
        <LinearLayout
            android:id="@+id/navHome"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="10dp"
            android:background="@drawable/nav_item_background">

            <ImageView
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:src="@drawable/menu_home"
                app:tint="@color/gray"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Home"
                android:fontFamily="@font/poppins_bold"
                android:textSize="12sp"
                android:textColor="@color/gray"
                android:textStyle="bold"
                android:layout_marginTop="4dp" />

        </LinearLayout>

        <!-- Downloads -->
        <LinearLayout
            android:id="@+id/navDownloads"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="10dp"
            android:background="@drawable/nav_item_background">

            <ImageView
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:src="@drawable/menu_downloads"
                app:tint="@color/insta_purple" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Downloads"
                android:fontFamily="@font/poppins_regular"
                android:textSize="12sp"
                android:textColor="@color/insta_purple"
                android:layout_marginTop="4dp" />

        </LinearLayout>

        <!-- Settings -->
        <LinearLayout
            android:id="@+id/navSettings"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="10dp"
            android:background="@drawable/nav_item_background">

            <ImageView
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:src="@drawable/setting"
                app:tint="@color/gray"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Settings"
                android:textSize="12sp"
                android:textColor="@color/gray"
                android:fontFamily="@font/poppins_bold"
                android:layout_marginTop="4dp" />

        </LinearLayout>

    </LinearLayout>


</androidx.coordinatorlayout.widget.CoordinatorLayout>
