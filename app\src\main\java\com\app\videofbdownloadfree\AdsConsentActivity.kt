package com.app.videofbdownloadfree

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import com.google.android.material.button.MaterialButton

class AdsConsentActivity : BaseActivity() {

    companion object {
        private const val TAG = "AdsConsentActivity"
        private const val PREFS_NAME = "ads_consent"
        private const val KEY_ADS_CONSENT_GIVEN = "ads_consent_given"
        private const val KEY_FIRST_TIME_ADS = "first_time_ads"
        private const val PRIVACY_POLICY_URL = "https://kurodev.blogspot.com/p/fbdownloader-video-or-storie-privacy.html" // Replace with your actual URL

        fun isFirstTimeAds(context: Context): Boolean {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            return prefs.getBoolean(KEY_FIRST_TIME_ADS, true)
        }

        fun isAdsConsentGiven(context: Context): Boolean {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            return prefs.getBoolean(KEY_ADS_CONSENT_GIVEN, false)
        }

        fun shouldShowAdsConsentActivity(context: Context): Boolean {
            return isFirstTimeAds(context) || !isAdsConsentGiven(context)
        }

        fun setAdsConsentGiven(context: Context, consent: Boolean) {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            prefs.edit()
                .putBoolean(KEY_ADS_CONSENT_GIVEN, consent)
                .putBoolean(KEY_FIRST_TIME_ADS, false)
                .apply()
        }
    }

    private lateinit var btnAcceptAds: MaterialButton
    private lateinit var btnManageAds: MaterialButton
    private lateinit var tvPrivacyPolicy: TextView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_ads_consent)

        initViews()
        setupClickListeners()

        Log.d(TAG, "AdsConsentActivity created")
    }

    private fun initViews() {
        btnAcceptAds = findViewById(R.id.btnAcceptAds)
        btnManageAds = findViewById(R.id.btnManageAds)
        tvPrivacyPolicy = findViewById(R.id.tvPrivacyPolicy)
    }

    private fun setupClickListeners() {
        btnAcceptAds.setOnClickListener {
            acceptAdsConsent()
        }

        btnManageAds.setOnClickListener {
            showAdPreferencesDialog()
        }

        tvPrivacyPolicy.setOnClickListener {
            openPrivacyPolicy()
        }
    }

    private fun acceptAdsConsent() {
        Log.d(TAG, "User accepted ads consent")
        
        // Save consent
        setAdsConsentGiven(this, true)
        
        Toast.makeText(this, "Thank you! Ads consent saved.", Toast.LENGTH_LONG).show()
        
        // Navigate to next activity (PermissionsActivity or MainActivity)
        navigateToNextActivity()
    }

    private fun showAdPreferencesDialog() {
        Log.d(TAG, "User wants to manage ad preferences")
        
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("Ad Preferences")
            .setMessage("""
                Ad Personalization Options:
                
                • Personalized Ads: Ads based on your interests and activity
                • Non-Personalized Ads: Generic ads not based on your profile
                
                You can change these settings anytime in the app menu.
                
                What would you like to choose?
            """.trimIndent())
            .setPositiveButton("Personalized Ads") { _, _ ->
                setAdsConsentGiven(this, true)
                Toast.makeText(this, "Personalized ads enabled", Toast.LENGTH_SHORT).show()
                navigateToNextActivity()
            }
            .setNegativeButton("Non-Personalized Ads") { _, _ ->
                setAdsConsentGiven(this, false)
                Toast.makeText(this, "Non-personalized ads enabled", Toast.LENGTH_SHORT).show()
                navigateToNextActivity()
            }
            .setNeutralButton("Cancel", null)
            .show()
    }

    private fun openPrivacyPolicy() {
        Log.d(TAG, "Opening privacy policy")
        
        try {
            val intent = Intent(Intent.ACTION_VIEW, Uri.parse(PRIVACY_POLICY_URL))
            startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "Error opening privacy policy: ${e.message}")
            Toast.makeText(this, "Privacy policy will be available soon", Toast.LENGTH_SHORT).show()
        }
    }

    private fun navigateToNextActivity() {
        // Check if we need to show permissions activity next
        if (PermissionsActivity.shouldShowPermissionsActivity(this)) {
            val intent = Intent(this, PermissionsActivity::class.java)
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            startActivity(intent)
        } else {
            val intent = Intent(this, MainActivity::class.java)
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            startActivity(intent)
        }
        finish()
    }

    override fun onBackPressed() {
        // Show dialog asking user to make a choice
        AlertDialog.Builder(this)
            .setTitle("Ads Notice Required")
            .setMessage("Please accept or manage ad preferences to continue using the app.")
            .setPositiveButton("Accept Ads") { _, _ ->
                acceptAdsConsent()
            }
            .setNegativeButton("Manage Preferences") { _, _ ->
                showAdPreferencesDialog()
            }
            .setNeutralButton("Exit App") { _, _ ->
                finishAffinity() // Close the entire app
            }
            .show()
    }
}
