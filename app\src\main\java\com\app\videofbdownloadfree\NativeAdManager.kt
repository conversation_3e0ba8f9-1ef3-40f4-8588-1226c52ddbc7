package com.app.videofbdownloadfree

import android.content.Context
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import com.google.android.gms.ads.AdListener
import com.google.android.gms.ads.AdLoader
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.nativead.MediaView
import com.google.android.gms.ads.nativead.NativeAd
import com.google.android.gms.ads.nativead.NativeAdView

/**
 * Simplified Native Ad Manager for easy integration
 */
class NativeAdManager(private val context: Context) {
    
    companion object {
        private const val TAG = "NativeAdManager"
    }

    /**
     * Load native ad and display in container
     */
    fun loadNativeAd(
        container: ViewGroup,
        onAdLoaded: (NativeAdView) -> Unit = {},
        onAdFailedToLoad: (LoadAdError) -> Unit = {}
    ) {
        try {
            val adUnitId = AdConfig.nativeAdUnitId
            Log.d(TAG, "Loading native ad with unit ID: $adUnitId")

            val adLoader = AdLoader.Builder(context, adUnitId)
                .forNativeAd { nativeAd ->
                    try {
                        Log.d(TAG, "Native ad loaded successfully")
                        val adView = createNativeAdView(nativeAd)
                        
                        // Clear container and add ad view
                        container.removeAllViews()
                        container.addView(adView)
                        container.visibility = View.VISIBLE
                        
                        onAdLoaded(adView)
                        Log.d(TAG, "Native ad displayed successfully")
                    } catch (e: Exception) {
                        Log.e(TAG, "Error displaying native ad: ${e.message}")
                        container.visibility = View.GONE
                    }
                }
                .withAdListener(object : AdListener() {
                    override fun onAdFailedToLoad(adError: LoadAdError) {
                        Log.e(TAG, "Native ad failed to load: ${adError.message}")
                        container.visibility = View.GONE
                        onAdFailedToLoad(adError)
                    }
                    
                    override fun onAdLoaded() {
                        Log.d(TAG, "Native ad loaded callback")
                    }
                })
                .build()

            adLoader.loadAd(AdRequest.Builder().build())
            
        } catch (e: Exception) {
            Log.e(TAG, "Exception in loadNativeAd: ${e.message}")
            container.visibility = View.GONE
        }
    }

    /**
     * Create native ad view with proper binding
     */
    private fun createNativeAdView(nativeAd: NativeAd): NativeAdView {
        val adView = LayoutInflater.from(context)
            .inflate(R.layout.native_ad_layout, null) as NativeAdView

        try {
            // Bind ad components (matching the existing layout)
            val headlineView = adView.findViewById<TextView>(R.id.ad_headline)
            val bodyView = adView.findViewById<TextView>(R.id.ad_body)
            val callToActionView = adView.findViewById<Button>(R.id.ad_call_to_action)
            val iconView = adView.findViewById<ImageView>(R.id.ad_app_icon)
            val mediaView = adView.findViewById<MediaView>(R.id.ad_media)
            val advertiserView = adView.findViewById<TextView>(R.id.ad_advertiser)
            val storeView = adView.findViewById<TextView>(R.id.ad_store)
            val priceView = adView.findViewById<TextView>(R.id.ad_price)
            val starsView = adView.findViewById<android.widget.RatingBar>(R.id.ad_stars)

            // Set headline
            headlineView?.text = nativeAd.headline
            adView.headlineView = headlineView

            // Set body
            if (nativeAd.body != null) {
                bodyView?.text = nativeAd.body
                bodyView?.visibility = View.VISIBLE
            } else {
                bodyView?.visibility = View.GONE
            }
            adView.bodyView = bodyView

            // Set call to action
            if (nativeAd.callToAction != null) {
                callToActionView?.text = nativeAd.callToAction
                callToActionView?.visibility = View.VISIBLE
            } else {
                callToActionView?.visibility = View.GONE
            }
            adView.callToActionView = callToActionView

            // Set icon
            if (nativeAd.icon != null) {
                iconView?.setImageDrawable(nativeAd.icon?.drawable)
                iconView?.visibility = View.VISIBLE
            } else {
                iconView?.visibility = View.GONE
            }
            adView.iconView = iconView

            // Set media view
            if (nativeAd.mediaContent != null) {
                mediaView?.mediaContent = nativeAd.mediaContent
                mediaView?.visibility = View.VISIBLE
            } else {
                mediaView?.visibility = View.GONE
            }
            adView.mediaView = mediaView

            // Set advertiser
            if (nativeAd.advertiser != null) {
                advertiserView?.text = nativeAd.advertiser
                advertiserView?.visibility = View.VISIBLE
            } else {
                advertiserView?.visibility = View.GONE
            }
            adView.advertiserView = advertiserView

            // Set store
            if (nativeAd.store != null) {
                storeView?.text = nativeAd.store
                storeView?.visibility = View.VISIBLE
            } else {
                storeView?.visibility = View.GONE
            }
            adView.storeView = storeView

            // Set price
            if (nativeAd.price != null) {
                priceView?.text = nativeAd.price
                priceView?.visibility = View.VISIBLE
            } else {
                priceView?.visibility = View.GONE
            }
            adView.priceView = priceView

            // Set star rating
            if (nativeAd.starRating != null) {
                starsView?.rating = nativeAd.starRating!!.toFloat()
                starsView?.visibility = View.VISIBLE
            } else {
                starsView?.visibility = View.GONE
            }
            adView.starRatingView = starsView

            // Register the native ad with the native ad view
            adView.setNativeAd(nativeAd)

            Log.d(TAG, "Native ad view created and bound successfully")

        } catch (e: Exception) {
            Log.e(TAG, "Error binding native ad: ${e.message}")
        }

        return adView
    }

    /**
     * Create simple native ad view for small spaces
     */
    fun createSimpleNativeAdView(nativeAd: NativeAd): NativeAdView {
        val adView = LayoutInflater.from(context)
            .inflate(R.layout.native_ad_small_layout, null) as NativeAdView

        try {
            // Bind components for small layout (matching the actual IDs)
            val headlineView = adView.findViewById<TextView>(R.id.ad_headline)
            val bodyView = adView.findViewById<TextView>(R.id.ad_body)
            val callToActionView = adView.findViewById<Button>(R.id.ad_call_to_action)
            val iconView = adView.findViewById<ImageView>(R.id.ad_app_icon)
            val mediaView = adView.findViewById<MediaView>(R.id.ad_media)
            val advertiserView = adView.findViewById<TextView>(R.id.ad_advertiser)

            // Set headline
            headlineView?.text = nativeAd.headline
            adView.headlineView = headlineView

            // Set body
            if (nativeAd.body != null) {
                bodyView?.text = nativeAd.body
                bodyView?.visibility = View.VISIBLE
            } else {
                bodyView?.visibility = View.GONE
            }
            adView.bodyView = bodyView

            // Set call to action
            if (nativeAd.callToAction != null) {
                callToActionView?.text = nativeAd.callToAction
                callToActionView?.visibility = View.VISIBLE
            } else {
                callToActionView?.visibility = View.GONE
            }
            adView.callToActionView = callToActionView

            // Set icon
            if (nativeAd.icon != null) {
                iconView?.setImageDrawable(nativeAd.icon?.drawable)
                iconView?.visibility = View.VISIBLE
            } else {
                iconView?.visibility = View.GONE
            }
            adView.iconView = iconView

            // Set media view
            if (nativeAd.mediaContent != null) {
                mediaView?.mediaContent = nativeAd.mediaContent
                mediaView?.visibility = View.VISIBLE
            } else {
                mediaView?.visibility = View.GONE
            }
            adView.mediaView = mediaView

            // Set advertiser
            if (nativeAd.advertiser != null) {
                advertiserView?.text = nativeAd.advertiser
                advertiserView?.visibility = View.VISIBLE
            } else {
                advertiserView?.visibility = View.GONE
            }
            adView.advertiserView = advertiserView

            // Register the native ad
            adView.setNativeAd(nativeAd)

            Log.d(TAG, "Simple native ad view created successfully")

        } catch (e: Exception) {
            Log.e(TAG, "Error binding simple native ad: ${e.message}")
        }

        return adView
    }

    /**
     * Cleanup method (optional)
     */
    fun destroy() {
        Log.d(TAG, "NativeAdManager destroyed")
    }
}
