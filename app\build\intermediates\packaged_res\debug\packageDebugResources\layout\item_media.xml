<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="16dp"
    android:background="@android:color/white"
    android:layout_marginHorizontal="8dp"
    android:layout_marginVertical="6dp">

    <!-- Large Thumbnail Image -->
    <FrameLayout
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_marginEnd="16dp"
        android:background="@android:color/darker_gray">

        <ImageView
            android:id="@+id/mediaThumbnail"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop" />

        <!-- Play icon overlay for videos -->
        <ImageView
            android:id="@+id/playIcon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="center"
            android:src="@drawable/im_play"
            android:visibility="gone"
            android:tint="@android:color/white" />

        <!-- Video duration overlay -->
        <TextView
            android:id="@+id/videoDuration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|end"
            android:layout_margin="4dp"
            android:text="01"
            android:textColor="@android:color/white"
            android:textSize="10sp"
            android:padding="4dp"
            android:visibility="gone"
            android:background="@android:color/black" />

    </FrameLayout>

    <!-- Content Info -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <!-- User/Source Name with Profile -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <!-- Profile Picture -->
            <ImageView
                android:id="@+id/profileImage"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_marginEnd="12dp"
                android:scaleType="centerCrop"
                android:src="@drawable/folder"
                android:background="@color/insta_purple" />

            <!-- Download Date -->
            <TextView
                android:id="@+id/downloadDate"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="📅 Just now"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@android:color/black"
                android:maxLines="1"
                android:ellipsize="end" />

            <!-- Action Button -->
            <ImageButton
                android:id="@+id/actionButton"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_more_vert"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:tint="@android:color/darker_gray" />

        </LinearLayout>

        <!-- File Type and Size -->
        <TextView
            android:id="@+id/fileInfo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="🎬 MP4 Video • 15.2 MB"
            android:textSize="14sp"
            android:textColor="#666666"
            android:maxLines="1"
            android:ellipsize="end" />

    </LinearLayout>

</LinearLayout>
