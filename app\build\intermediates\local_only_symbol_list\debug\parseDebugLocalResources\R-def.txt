R_DEF: Internal format may change without notice
local
array reply_entries
array reply_values
color black
color btn_background_tint_selector
color gray
color green
color ic_launcher_background
color insta_deep_purple
color insta_orange
color insta_pink
color insta_purple
color insta_red
color insta_yellow
color instagram_red
color light_gray
color orange
color purple_200
color purple_500
color purple_700
color tab_button_text_color_selector
color teal_200
color teal_700
color white
color your_default_color
color your_pressed_color
color your_selected_color
dimen activity_horizontal_margin
dimen activity_vertical_margin
dimen fab_margin
dimen nav_header_height
dimen nav_header_vertical_spacing
drawable about
drawable ad_badge_background
drawable ad_container_background
drawable ad_label_background
drawable app_icon_background
drawable back
drawable bottom_nav_background
drawable btn_background_selector
drawable card_background
drawable check
drawable circle_backgound
drawable circle_background
drawable close
drawable contentpaste
drawable dialog_background
drawable download_btn
drawable download_button_selector
drawable duration_badge_background
drawable edittext_background
drawable email
drawable folder
drawable gradient_header_background
drawable gradient_icon
drawable gradient_overlay_bottom
drawable gradient_overlay_top
drawable help
drawable ic_ads
drawable ic_ads_24
drawable ic_check
drawable ic_close
drawable ic_dark_mode
drawable ic_home
drawable ic_info
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_more_vert
drawable ic_premium
drawable ic_shield
drawable ic_star_empty
drawable ic_star_filled
drawable ic_storage
drawable ic_target
drawable ic_video
drawable icon
drawable icon_no_background
drawable im_pause
drawable im_play
drawable images
drawable input_background
drawable link_icon
drawable list
drawable loading_card_background
drawable main_header_gradient
drawable media_type_icon_background
drawable menu_downloads
drawable menu_home
drawable menu_privecy_policy
drawable menu_rate_app
drawable minus
drawable nav_item_background
drawable next
drawable no_internet_image
drawable open
drawable placeholder_image
drawable play_icon_background
drawable play_pause
drawable police
drawable premium_badge_background
drawable previous
drawable progress_rounded
drawable reels
drawable rounded_background
drawable rounded_button
drawable rounded_corner_background
drawable setting
drawable skip_next
drawable soundplus
drawable star
drawable star1
drawable star2
drawable star3
drawable star4
drawable star5
drawable star_default
drawable status_background
drawable tab_button_background_selector
drawable usage1
drawable usage2
drawable usage3
drawable volume_high
drawable volume_medium
drawable welcome_cats
font poppins_bold
font poppins_regular
id StatusText
id actionButton
id adContainer
id ad_advertiser
id ad_app_icon
id ad_body
id ad_call_to_action
id ad_headline
id ad_media
id ad_price
id ad_stars
id ad_store
id appBarLayout
id appVersion
id bannerAdContainer
id bannerAdView
id bottomNavigation
id btnAcceptAds
id btnAllowPermission
id btnCloseDialog
id btnCloseDialogBottom
id btnCloseFullscreen
id btnCloseRating
id btnHelp
id btnManageAds
id btnNext
id btnPaste
id btnPlayPause
id btnPosts
id btnPrevious
id btnRate
id btnRecent
id btnReels
id btnRetry
id btnSkipPermission
id btnTopRating
id button
id cardAbout
id cardPrivacy
id centerControlsContainer
id contactUs
id description
id downloadCounter
id downloadsRecyclerView
id emptyStateLayout
id emptyTextView
id fileInfoCard
id fileInfoText
id fullscreenImage
id fullscreenVideo
id imageRate
id inputBox
id inputCard
id loadingIcon
id loadingText
id lottiesLoader
id main_scroll_view
id mediaContainer
id media_player_root
id nativeAdContainer
id navDownloads
id navHome
id navSettings
id otherApps
id playIcon
id profileImage
id progressBar
id progressCard
id rateApp
id recentDownloadsRecyclerView
id recentDownloadsSection
id sectionTitle
id seeAllButton
id star1
id star2
id star3
id star4
id star5
id storagePermissionCard
id storageStatusIcon
id tabLayout
id thumbnailImage
id titleText
id topOverlay
id tvPrivacyPolicy
id userName
id videoDuration
layout activity_ads_consent
layout activity_main
layout activity_no_internet
layout activity_permissions
layout activity_recent_downloads
layout activity_splash
layout dialog_ad_loading
layout dialog_how_to_use
layout dialog_rate_app
layout fullscreen_preview
layout item_banner_ad_square
layout item_recent_download
layout native_ad_floating
layout native_ad_grid_large
layout native_ad_layout
layout native_ad_simple
layout native_ad_small_layout
layout settings_preferences
mipmap ic_launcher
mipmap ic_launcher_foreground
mipmap ic_launcher_round
string about_url
string admob_id
string app_name
string attachment_summary_off
string attachment_summary_on
string attachment_title
string btn_no_internet
string close
string com.google.firebase.crashlytics.mapping_file_id
string description_admob
string description_storage
string download_now
string downloading
string easy_fast_secure
string facebook_app_id
string facebook_client_token
string free_downloads_5_5
string fullscreen_image
string gcm_defaultSenderId
string google_api_key
string google_app_id
string google_crash_reporting_api_key
string google_storage_bucket
string how_to_use
string mail_contact
string messages_header
string nav_header_desc
string nav_header_subtitle
string nav_header_title
string no_internet
string no_internet_description
string package_name
string paste
string paste_url_here
string preview_image
string preview_video
string privacy_url
string project_id
string reply_title
string saveit
string signature_title
string sync_header
string sync_title
string usage1
string usage2
string usage2_2
string usage3
string version_app
style CollapsedAppBarTextAppearance
style ExpandedAppBarTextAppearance
style Theme.InstaLoader
xml backup_rules
xml data_extraction_rules
xml file_paths
xml gma_ad_services_config
