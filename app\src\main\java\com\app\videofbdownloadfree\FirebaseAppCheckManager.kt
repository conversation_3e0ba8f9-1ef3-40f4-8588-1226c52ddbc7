package com.app.videofbdownloadfree

import android.content.Context
import android.util.Log
import com.google.firebase.FirebaseApp
import com.google.firebase.appcheck.FirebaseAppCheck
import com.google.firebase.appcheck.playintegrity.PlayIntegrityAppCheckProviderFactory

/**
 * Manager for Firebase App Check initialization and configuration
 * Provides security verification for Firebase services
 */
object FirebaseAppCheckManager {
    private const val TAG = "FirebaseAppCheck"
    private var isInitialized = false

    /**
     * Initialize Firebase App Check with Play Integrity
     * Call this early in Application onCreate
     */
    fun initialize(context: Context) {
        if (isInitialized) {
            Log.d(TAG, "Firebase App Check already initialized")
            return
        }

        try {
            Log.d(TAG, "Initializing Firebase App Check...")

            // Ensure Firebase is initialized first
            FirebaseApp.initializeApp(context)

            // Get Firebase App Check instance
            val firebaseAppCheck = FirebaseAppCheck.getInstance()

            // Install Play Integrity provider for production
            if (BuildConfig.DEBUG) {
                // In debug mode, you might want to use debug provider
                // For now, we'll use Play Integrity in both debug and release
                Log.d(TAG, "Debug mode: Using Play Integrity provider")
            }

            // Install Play Integrity App Check provider
            firebaseAppCheck.installAppCheckProviderFactory(
                PlayIntegrityAppCheckProviderFactory.getInstance()
            )

            isInitialized = true
            Log.d(TAG, "Firebase App Check initialized successfully with Play Integrity")

        } catch (e: Exception) {
            Log.e(TAG, "Error initializing Firebase App Check: ${e.message}")
            CrashlyticsManager.logException(e, "Firebase App Check initialization failed")
        }
    }

    /**
     * Check if Firebase App Check is initialized
     */
    fun isInitialized(): Boolean {
        return isInitialized
    }

    /**
     * Get App Check token for manual verification (if needed)
     */
    fun getAppCheckToken(forceRefresh: Boolean = false, callback: (String?) -> Unit) {
        try {
            if (!isInitialized) {
                Log.w(TAG, "Firebase App Check not initialized")
                callback(null)
                return
            }

            FirebaseAppCheck.getInstance().getAppCheckToken(forceRefresh)
                .addOnSuccessListener { result ->
                    Log.d(TAG, "App Check token retrieved successfully")
                    callback(result.token)
                }
                .addOnFailureListener { exception ->
                    Log.e(TAG, "Failed to get App Check token: ${exception.message}")
                    callback(null)
                }

        } catch (e: Exception) {
            Log.e(TAG, "Error getting App Check token: ${e.message}")
            callback(null)
        }
    }

    /**
     * Enable debug logging for App Check (debug builds only)
     */
    fun enableDebugLogging() {
        if (BuildConfig.DEBUG) {
            try {
                // Debug logging is automatically enabled in debug builds
                Log.d(TAG, "Debug logging enabled for Firebase App Check")
            } catch (e: Exception) {
                Log.e(TAG, "Error enabling debug logging: ${e.message}")
            }
        }
    }

    /**
     * Get App Check status information
     */
    fun getAppCheckStatus(): String {
        return try {
            val status = StringBuilder()
            status.append("Firebase App Check Status:\n")
            status.append("- Initialized: $isInitialized\n")
            status.append("- Provider: Play Integrity\n")
            status.append("- Debug Mode: ${BuildConfig.DEBUG}\n")
            status.toString()
        } catch (e: Exception) {
            "Error getting App Check status: ${e.message}"
        }
    }

    /**
     * Log App Check metrics for monitoring
     */
    fun logAppCheckMetrics(success: Boolean, operation: String) {
        try {
            Log.d(TAG, "App Check metrics - Operation: $operation, Success: $success")
            
            // Log to analytics
            AnalyticsManager.logEvent("app_check_metrics", android.os.Bundle().apply {
                putString("operation", operation)
                putString("success", success.toString())
                putString("provider", "play_integrity")
            })
            
        } catch (e: Exception) {
            Log.e(TAG, "Error logging App Check metrics: ${e.message}")
        }
    }

    /**
     * Handle App Check errors
     */
    fun handleAppCheckError(error: String, operation: String) {
        try {
            Log.e(TAG, "App Check error for $operation: $error")
            
            // Log to crashlytics
            CrashlyticsManager.logException(
                Exception("App Check error: $error"), 
                "App Check failed for $operation"
            )
            
            // Log to analytics
            AnalyticsManager.logError("app_check_error", error, "FirebaseAppCheckManager")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error handling App Check error: ${e.message}")
        }
    }
}
