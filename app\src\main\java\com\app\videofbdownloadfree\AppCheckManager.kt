package com.app.videofbdownloadfree

import android.content.Context
import android.util.Log
import com.google.firebase.FirebaseApp
import com.google.firebase.appcheck.FirebaseAppCheck
import com.google.firebase.appcheck.playintegrity.PlayIntegrityAppCheckProviderFactory

/**
 * Manager for Firebase App Check
 * Provides security verification for Firebase services
 */
object AppCheckManager {
    private const val TAG = "AppCheckManager"
    private var isInitialized = false

    /**
     * Initialize Firebase App Check
     * Call this in Application onCreate
     */
    fun initialize(context: Context) {
        if (isInitialized) {
            Log.d(TAG, "App Check already initialized")
            return
        }

        try {
            Log.d(TAG, "Initializing Firebase App Check...")

            // Ensure Firebase is initialized first
            FirebaseApp.initializeApp(context)

            val firebaseAppCheck = FirebaseAppCheck.getInstance()

            // Use Play Integrity API for production
            firebaseAppCheck.installAppCheckProviderFactory(
                PlayIntegrityAppCheckProviderFactory.getInstance()
            )

            isInitialized = true
            Log.d(TAG, "Firebase App Check initialized successfully with Play Integrity")

        } catch (e: Exception) {
            Log.e(TAG, "Error initializing Firebase App Check: ${e.message}")
            CrashlyticsManager.logException(e, "App Check initialization failed")
        }
    }

    /**
     * Check if App Check is initialized
     */
    fun isInitialized(): Boolean {
        return isInitialized
    }

    /**
     * Get App Check token for debugging
     * Only use in debug builds
     */
    fun getAppCheckToken(onComplete: (String?) -> Unit) {
        if (BuildConfig.DEBUG) {
            try {
                FirebaseAppCheck.getInstance().getAppCheckToken(false)
                    .addOnSuccessListener { result ->
                        Log.d(TAG, "App Check token retrieved successfully")
                        onComplete(result.token)
                    }
                    .addOnFailureListener { exception ->
                        Log.e(TAG, "Failed to get App Check token: ${exception.message}")
                        onComplete(null)
                    }
            } catch (e: Exception) {
                Log.e(TAG, "Error getting App Check token: ${e.message}")
                onComplete(null)
            }
        } else {
            Log.d(TAG, "App Check token retrieval only available in debug builds")
            onComplete(null)
        }
    }

    /**
     * Enable debug mode for App Check
     * Only use in debug builds and testing
     */
    fun enableDebugMode() {
        if (BuildConfig.DEBUG) {
            try {
                Log.d(TAG, "App Check debug mode enabled")
                // Debug mode is configured through Firebase Console
                // This is just for logging purposes
            } catch (e: Exception) {
                Log.e(TAG, "Error enabling App Check debug mode: ${e.message}")
            }
        }
    }

    /**
     * Log App Check status for monitoring
     */
    fun logStatus() {
        try {
            val status = StringBuilder()
            status.append("Firebase App Check Status:\n")
            status.append("- Initialized: $isInitialized\n")
            status.append("- Provider: Play Integrity API\n")
            status.append("- Debug Mode: ${BuildConfig.DEBUG}\n")
            
            Log.d(TAG, status.toString())
            
            // Log to analytics
            AnalyticsManager.logEvent("app_check_status", android.os.Bundle().apply {
                putString("initialized", isInitialized.toString())
                putString("provider", "play_integrity")
                putString("debug_mode", BuildConfig.DEBUG.toString())
            })
            
        } catch (e: Exception) {
            Log.e(TAG, "Error logging App Check status: ${e.message}")
        }
    }

    /**
     * Handle App Check errors
     */
    fun handleAppCheckError(error: String, context: String) {
        try {
            Log.e(TAG, "App Check error in $context: $error")
            
            // Log to crashlytics
            CrashlyticsManager.logException(
                Exception("App Check error: $error"), 
                "App Check failed in $context"
            )
            
            // Log to analytics
            AnalyticsManager.logError("app_check_error", error, context)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error handling App Check error: ${e.message}")
        }
    }
}
