<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/dialog_background"
    android:padding="24dp">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">
        <!-- Close Button -->
        <ImageButton
            android:id="@+id/btnCloseRating"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_gravity="end"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_close"
            app:tint="@color/gray"
            android:layout_marginBottom="16dp" />

        <!-- Character Icon -->
        <ImageView
            android:id="@+id/imageRate"
            android:layout_width="250dp"
            android:layout_height="250dp"
            android:layout_gravity="center"
            android:src="@drawable/star_default"
            android:layout_marginBottom="16dp" />

        <!-- Title -->
        <TextView
            android:id="@+id/titleText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="We'd be grateful if you rate us!"
            android:textSize="22sp"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:gravity="center"
            android:layout_marginBottom="8dp"
            android:fontFamily="sans-serif-medium" />

        <!-- Subtitle -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="We are working hard for a better\nuser experience."
            android:textSize="15sp"
            android:textColor="@color/gray"
            android:gravity="center"
            android:lineSpacingExtra="6dp"
            android:layout_marginBottom="24dp"
            android:alpha="0.8" />

        <!-- Top Rating Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnTopRating"
            android:layout_width="match_parent"
            android:layout_height="52dp"
            android:text="Top rating for us"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@android:color/white"
            android:fontFamily="sans-serif-medium"
            app:backgroundTint="@color/orange"
            app:cornerRadius="100dp"
            android:layout_marginBottom="20dp"
            android:elevation="6dp"
            app:icon="@drawable/star"
            app:iconTint="@android:color/white"
            app:iconGravity="textStart"
            app:iconPadding="8dp"
            app:iconSize="32dp"/>

        <!-- Star Rating -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <ImageView
                android:id="@+id/star1"
                android:layout_width="44dp"
                android:layout_height="44dp"
                android:src="@drawable/ic_star_empty"
                android:layout_marginEnd="6dp"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:padding="6dp"
                android:scaleType="centerInside" />

            <ImageView
                android:id="@+id/star2"
                android:layout_width="44dp"
                android:layout_height="44dp"
                android:src="@drawable/ic_star_empty"
                android:layout_marginEnd="6dp"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:padding="6dp"
                android:scaleType="centerInside" />

            <ImageView
                android:id="@+id/star3"
                android:layout_width="44dp"
                android:layout_height="44dp"
                android:src="@drawable/ic_star_empty"
                android:layout_marginEnd="6dp"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:padding="6dp"
                android:scaleType="centerInside" />

            <ImageView
                android:id="@+id/star4"
                android:layout_width="44dp"
                android:layout_height="44dp"
                android:src="@drawable/ic_star_empty"
                android:layout_marginEnd="6dp"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:padding="6dp"
                android:scaleType="centerInside" />

            <ImageView
                android:id="@+id/star5"
                android:layout_width="44dp"
                android:layout_height="44dp"
                android:src="@drawable/ic_star_empty"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:padding="6dp"
                android:scaleType="centerInside" />

        </LinearLayout>

        <!-- Rate Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnRate"
            android:layout_width="match_parent"
            android:layout_height="52dp"
            android:text="Rate"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/white"
            app:backgroundTint="@color/gray"
            app:cornerRadius="26dp"
            android:elevation="2dp"
            app:rippleColor="@color/white" />

    </LinearLayout>

</ScrollView>
