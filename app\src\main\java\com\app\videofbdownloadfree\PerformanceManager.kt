package com.app.videofbdownloadfree

import android.util.Log
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.firebase.perf.FirebasePerformance
import com.google.firebase.perf.metrics.Trace

object PerformanceManager {
    private val TAG = "PerformanceManager"
    private val performance = FirebasePerformance.getInstance()
    private val activeTraces = mutableMapOf<String, Trace>()

    // Performance Monitoring initialization
    fun initialize() {
        try {
            performance.isPerformanceCollectionEnabled = true
            Log.d(TAG, "Firebase Performance initialized successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize Firebase Performance ${e.message}")
            FirebaseCrashlytics.getInstance().recordException(e)
        }
    }

    /**
     * Start traces
     * @param traceName
     * @return true if has successfully started
     */
    fun startTrace(traceName: String): Boolean {
        return try {
            if (activeTraces.containsKey(traceName)) {
                Log.w(TAG, "Trace $traceName is already running")
                return false
            }
            val trace = performance.newTrace(traceName)
            trace.start()
            activeTraces[traceName] = trace
            Log.d(TAG, "Started trace: $traceName")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start trace $traceName: ${e.message}")
            FirebaseCrashlytics.getInstance().recordException(e)
            false
        }
    }

    /**
     * Stop traces
     * @param traceName
     * @return true if has successfully stopped
     */
    fun stopTrace(traceName: String): Boolean {
        return try {
            val trace = activeTraces.remove(traceName)
            if (trace == null) {
                Log.w(TAG, "No active trace found with name: $traceName")
                return false
            }
            trace.stop()
            Log.d(TAG, "Stopped trace: $traceName")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to stop trace $traceName: ${e.message}")
            FirebaseCrashlytics.getInstance().recordException(e)
            false
        }
    }

    fun incrementCounter(traceName: String, counterName: String, incrementBy: Long = 1) {
        try {
            val trace = activeTraces[traceName]
            if (trace == null) {
                Log.w(TAG, "No active trace found with name: $traceName")
                return
            }
            trace.incrementMetric(counterName, incrementBy)
            Log.d(TAG, "Incremented counter $counterName in trace $traceName by $incrementBy")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to increment counter ${e.message}")
            FirebaseCrashlytics.getInstance().recordException(e)
        }
    }

    /**
     * إضافة قيمة خاصة لعملية
     * @param traceName اسم العملية
     * @param attributeName اسم الخاصية
     * @param value قيمة الخاصية
     */
    fun putAttribute(traceName: String, attributeName: String, value: String) {
        try {
            val trace = activeTraces[traceName]
            if (trace == null) {
                Log.w(TAG, "No active trace found with name: $traceName")
                return
            }

            trace.putAttribute(attributeName, value)
            Log.d(TAG, "Added attribute $attributeName=$value to trace $traceName")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to add attribute: ${e.message}")
            FirebaseCrashlytics.getInstance().recordException(e)
        }
    }

    /**
     * Record ad load performance
     * @param adType
     * @param success
     * @param loadTimeMs
     */
    fun recordAdLoadPerformance(adType: String, success: Boolean, loadTimeMs: Long) {
        try {
            val trace = performance.newTrace("ad_load_performance")
            trace.start()

            trace.putAttribute("ad_type", adType)
            trace.putAttribute("success", success.toString())
            trace.putMetric("load_time_ms", loadTimeMs)

            trace.stop()

            // تسجيل في Analytics
            AnalyticsManager.logPerformanceEvent("ad_load", loadTimeMs)

            Log.d(TAG, "Recorded ad load performance: $adType, success=$success, time=${loadTimeMs}ms")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to record ad load performance: ${e.message}")
            FirebaseCrashlytics.getInstance().recordException(e)
        }
    }

    /**
     * Record screen load time
     * @param screenName
     * @param loadTimeMs
     */
    fun recordScreenLoadTime(screenName: String, loadTimeMs: Long) {
        try {
            val trace = performance.newTrace("screen_load_time")
            trace.start()

            trace.putAttribute("screen_name", screenName)
            trace.putMetric("load_time_ms", loadTimeMs)

            trace.stop()

            // تسجيل في Analytics system
            AnalyticsManager.logPerformanceEvent("screen_load", loadTimeMs)

            Log.d(TAG, "Recorded screen load time: $screenName, time=${loadTimeMs}ms")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to record screen load time: ${e.message}")
            FirebaseCrashlytics.getInstance().recordException(e)
        }
    }

    /**
     * Record download performance
     * @param contentType
     * @param success
     * @param downloadTimeMs
     * @param fileSize
     */
    fun recordDownloadPerformance(contentType: String, success: Boolean, downloadTimeMs: Long, fileSize: Long) {
        try {
            val trace = performance.newTrace("download_performance")
            trace.start()

            trace.putAttribute("content_type", contentType)
            trace.putAttribute("success", success.toString())
            trace.putMetric("download_time_ms", downloadTimeMs)
            trace.putMetric("file_size_bytes", fileSize)

            trace.stop()

            // تسجيل في Analytics system
            AnalyticsManager.logPerformanceEvent("download", downloadTimeMs)

            Log.d(TAG, "Recorded download performance: $contentType, success=$success, time=${downloadTimeMs}ms, size=${fileSize}bytes")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to record download performance: ${e.message}")
            FirebaseCrashlytics.getInstance().recordException(e)
        }
    }

    /**
     * Record network check performance
     * @param isConnected
     * @param checkTimeMs
     */
    fun recordNetworkCheckPerformance(isConnected: Boolean, checkTimeMs: Long) {
        try {
            val trace = performance.newTrace("network_check_performance")
            trace.start()

            trace.putAttribute("is_connected", isConnected.toString())
            trace.putMetric("check_time_ms", checkTimeMs)

            trace.stop()

            Log.d(TAG, "Recorded network check performance: connected=$isConnected, time=${checkTimeMs}ms")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to record network check performance: ${e.message}")
            FirebaseCrashlytics.getInstance().recordException(e)
        }
    }
}
