package com.app.videofbdownloadfree

import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import com.google.android.material.button.MaterialButton

@Suppress("DEPRECATION")
class RatingDialog(private val context: Context) {

    companion object {
        private const val TAG = "RatingDialog"
        private const val PREFS_NAME = "rating_prefs"
        private const val KEY_RATING_SHOWN = "rating_shown"
        private const val KEY_USER_RATED = "user_rated"
        private const val KEY_SHOW_COUNT = "show_count"
        private const val SHOW_AFTER_DOWNLOADS = 3 // Show after 3 downloads

        fun shouldShowRating(context: Context): Boolean {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            val userRated = prefs.getBoolean(KEY_USER_RATED, false)
            val showCount = prefs.getInt(KEY_SHOW_COUNT, 0)
            
            return !userRated && showCount >= SHOW_AFTER_DOWNLOADS
        }

        fun incrementShowCount(context: Context) {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            val currentCount = prefs.getInt(KEY_SHOW_COUNT, 0)
            prefs.edit().putInt(KEY_SHOW_COUNT, currentCount + 1).apply()
        }

        fun markAsRated(context: Context) {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            prefs.edit().putBoolean(KEY_USER_RATED, true).apply()
        }
    }

    private var dialog: Dialog? = null
    private var selectedRating = 0
    private val stars = mutableListOf<ImageView>()
    private var titleText: TextView? = null
    private var imageView: ImageView? = null
    fun show() {
        if (dialog?.isShowing == true) return

        dialog = Dialog(context, android.R.style.Theme_Dialog)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)

        val view = LayoutInflater.from(context).inflate(R.layout.dialog_rate_app, null)
        
        initViews(view)
        setupClickListeners(view)
        
        dialog?.setContentView(view)
        dialog?.setCancelable(true)

        // Add entrance animation
        dialog?.window?.attributes?.windowAnimations = android.R.style.Animation_Dialog

        // Handle window insets for navigation bar
        setupDialogInsets(dialog, view)

        dialog?.show()

        // Animate the dialog content
        view.alpha = 0f
        view.scaleX = 0.8f
        view.scaleY = 0.8f
        view.animate()
            .alpha(1f)
            .scaleX(1f)
            .scaleY(1f)
            .setDuration(300)
            .setInterpolator(android.view.animation.OvershootInterpolator())
            .start()

        Log.d(TAG, "Rating dialog shown")
    }

    private fun initViews(view: android.view.View) {
        // Initialize title
        titleText = view.findViewById(R.id.titleText)
        imageView = view.findViewById(R.id.imageRate)
        // Initialize star views
        stars.clear()
        stars.add(view.findViewById(R.id.star1))
        stars.add(view.findViewById(R.id.star2))
        stars.add(view.findViewById(R.id.star3))
        stars.add(view.findViewById(R.id.star4))
        stars.add(view.findViewById(R.id.star5))
    }

    private fun setupClickListeners(view: android.view.View) {
        val btnClose = view.findViewById<ImageButton>(R.id.btnCloseRating)
        val btnTopRating = view.findViewById<MaterialButton>(R.id.btnTopRating)
        val btnRate = view.findViewById<MaterialButton>(R.id.btnRate)

        // Close button
        btnClose.setOnClickListener {
            dismiss()
        }

        // Top rating button (5 stars automatically)
        btnTopRating.setOnClickListener {
            selectedRating = 5
            updateStars()
            openPlayStore()
        }

        // Rate button
        btnRate.setOnClickListener {
            if (selectedRating > 0) {
                handleRating()
            } else {
                Toast.makeText(context, "Please select a rating", Toast.LENGTH_SHORT).show()
            }
        }

        // Star click listeners
        stars.forEachIndexed { index, star ->
            star.setOnClickListener {
                selectedRating = index + 1
                updateStars()
                updateRateButton(btnRate)

                // Add animation effect
                star.animate()
                    .scaleX(1.2f)
                    .scaleY(1.2f)
                    .setDuration(150)
                    .withEndAction {
                        star.animate()
                            .scaleX(1.0f)
                            .scaleY(1.0f)
                            .setDuration(150)
                            .start()
                    }
                    .start()
            }
        }
    }

    private fun updateStars() {
        stars.forEachIndexed { index, star ->
            if (index < selectedRating) {
                star.setImageResource(R.drawable.ic_star_filled)
            } else {
                star.setImageResource(R.drawable.ic_star_empty)
            }
        }

        // Update title based on rating
        titleText?.text = when (selectedRating) {
            1 -> "Oh no! What went wrong?"
            2 -> "We can do better! Please"
            3 -> "Thanks for your feedback!"
            4 -> "Great! We're glad you like it!"
            5 -> "Awesome! You're amazing!"
            else -> "We'd be grateful if you rate us!"
        }
        imageView?.setImageResource  ( when(selectedRating) {
            1 -> R.drawable.star1
            2 -> R.drawable.star2
            3 -> R.drawable.star3
            4 -> R.drawable.star4
            5 -> R.drawable.star5
            else -> R.drawable.star_default

        }
        )

    }

    private fun updateRateButton(btnRate: MaterialButton) {
        if (selectedRating > 0) {
            val starText = "⭐".repeat(selectedRating)
            when (selectedRating) {
                1 -> {
                    btnRate.backgroundTintList = android.content.res.ColorStateList.valueOf(context.getColor(R.color.gray))
                    btnRate.text = "Very Poor $starText"
                }
                2 -> {
                    btnRate.backgroundTintList = android.content.res.ColorStateList.valueOf(context.getColor(R.color.orange))
                    btnRate.text = "Poor $starText"
                }
                3 -> {
                    btnRate.backgroundTintList = android.content.res.ColorStateList.valueOf(context.getColor(R.color.orange))
                    btnRate.text = "Good $starText"
                }
                4 -> {
                    btnRate.backgroundTintList = android.content.res.ColorStateList.valueOf(context.getColor(R.color.green))
                    btnRate.text = "Very Good $starText"
                }
                5 -> {
                    btnRate.backgroundTintList = android.content.res.ColorStateList.valueOf(context.getColor(R.color.green))
                    btnRate.text = "Excellent $starText"
                }
            }
        } else {
            btnRate.backgroundTintList = android.content.res.ColorStateList.valueOf(context.getColor(R.color.gray))
            btnRate.text = "Rate"
        }
    }

    private fun handleRating() {
        when {
            selectedRating >= 4 -> {
                // High rating - direct to Play Store
                openPlayStore()
            }
            selectedRating >= 2 -> {
                // Medium rating - show feedback option
                showFeedbackOption()
            }
            else -> {
                // Low rating - show feedback form
                showFeedbackForm()
            }
        }
    }

    private fun openPlayStore() {
        try {
            val packageName = context.packageName
            val intent = Intent(Intent.ACTION_VIEW, Uri.parse("market://details?id=$packageName"))
            context.startActivity(intent)
            
            markAsRated(context)
            Toast.makeText(context, "Thank you for rating us! ⭐", Toast.LENGTH_LONG).show()
            dismiss()
            
        } catch (e: Exception) {
            Log.e(TAG, "Error opening Play Store: ${e.message}")
            Toast.makeText(context, "Unable to open Play Store", Toast.LENGTH_SHORT).show()
        }
    }

    private fun showFeedbackOption() {
        androidx.appcompat.app.AlertDialog.Builder(context)
            .setTitle("Thank you for rating!")
            .setMessage("Would you like to leave a review on the Play Store or send us feedback?")
            .setPositiveButton("Play Store Review") { _, _ ->
                openPlayStore()
            }
            .setNegativeButton("Send Feedback") { _, _ ->
                showFeedbackForm()
            }
            .setNeutralButton("Maybe Later") { _, _ ->
                dismiss()
            }
            .show()
    }

    private fun showFeedbackForm() {
        androidx.appcompat.app.AlertDialog.Builder(context)
            .setTitle("Help us improve")
            .setMessage("We'd love to hear your feedback! What can we do better?")
            .setPositiveButton("Send Email") { _, _ ->
                sendFeedbackEmail()
            }
            .setNegativeButton("Cancel") { _, _ ->
                dismiss()
            }
            .show()
    }

    private fun sendFeedbackEmail() {
        try {
            val intent = Intent(Intent.ACTION_SENDTO).apply {
                data = Uri.parse("mailto:")
                putExtra(Intent.EXTRA_EMAIL, arrayOf("<EMAIL>")) // Replace with your email
                putExtra(Intent.EXTRA_SUBJECT, "SaveIt App Feedback")
                putExtra(Intent.EXTRA_TEXT, "Rating: $selectedRating stars\n\nFeedback:\n")
            }
            context.startActivity(intent)
            
            markAsRated(context)
            dismiss()
            
        } catch (e: Exception) {
            Log.e(TAG, "Error opening email: ${e.message}")
            Toast.makeText(context, "Unable to open email app", Toast.LENGTH_SHORT).show()
        }
    }

    private fun setupDialogInsets(dialog: Dialog?, view: View) {
        dialog?.window?.let { window ->
            // Enable edge-to-edge for dialog
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.R) {
                window.setDecorFitsSystemWindows(false)
            }

            // Apply window insets to dialog content
            androidx.core.view.ViewCompat.setOnApplyWindowInsetsListener(view) { v, insets ->
                val navigationBars = insets.getInsets(androidx.core.view.WindowInsetsCompat.Type.navigationBars())

                // Add bottom padding to avoid navigation bar overlap
                v.setPadding(
                    v.paddingLeft,
                    v.paddingTop,
                    v.paddingRight,
                    v.paddingBottom + navigationBars.bottom
                )

                insets
            }
        }
    }

    private fun dismiss() {
        dialog?.dismiss()
        dialog = null

        // Mark that rating dialog was shown
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit().putBoolean(KEY_RATING_SHOWN, true).apply()
    }
}
