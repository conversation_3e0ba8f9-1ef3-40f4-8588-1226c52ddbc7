{"assets": {"requirements-x86.imy": "b04f3ee8f5e43fa3b162981b50bb72fe1acabb33", "bootstrap-native/x86/_random.cpython-38.so": "ce1c455d23504d9259ab790d42929178c6c286fa", "bootstrap-native/x86/java/chaquopy.so": "fe6d9148299d5c0165e735998d6c1ede92057064", "bootstrap-native/x86/_datetime.cpython-38.so": "5dc92735a029e4e2cb73e529f596fc948b5650fa", "bootstrap-native/armeabi-v7a/mmap.cpython-38.so": "c9d8fcde92b4f7716c20a1aef5bae1c6e13de4d6", "requirements-x86_64.imy": "b04f3ee8f5e43fa3b162981b50bb72fe1acabb33", "app.imy": "d2724c6b619b247eb81552648abb9112d0022e44", "bootstrap-native/arm64-v8a/_bz2.cpython-38.so": "9475bcc75d8bb37c1ede71744098d3664563fc95", "bootstrap-native/armeabi-v7a/math.cpython-38.so": "8f65d326f484e14619bbe223d01761dde73febce", "stdlib-armeabi-v7a.imy": "dfe833c843c830ba3414daa30093361134d0d18b", "bootstrap-native/armeabi-v7a/_random.cpython-38.so": "907da0c7d7ec2053b1d0205d70ff8320caccf624", "bootstrap-native/armeabi-v7a/zlib.cpython-38.so": "7f459cc8932f04fdc480918b3663154a27df2d9c", "bootstrap-native/x86_64/java/chaquopy.so": "16aca999202fc2e8f9c721daa3a8ce9ae5860c5c", "bootstrap-native/arm64-v8a/zlib.cpython-38.so": "255fc74e4f78b5c18e2535d2e166c1c730bc0d3f", "bootstrap-native/x86_64/_sha512.cpython-38.so": "7bb8d10ed98e6c9812fa0c052363c2ac549811f3", "requirements-arm64-v8a.imy": "b04f3ee8f5e43fa3b162981b50bb72fe1acabb33", "bootstrap-native/arm64-v8a/java/chaquopy.so": "4329280ef245bc300a792ebbe11236740947c9d2", "bootstrap-native/x86/math.cpython-38.so": "1db56e29c82680883e9fcfb697140a02b51c979d", "bootstrap-native/x86/_bz2.cpython-38.so": "a1cd5a5f44cc47fcab810e397d5fb4b4ef372058", "bootstrap-native/x86_64/_ctypes.cpython-38.so": "ea64807ac243049328aee80c734f3c3743446690", "bootstrap-native/armeabi-v7a/_sha512.cpython-38.so": "da8f5feca209be44d07352e81d2e6d0e52a72ffe", "bootstrap-native/x86_64/math.cpython-38.so": "cf0472b559e08d430a294d7e359cc6cbca59d6b2", "bootstrap-native/x86_64/mmap.cpython-38.so": "d98d357e1b4ed4845a714f0caad5bb9f757ee4ac", "bootstrap-native/x86_64/_bz2.cpython-38.so": "e529f498f9fa94efa75831e6998ca8abb72ba3cc", "bootstrap-native/armeabi-v7a/_struct.cpython-38.so": "cd5aef9b74daf82e525918f488525e9e3cd85628", "stdlib-x86_64.imy": "c4d17a3acd94e992db2bc3d3e384c786eb4de087", "bootstrap-native/x86_64/_datetime.cpython-38.so": "5fee3d3c4da48c6331cb76689e2af6504cc0f28f", "bootstrap-native/x86/_struct.cpython-38.so": "3e93309ea694d9d2d3cb29f1472fcf5522a1208a", "requirements-common.imy": "c47274e0c77a13e97d3d6c675dead8431a3145dc", "bootstrap-native/armeabi-v7a/_datetime.cpython-38.so": "c1bc3fad4c33536f83cb75e3d3f8e5c43c1602e8", "stdlib-x86.imy": "5ed9482f6595a719f4a1cca782f2d6294919e40f", "bootstrap-native/arm64-v8a/_ctypes.cpython-38.so": "43e46a4302807bee04ae6bb179600ba761512677", "bootstrap-native/arm64-v8a/binascii.cpython-38.so": "0c23d2ece7bcfef4b980f20e345a586b607e3e7c", "bootstrap-native/x86_64/_random.cpython-38.so": "aa93d81710db4814a4da76d0a29a24dcf10c470c", "bootstrap-native/arm64-v8a/_sha512.cpython-38.so": "0938f3a454a402d265c72f65928d4999a824e0ae", "bootstrap-native/arm64-v8a/_struct.cpython-38.so": "a34f7bee8fb42be3b37710f5ce6c1bf86c2b2572", "bootstrap-native/x86/mmap.cpython-38.so": "54e80cf37721eb9ef176cc9b02d2fcba52f5dfda", "bootstrap-native/x86_64/_lzma.cpython-38.so": "400c0acd426b3f8786e8c9abf88f80d05bef3c46", "stdlib-common.imy": "ec4bcab93b4d13e3c9f2a4066c6cd3c375b01561", "bootstrap-native/armeabi-v7a/_ctypes.cpython-38.so": "c78800deffc3c3c437f5323098337c9b6e3f7840", "bootstrap-native/armeabi-v7a/java/chaquopy.so": "d3ee7c0226795d6a4d075484599f7dd7fd342b64", "bootstrap-native/arm64-v8a/_datetime.cpython-38.so": "95783eeed7263f856bd6c8330a81a53fea0e2c39", "stdlib-arm64-v8a.imy": "94d4123763a315317c186f8b104f2e62358ef1d8", "bootstrap-native/x86_64/binascii.cpython-38.so": "4fe4ef9336f006b28f2a557ff9cbae234dac8114", "bootstrap-native/x86/_sha512.cpython-38.so": "82f10d27eb052179dacf891c2f9fea3829857a5c", "bootstrap-native/arm64-v8a/_lzma.cpython-38.so": "cc8c2b0129c119d0324654aff40cea2185dfcc9c", "bootstrap-native/x86/_lzma.cpython-38.so": "268c7795a1adfa14303366bf02952284c05ba25a", "bootstrap.imy": "3d100ce273dfea3c3edfca85cabcd0f928d5df80", "bootstrap-native/armeabi-v7a/_lzma.cpython-38.so": "d708d03343ecbc44833f4493c9c328ebc7dd1c98", "bootstrap-native/arm64-v8a/math.cpython-38.so": "79fae77715f53c727e8311d5a486ce5409f6edaf", "bootstrap-native/armeabi-v7a/_bz2.cpython-38.so": "aeb9b3bdf097940fd753604ce9421a4fd12f457d", "bootstrap-native/x86/zlib.cpython-38.so": "d558f25cb661ef75d08b4b67e3c53d396bbc2399", "bootstrap-native/x86/_ctypes.cpython-38.so": "aeaecf61c89b40fced10b029c9316a41a84148ec", "cacert.pem": "2c68212e96605210eddf740291862bdf59398aef", "bootstrap-native/armeabi-v7a/binascii.cpython-38.so": "2ecd3a3e69ed31b32e521220698e918adb81bdd1", "bootstrap-native/arm64-v8a/mmap.cpython-38.so": "8ad998193a2c3b48292b7938aaf647e21724e555", "requirements-armeabi-v7a.imy": "b04f3ee8f5e43fa3b162981b50bb72fe1acabb33", "bootstrap-native/x86/binascii.cpython-38.so": "4ae076b1e7101ebb9e73e39f10432338184c4b2d", "bootstrap-native/x86_64/zlib.cpython-38.so": "0c6902e59a97f8b8eb5ef78e7a5418fa929cafc5", "bootstrap-native/x86_64/_struct.cpython-38.so": "caa6f4d3c160ce2d25c808524c2070bddd79ff0d", "bootstrap-native/arm64-v8a/_random.cpython-38.so": "addc721256ce87d7198b3bd12c682ef0359fa5e3"}, "extract_packages": [], "python_version": "3.8"}