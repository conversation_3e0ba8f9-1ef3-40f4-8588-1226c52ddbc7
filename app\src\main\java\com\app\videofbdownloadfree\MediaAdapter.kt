package com.app.videofbdownloadfree

import android.content.Context
import android.graphics.Bitmap
import android.net.Uri
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import java.io.File

class MediaAdapter(
    private val context: Context,
    private var mediaFiles: List<File>,
    private val onItemClick: (File) -> Unit
) : RecyclerView.Adapter<MediaAdapter.MediaViewHolder>() {

    // Glide request options for better performance
    private val glideOptions = RequestOptions()
        .diskCacheStrategy(DiskCacheStrategy.ALL)
        .skipMemoryCache(false)
        .override(200, 200) // Fixed size for better performance

    inner class MediaViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val thumbnail: ImageView = itemView.findViewById(R.id.mediaThumbnail)
        val playIcon: ImageView = itemView.findViewById(R.id.playIcon)
        val videoDuration: android.widget.TextView = itemView.findViewById(R.id.videoDuration)
        val profileImage: ImageView = itemView.findViewById(R.id.profileImage)
        val downloadDate: android.widget.TextView = itemView.findViewById(R.id.downloadDate)
        val fileInfo: android.widget.TextView = itemView.findViewById(R.id.fileInfo)
        val actionButton: ImageView = itemView.findViewById(R.id.actionButton)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MediaViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_media, parent, false)
        return MediaViewHolder(view)
    }

    override fun getItemCount(): Int = mediaFiles.size

    override fun onBindViewHolder(holder: MediaViewHolder, position: Int) {
        val file = mediaFiles[position]

        // Clear previous image to prevent recycling issues
        holder.thumbnail.setImageDrawable(null)

        // Load thumbnail with Glide
        Glide.with(context)
            .load(Uri.fromFile(file))
            .apply(glideOptions)
            .centerCrop()
            .into(holder.thumbnail)

        // Set download date
        holder.downloadDate.text = formatDownloadDate(file.lastModified())

        // Set file type and size
        holder.fileInfo.text = generateFileDescription(file)

        // Determine file type and set appropriate elements
        val isVideo = isVideoFile(file)
        if (isVideo) {
            holder.playIcon.visibility = View.VISIBLE
            holder.videoDuration.visibility = View.VISIBLE
            holder.videoDuration.text = "01"
        } else {
            holder.playIcon.visibility = View.GONE
            holder.videoDuration.visibility = View.GONE
        }

        // Load profile image
        loadProfileImage(holder.profileImage)

        // Set click listeners
        holder.itemView.setOnClickListener {
            onItemClick(file)
        }

        holder.actionButton.setOnClickListener {
            showFileActionMenu(file)
        }
    }

    // Helper functions
    private fun isVideoFile(file: File): Boolean {
        val videoExtensions = listOf("mp4", "mov", "avi", "mkv", "webm", "3gp")
        return videoExtensions.contains(file.extension.lowercase())
    }

    private fun isImageFile(file: File): Boolean {
        val extension = file.extension.lowercase()
        return extension in listOf("jpg", "jpeg", "png", "webp", "gif", "bmp", "tiff")
    }

    private fun formatDownloadDate(timestamp: Long): String {
        val now = System.currentTimeMillis()
        val diff = now - timestamp

        return when {
            diff < 60000 -> "📅 Just now"
            diff < 3600000 -> {
                val minutes = (diff / 60000).toInt()
                if (minutes == 1) "📅 1 minute ago" else "📅 ${minutes} minutes ago"
            }
            diff < 86400000 -> {
                val hours = (diff / 3600000).toInt()
                if (hours == 1) "📅 1 hour ago" else "📅 ${hours} hours ago"
            }
            diff < 604800000 -> {
                val days = (diff / 86400000).toInt()
                when (days) {
                    1 -> "📅 Yesterday"
                    2 -> "📅 2 days ago"
                    else -> "📅 ${days} days ago"
                }
            }
            diff < 2592000000L -> {
                val weeks = (diff / 604800000).toInt()
                if (weeks == 1) "📅 1 week ago" else "📅 ${weeks} weeks ago"
            }
            else -> {
                val dateFormat = java.text.SimpleDateFormat("📅 MMM dd, yyyy", java.util.Locale.getDefault())
                dateFormat.format(java.util.Date(timestamp))
            }
        }
    }

    private fun generateFileDescription(file: File): String {
        val fileType = getFileTypeWithIcon(file)
        val fileSize = formatFileSize(file.length())
        return "$fileType • $fileSize"
    }

    private fun getFileTypeWithIcon(file: File): String {
        val extension = file.extension.lowercase()
        return when (extension) {
            "mp4" -> "🎬 MP4 Video"
            "mov" -> "🎬 MOV Video"
            "avi" -> "🎬 AVI Video"
            "mkv" -> "🎬 MKV Video"
            "webm" -> "🎬 WebM Video"
            "3gp" -> "🎬 3GP Video"
            "jpg", "jpeg" -> "📷 JPEG Image"
            "png" -> "📷 PNG Image"
            "gif" -> "📷 GIF Image"
            "webp" -> "📷 WebP Image"
            "bmp" -> "📷 BMP Image"
            "tiff" -> "📷 TIFF Image"
            else -> {
                if (isVideoFile(file)) "🎬 Video File"
                else "📷 Image File"
            }
        }
    }

    private fun formatFileSize(bytes: Long): String {
        if (bytes <= 0) return "0 B"

        val kb = bytes / 1024.0
        val mb = kb / 1024.0
        val gb = mb / 1024.0

        return when {
            gb >= 1 -> String.format("%.1f GB", gb)
            mb >= 1 -> String.format("%.1f MB", mb)
            kb >= 1 -> String.format("%.1f KB", kb)
            else -> "$bytes B"
        }
    }

    private fun loadProfileImage(imageView: ImageView) {
        val colors = listOf(
            "#E91E63", "#9C27B0", "#673AB7", "#3F51B5",
            "#2196F3", "#00BCD4", "#4CAF50", "#FF9800"
        )
        val randomColor = colors.random()

        try {
            imageView.setBackgroundColor(android.graphics.Color.parseColor(randomColor))
            imageView.setImageResource(R.drawable.folder)
            imageView.scaleType = ImageView.ScaleType.CENTER
        } catch (e: Exception) {
            imageView.setImageResource(R.drawable.folder)
        }
    }

    private fun showFileActionMenu(file: File) {
        // This should be implemented in the activity/fragment that uses this adapter
        android.util.Log.d("MediaAdapter", "Action menu requested for file: ${file.name}")
    }

    /**
     * Update data (simplified)
     */
    fun updateData(newFiles: List<File>) {
        mediaFiles = newFiles
        notifyDataSetChanged()
    }
}
