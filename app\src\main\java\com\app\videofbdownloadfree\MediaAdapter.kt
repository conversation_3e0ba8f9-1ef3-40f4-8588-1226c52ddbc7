package com.app.videofbdownloadfree

import android.content.Context
import android.graphics.Bitmap
import android.net.Uri
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import java.io.File

class MediaAdapter(
    private val context: Context,
    private var mediaFiles: List<File>,
    private val onItemClick: (File) -> Unit
) : RecyclerView.Adapter<MediaAdapter.MediaViewHolder>() {

    // Glide request options for better performance
    private val glideOptions = RequestOptions()
        .diskCacheStrategy(DiskCacheStrategy.ALL)
        .skipMemoryCache(false)
        .override(200, 200) // Fixed size for better performance

    inner class MediaViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val thumbnail: ImageView = itemView.findViewById(R.id.mediaThumbnail)
        val playIconContainer: View = itemView.findViewById(R.id.playIconContainer)
        val durationBadge: android.widget.TextView = itemView.findViewById(R.id.durationBadge)
        val mediaTypeIcon: ImageView = itemView.findViewById(R.id.mediaTypeIcon)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MediaViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_media, parent, false)
        return MediaViewHolder(view)
    }

    override fun getItemCount(): Int = mediaFiles.size

    override fun onBindViewHolder(holder: MediaViewHolder, position: Int) {
        val file = mediaFiles[position]

        // Clear previous image to prevent recycling issues
        holder.thumbnail.setImageDrawable(null)

        // Load thumbnail with Glide (simplified)
        Glide.with(context)
            .load(Uri.fromFile(file))
            .apply(glideOptions)
            .centerCrop()
            .into(holder.thumbnail)

        val extension = file.extension.lowercase()
        if (extension in listOf("mp4", "mov", "avi", "mkv")) {
            // For videos
            holder.playIconContainer.visibility = View.VISIBLE
            holder.durationBadge.visibility = View.VISIBLE
            holder.mediaTypeIcon.setImageResource(android.R.drawable.ic_media_play)

            // You can add actual duration calculation here if needed
            holder.durationBadge.text = "Video"
        } else if (extension in listOf("jpg", "jpeg", "png", "webp")) {
            // For images
            holder.playIconContainer.visibility = View.GONE
            holder.durationBadge.visibility = View.GONE
            holder.mediaTypeIcon.setImageResource(android.R.drawable.ic_menu_camera)
        } else {
            // Unknown file type
            holder.playIconContainer.visibility = View.GONE
            holder.durationBadge.visibility = View.VISIBLE
            holder.durationBadge.text = "File"
            holder.mediaTypeIcon.setImageResource(android.R.drawable.ic_menu_gallery)
        }

        holder.itemView.setOnClickListener {
            onItemClick(file)
        }
    }

    /**
     * Check if file is an image
     */
    private fun isImageFile(file: File): Boolean {
        val extension = file.extension.lowercase()
        return extension in listOf("jpg", "jpeg", "png", "webp")
    }

    /**
     * Update data (simplified)
     */
    fun updateData(newFiles: List<File>) {
        mediaFiles = newFiles
        notifyDataSetChanged()
    }
}
