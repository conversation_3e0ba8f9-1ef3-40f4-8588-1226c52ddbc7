<?xml version="1.0" encoding="utf-8"?>
<!-- Square Banner Ad Item for RecyclerView -->
<com.google.android.material.card.MaterialCardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="4dp"
    android:layout_marginRight="4dp"
    android:layout_marginTop="8dp"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="6dp"
    app:cardBackgroundColor="@android:color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="8dp">

        <!-- Ad Label -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="إعلان"
            android:textSize="10sp"
            android:textColor="@android:color/darker_gray"
            android:background="@android:color/transparent"
            android:padding="4dp"
            android:layout_marginBottom="8dp"
            android:fontFamily="sans-serif-medium" />

        <!-- Banner Ad Container -->
        <FrameLayout
            android:id="@+id/bannerAdContainer"
            android:layout_width="match_parent"
            android:layout_height="100dp"
            android:layout_gravity="center"
            android:minHeight="100dp"
            android:background="@drawable/ad_container_background">

            <!-- Loading placeholder -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="100dp"
                android:orientation="vertical"
                android:gravity="center"
                android:background="@color/light_gray">

                <ImageView
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@drawable/ic_ads_24"
                    android:alpha="0.5"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="جاري تحميل الإعلان..."
                    android:textSize="12sp"
                    android:textColor="@android:color/darker_gray"
                    android:fontFamily="sans-serif" />

            </LinearLayout>

        </FrameLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
