<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="20dp">

        <!-- Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="20dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="10"
                android:text="📚 How to Use"
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="@android:color/black"
                android:gravity="center" />

            <Button
                android:id="@+id/btnCloseDialog"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:text="X"
                android:textSize="18sp"
                android:layout_weight="1"
                android:textStyle="bold"
                android:textColor="@android:color/black"
                android:background="@android:color/transparent"
                android:layout_marginStart="10dp" />

        </LinearLayout>

        <!-- Step 1 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@android:color/white">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Step 1️⃣"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="@color/insta_purple"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Go to Instagram and copy the link of the post or reel you want to download"
                    android:textSize="16sp"
                    android:textColor="#343a40"
                    android:layout_marginBottom="12dp" />

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="200dp"
                    android:src="@drawable/usage1"
                    android:scaleType="centerCrop"
                    android:background="#f8f9fa" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Step 2 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@android:color/white">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Step 2️⃣"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="@color/insta_pink"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/usage2"
                    android:textSize="16sp"
                    android:textColor="#343a40"
                    android:layout_marginBottom="12dp" />

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="200dp"
                    android:src="@drawable/usage2"
                    android:scaleType="centerCrop"
                    android:background="#f8f9fa"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/usage2_2"
                    android:textSize="14sp"
                    android:textColor="#6c757d" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Step 3 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@android:color/white">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Step 3️⃣"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="@color/insta_orange"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/usage3"
                    android:textSize="16sp"
                    android:textColor="#343a40"
                    android:layout_marginBottom="12dp" />

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="200dp"
                    android:src="@drawable/usage3"
                    android:scaleType="centerCrop"
                    android:background="#f8f9fa" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Final Note -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="#e8f5e8">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp"
                android:background="@android:color/white">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="📁 File Save Location"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="#28a745"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/description_storage"
                    android:textSize="16sp"
                    android:textColor="#155724" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Close Button -->
        <Button
            android:id="@+id/btnCloseDialogBottom"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:text="Got it"
            android:textSize="16sp"
            android:textColor="@android:color/white"
            android:background="@drawable/rounded_button"
            android:layout_marginTop="8dp" />

    </LinearLayout>

</ScrollView>
