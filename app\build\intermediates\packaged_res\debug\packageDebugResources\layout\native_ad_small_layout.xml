<?xml version="1.0" encoding="utf-8"?>
<com.google.android.gms.ads.nativead.NativeAdView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp">

    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp"
        app:cardBackgroundColor="@android:color/white">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="12dp">

            <!-- Header with icon and headline -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="8dp">

                <ImageView
                    android:id="@+id/ad_app_icon"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_marginEnd="8dp"
                    android:scaleType="centerCrop" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/ad_headline"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@android:color/black"
                        android:maxLines="1"
                        android:ellipsize="end" />

                    <TextView
                        android:id="@+id/ad_advertiser"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="11sp"
                        android:textColor="@android:color/darker_gray"
                        android:maxLines="1"
                        android:ellipsize="end" />

                </LinearLayout>

                <!-- Ad badge -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Ad"
                    android:textSize="9sp"
                    android:textColor="@android:color/white"
                    android:background="@drawable/ad_badge_background"
                    android:paddingHorizontal="4dp"
                    android:paddingVertical="1dp" />

            </LinearLayout>

            <!-- Media content (smaller) -->
            <com.google.android.gms.ads.nativead.MediaView
                android:id="@+id/ad_media"
                android:layout_width="match_parent"
                android:layout_height="120dp"
                android:layout_marginBottom="8dp" />

            <!-- Body text (shorter) -->
            <TextView
                android:id="@+id/ad_body"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:textColor="@android:color/black"
                android:maxLines="2"
                android:ellipsize="end"
                android:layout_marginBottom="8dp" />

            <!-- Call to Action -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/ad_call_to_action"
                android:layout_width="match_parent"
                android:layout_height="32dp"
                android:textSize="11sp"
                android:textAllCaps="false"
                style="@style/Widget.MaterialComponents.Button" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

</com.google.android.gms.ads.nativead.NativeAdView>
