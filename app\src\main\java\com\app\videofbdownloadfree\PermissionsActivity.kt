package com.app.videofbdownloadfree

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.provider.Settings
import android.util.Log
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import com.google.android.material.button.MaterialButton

class PermissionsActivity : BaseActivity() {

    companion object {
        private const val TAG = "PermissionsActivity"
        private const val PREFS_NAME = "app_permissions"
        private const val KEY_PERMISSIONS_GRANTED = "permissions_granted"
        private const val KEY_FIRST_TIME = "first_time"

        fun isFirstTime(context: Context): Boolean {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            return prefs.getBoolean(KEY_FIRST_TIME, true)
        }

        fun arePermissionsGranted(context: Context): Boolean {
     return when{
         // API +33
         Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU -> {
             ContextCompat.checkSelfPermission(context,Manifest.permission.READ_MEDIA_IMAGES)==
                     PackageManager.PERMISSION_GRANTED &&
                     ContextCompat.checkSelfPermission(context,Manifest.permission.READ_MEDIA_VIDEO) ==
                     PackageManager.PERMISSION_GRANTED
         }
         // API 29-32
         Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q -> {
             ContextCompat.checkSelfPermission(context, Manifest.permission.READ_EXTERNAL_STORAGE) ==
                     PackageManager.PERMISSION_GRANTED
         }
         // API > 28
         else ->{
             ContextCompat.checkSelfPermission(context,Manifest.permission.READ_EXTERNAL_STORAGE)==
                     PackageManager.PERMISSION_GRANTED &&
                     ContextCompat.checkSelfPermission(context,Manifest.permission.WRITE_EXTERNAL_STORAGE) ==
                     PackageManager.PERMISSION_GRANTED
         }
     }
        }

        fun shouldShowPermissionsActivity(context: Context): Boolean {
            return isFirstTime(context) || !arePermissionsGranted(context)
        }
    }

    private lateinit var btnAllowPermission: MaterialButton
    private lateinit var btnSkipPermission: MaterialButton

    // Permission launcher for Android 11+
    private val mediaPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.entries.all {it.value}
        if (allGranted){
            onPermissionsGranted()
        }else{
            onPermissionsDenied()
        }
    }

    // Permission launcher for older Android versions
    private val storagePermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.entries.all { it.value }
        if (allGranted) {
            onPermissionsGranted()
        } else {
            onPermissionsDenied()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_permissions)

        initViews()
        setupClickListeners()

        Log.d(TAG, "PermissionsActivity created")
    }

    private fun initViews() {
        btnAllowPermission = findViewById(R.id.btnAllowPermission)
        btnSkipPermission = findViewById(R.id.btnSkipPermission)
    }

    private fun setupClickListeners() {
        btnAllowPermission.setOnClickListener {
            requestStoragePermissions()
        }

        btnSkipPermission.setOnClickListener {
            skipPermissions()
        }
    }

    private fun requestStoragePermissions() {
        Log.d(TAG, "Requesting storage permissions")

        when {
            // API +33
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU -> {
                val permissionsNeeded = mutableListOf<String>()
                if(ContextCompat.checkSelfPermission(this, Manifest.permission.READ_MEDIA_IMAGES)
                    != PackageManager.PERMISSION_GRANTED){
                    permissionsNeeded.add(Manifest.permission.READ_MEDIA_IMAGES)
                }
                if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_MEDIA_VIDEO)
                    != PackageManager.PERMISSION_GRANTED){
                    permissionsNeeded.add(Manifest.permission.READ_MEDIA_VIDEO)
                }
                if(permissionsNeeded.isNotEmpty()){
                    mediaPermissionLauncher.launch(permissionsNeeded.toTypedArray())
                }else{
                    onPermissionsGranted()
                }
            }
            // API 29-32
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q -> {
                if(ContextCompat.checkSelfPermission(this,Manifest.permission.READ_EXTERNAL_STORAGE)
                    != PackageManager.PERMISSION_GRANTED){
                    storagePermissionLauncher.launch(arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE))
                }else{
                    onPermissionsGranted()
                }
            }
            // API > 28
            else -> {
                val permissionsNeeded = mutableListOf<String>()
                // Check Read Permission
                if(ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE)
                    != PackageManager.PERMISSION_GRANTED){
                    permissionsNeeded.add(Manifest.permission.READ_EXTERNAL_STORAGE)
                }
                if(ContextCompat.checkSelfPermission(this,Manifest.permission.WRITE_EXTERNAL_STORAGE)
                    != PackageManager.PERMISSION_GRANTED){
                    permissionsNeeded.add(Manifest.permission.WRITE_EXTERNAL_STORAGE)
                }
                if(permissionsNeeded.isNotEmpty()){
                    storagePermissionLauncher.launch(permissionsNeeded.toTypedArray())
                }else{
                    onPermissionsGranted()
                }
            }
        }
    }

    private fun onPermissionsGranted() {
        Log.d(TAG, "Storage permissions granted")
        
        // Save that permissions are granted and it's not first time anymore
        val prefs = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit()
            .putBoolean(KEY_PERMISSIONS_GRANTED, true)
            .putBoolean(KEY_FIRST_TIME, false)
            .apply()

        Toast.makeText(this, "Storage access granted!.", Toast.LENGTH_LONG).show()
        
        // Navigate to MainActivity
        navigateToMainActivity()
    }

    private fun onPermissionsDenied() {
        Log.d(TAG, "Storage permissions denied")
        
        // Save that it's not first time anymore (even if denied)
        val prefs = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit()
            .putBoolean(KEY_PERMISSIONS_GRANTED, false)
            .putBoolean(KEY_FIRST_TIME, false)
            .apply()

        Toast.makeText(this, "Storage access denied.", Toast.LENGTH_SHORT).show()
        
        // Still navigate to MainActivity but with limited functionality
        navigateToMainActivity()
    }

    private fun skipPermissions() {
        Log.d(TAG, "User skipped permissions")
        
        // Save that it's not first time anymore
        val prefs = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit()
            .putBoolean(KEY_PERMISSIONS_GRANTED, false)
            .putBoolean(KEY_FIRST_TIME, false)
            .apply()

        // Navigate to MainActivity
        navigateToMainActivity()
    }

    private fun navigateToMainActivity() {
        val intent = Intent(this, MainActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish()
    }

    override fun onBackPressed() {
        // Prevent going back, force user to make a choice
        Toast.makeText(this, "Please choose an option to continue", Toast.LENGTH_SHORT).show()
    }
}
