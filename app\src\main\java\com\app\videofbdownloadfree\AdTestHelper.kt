package com.app.videofbdownloadfree

import android.content.Context
import android.util.Log
import android.widget.Toast

/**
 * Helper class for testing and debugging App Open Ads
 */
object AdTestHelper {
    
    private const val TAG = "AdTestHelper"
    
    /**
     * Show current ad status as a Toast and log it
     */
    fun showAdStatus(context: Context) {
        val app = context.applicationContext as? MyApplication
        val adManager = app?.getAppOpenAdManager()
        
        val status = if (adManager != null) {
            "Ad Status: ${adManager.getAdStatus()}\nAd Ready: ${adManager.isAdReady()}"
        } else {
            "AppOpenAdManager not found"
        }
        
        Log.d(TAG, status)
        Toast.makeText(context, status, Toast.LENGTH_LONG).show()
    }
    
    /**
     * Force load a new ad (for testing purposes)
     */
    fun forceLoadAd(context: Context) {
        val app = context.applicationContext as? MyApplication
        val adManager = app?.getAppOpenAdManager()
        
        if (adManager != null) {
            Log.d(TAG, "Force loading new ad...")
            adManager.loadAd()
            Toast.makeText(context, "Loading new ad...", Toast.LENGTH_SHORT).show()
        } else {
            Log.w(TAG, "AppOpenAdManager not found")
            Toast.makeText(context, "AdManager not found", Toast.LENGTH_SHORT).show()
        }
    }
    
    /**
     * Show debug information about the app lifecycle
     */
    fun showDebugInfo(context: Context) {
        val debugInfo = """
            App Open Ads Debug Info:
            - Check Logcat for 'AppOpenAdManager' and 'MyApplication' tags
            - Test ads should show after backgrounding and returning to app
            - First launch: No ad (by design)
            - Ad timeout: 4 hours
            - Current implementation: ${getImplementationType(context)}
        """.trimIndent()
        
        Log.d(TAG, debugInfo)
        Toast.makeText(context, "Debug info logged - check Logcat", Toast.LENGTH_LONG).show()
    }
    
    private fun getImplementationType(context: Context): String {
        return when (context.applicationContext::class.java.simpleName) {
            "MyApplication" -> "ProcessLifecycleOwner (with dependencies)"
            "MyApplicationSimple" -> "ActivityLifecycleCallbacks (no extra dependencies) - CURRENT"
            else -> "Unknown"
        }
    }
}
