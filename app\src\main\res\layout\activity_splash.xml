<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center_horizontal"
    android:background="@color/insta_purple">

    <!-- App Logo -->
    <ImageView
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:src="@drawable/icon_no_background"
        android:layout_marginTop="164dp"
        android:layout_marginBottom="24dp" />

    <!-- App Name -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="SaveIt Instagram"
        android:textSize="32sp"
        android:textStyle="bold"
        android:textColor="@android:color/white"
        android:layout_marginBottom="8dp" />

    <!-- App Description -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/easy_fast_secure"
        android:textSize="16sp"
        android:textColor="@android:color/white"
        android:layout_marginBottom="8dp" />

    <!-- Spacer to push Lottie to bottom -->
    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
    />

    <!-- Loading Animation at Bottom -->
    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/lottiesLoader"
        android:layout_width="wrap_content"
        android:layout_height="120dp"
        android:layout_gravity="center"
        app:lottie_fileName="loader_bar.json"
        app:lottie_autoPlay="true"
        app:lottie_loop="true"
        android:layout_marginBottom="32dp" />

</LinearLayout>
