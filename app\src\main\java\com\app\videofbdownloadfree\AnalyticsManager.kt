package com.app.videofbdownloadfree

import android.content.Context
import android.os.Bundle
import android.util.Log
import com.google.firebase.analytics.FirebaseAnalytics

/**
 * Manager for Firebase Analytics
 */
object AnalyticsManager {
    
    private const val TAG = "AnalyticsManager"
    private var firebaseAnalytics: FirebaseAnalytics? = null
    
    /**
     * Initialize Firebase Analytics
     */
    fun initialize(context: Context) {
        try {
            firebaseAnalytics = FirebaseAnalytics.getInstance(context)
            Log.d(TAG, "Firebase Analytics initialized successfully")
            
            // Log app start event
            logEvent("app_start", Bundle().apply {
                putString("platform", "android")
                putLong("timestamp", System.currentTimeMillis())
            })
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize Firebase Analytics: ${e.message}")
        }
    }
    
    /**
     * Log a custom event
     */
    fun logEvent(eventName: String, parameters: Bundle? = null) {
        try {
            firebaseAnalytics?.logEvent(eventName, parameters)
            Log.d(TAG, "Event logged: $eventName")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to log event $eventName: ${e.message}")
        }
    }
    
    /**
     * Set user property
     */
    fun setUserProperty(name: String, value: String) {
        try {
            firebaseAnalytics?.setUserProperty(name, value)
            Log.d(TAG, "User property set: $name = $value")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to set user property: ${e.message}")
        }
    }
    
    /**
     * Set user ID
     */
    fun setUserId(userId: String) {
        try {
            firebaseAnalytics?.setUserId(userId)
            Log.d(TAG, "User ID set: $userId")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to set user ID: ${e.message}")
        }
    }
    
    /**
     * Log app open event
     */
    fun logAppOpen() {
        logEvent(FirebaseAnalytics.Event.APP_OPEN, Bundle().apply {
            putLong("timestamp", System.currentTimeMillis())
        })
    }
    
    /**
     * Log screen view
     */
    fun logScreenView(screenName: String, screenClass: String) {
        logEvent(FirebaseAnalytics.Event.SCREEN_VIEW, Bundle().apply {
            putString(FirebaseAnalytics.Param.SCREEN_NAME, screenName)
            putString(FirebaseAnalytics.Param.SCREEN_CLASS, screenClass)
        })
    }
    
    /**
     * Log download event
     */
    fun logDownloadEvent(url: String, contentType: String, success: Boolean) {
        logEvent("download_content", Bundle().apply {
            putString("content_url", url)
            putString("content_type", contentType)
            putBoolean("success", success)
            putLong("timestamp", System.currentTimeMillis())
        })
    }
    
    /**
     * Log ad event
     */
    fun logAdEvent(adType: String, adEvent: String, success: Boolean) {
        val eventName = when (adEvent) {
            "load" -> "ad_load"
            "show" -> "ad_impression"
            "click" -> "ad_click"
            "close" -> "ad_close"
            else -> "ad_event"
        }
        
        logEvent(eventName, Bundle().apply {
            putString("ad_type", adType)
            putString("ad_event", adEvent)
            putBoolean("success", success)
            putLong("timestamp", System.currentTimeMillis())
        })
    }
    
    /**
     * Log search event
     */
    fun logSearchEvent(searchTerm: String) {
        logEvent(FirebaseAnalytics.Event.SEARCH, Bundle().apply {
            putString(FirebaseAnalytics.Param.SEARCH_TERM, searchTerm)
            putLong("timestamp", System.currentTimeMillis())
        })
    }
    
    /**
     * Log share event
     */
    fun logShareEvent(contentType: String, itemId: String) {
        logEvent(FirebaseAnalytics.Event.SHARE, Bundle().apply {
            putString(FirebaseAnalytics.Param.CONTENT_TYPE, contentType)
            putString(FirebaseAnalytics.Param.ITEM_ID, itemId)
            putLong("timestamp", System.currentTimeMillis())
        })
    }
    
    /**
     * Log button click event
     */
    fun logButtonClick(buttonName: String, screenName: String) {
        logEvent("button_click", Bundle().apply {
            putString("button_name", buttonName)
            putString("screen_name", screenName)
            putLong("timestamp", System.currentTimeMillis())
        })
    }
    
    /**
     * Log error event
     */
    fun logError(errorType: String, errorMessage: String, screenName: String) {
        logEvent("app_error", Bundle().apply {
            putString("error_type", errorType)
            putString("error_message", errorMessage)
            putString("screen_name", screenName)
            putLong("timestamp", System.currentTimeMillis())
        })
    }
    
    /**
     * Log network event
     */
    fun logNetworkEvent(connected: Boolean) {
        logEvent("network_status", Bundle().apply {
            putBoolean("connected", connected)
            putString("status", if (connected) "connected" else "disconnected")
            putLong("timestamp", System.currentTimeMillis())
        })
    }
    
    /**
     * Log app performance event
     */
    fun logPerformanceEvent(eventType: String, duration: Long) {
        logEvent("app_performance", Bundle().apply {
            putString("event_type", eventType)
            putLong("duration_ms", duration)
            putLong("timestamp", System.currentTimeMillis())
        })
    }
    
    /**
     * Log user engagement
     */
    fun logUserEngagement(action: String, value: Long = 1) {
        logEvent("user_engagement", Bundle().apply {
            putString("engagement_action", action)
            putLong("engagement_value", value)
            putLong("timestamp", System.currentTimeMillis())
        })
    }
    
    /**
     * Log feature usage
     */
    fun logFeatureUsage(featureName: String, usageCount: Int = 1) {
        logEvent("feature_usage", Bundle().apply {
            putString("feature_name", featureName)
            putInt("usage_count", usageCount)
            putLong("timestamp", System.currentTimeMillis())
        })
    }
}
