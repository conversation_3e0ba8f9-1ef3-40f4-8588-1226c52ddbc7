<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res/auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="8dp"
    android:layout_marginVertical="6dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="3dp"
    app:cardBackgroundColor="@color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <!-- Large Thumbnail Image -->
        <androidx.cardview.widget.CardView
            android:layout_width="80dp"
            android:layout_height="80dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="0dp"
            android:layout_marginEnd="16dp">

            <ImageView
                android:id="@+id/mediaThumbnail"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:src="@drawable/placeholder_image"
                android:background="@color/light_gray" />

            <!-- Play icon overlay for videos -->
            <ImageView
                android:id="@+id/playIcon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="center"
                android:src="@drawable/im_play"
                android:visibility="gone"
                android:tint="@color/white"
                android:background="@drawable/circle_background" />

            <!-- Video duration overlay -->
            <TextView
                android:id="@+id/videoDuration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom|end"
                android:layout_margin="4dp"
                android:background="@drawable/circle_background"
                android:text="01"
                android:textColor="@color/white"
                android:textSize="10sp"
                android:padding="4dp"
                android:visibility="gone" />

        </androidx.cardview.widget.CardView>

        <!-- Content Info -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- User/Source Name with Profile -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="8dp">

                <!-- Profile Picture -->
                <androidx.cardview.widget.CardView
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="0dp"
                    android:layout_marginEnd="12dp">

                    <ImageView
                        android:id="@+id/profileImage"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:scaleType="centerCrop"
                        android:src="@drawable/folder"
                        android:background="@color/insta_purple" />

                </androidx.cardview.widget.CardView>

                <!-- Download Date -->
                <TextView
                    android:id="@+id/downloadDate"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="📅 Just now"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <!-- Action Button -->
                <ImageButton
                    android:id="@+id/actionButton"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_more_vert"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:tint="@color/gray" />

            </LinearLayout>

            <!-- File Type and Size -->
            <TextView
                android:id="@+id/fileInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="🎬 MP4 Video • 15.2 MB"
                android:textSize="14sp"
                android:textColor="#666666"
                android:maxLines="1"
                android:ellipsize="end"
                android:lineSpacingExtra="2dp" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
