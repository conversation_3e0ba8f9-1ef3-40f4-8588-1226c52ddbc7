<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="6dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="6dp"
    app:cardBackgroundColor="@android:color/white"
    android:foreground="?attr/selectableItemBackground">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <!-- Media Thumbnail -->
        <ImageView
            android:id="@+id/mediaThumbnail"
            android:layout_width="match_parent"
            android:layout_height="140dp"
            android:scaleType="centerCrop"
            android:contentDescription="media thumbnail" />

        <!-- Gradient Overlay for better text visibility -->
        <View
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:layout_gravity="bottom"
            android:background="@drawable/gradient_overlay_bottom" />

        <!-- Play Icon with Background -->
        <FrameLayout
            android:id="@+id/playIconContainer"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_gravity="center"
            android:background="@drawable/play_icon_background"
            android:visibility="gone">

            <ImageView
                android:id="@+id/playIcon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="center"
                android:src="@android:drawable/ic_media_play"
                android:tint="@android:color/white" />

        </FrameLayout>

        <!-- Duration Badge (for videos) -->
        <TextView
            android:id="@+id/durationBadge"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|end"
            android:layout_margin="8dp"
            android:background="@drawable/duration_badge_background"
            android:paddingHorizontal="8dp"
            android:paddingVertical="4dp"
            android:text="00:30"
            android:textColor="@android:color/white"
            android:textSize="12sp"
            android:textStyle="bold"
            android:visibility="gone" />

        <!-- Media Type Icon -->
        <ImageView
            android:id="@+id/mediaTypeIcon"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_gravity="top|start"
            android:layout_margin="8dp"
            android:background="@drawable/media_type_icon_background"
            android:padding="4dp"
            android:src="@android:drawable/ic_menu_camera"
            android:visibility="visible"
            android:tint="@android:color/white" />

    </FrameLayout>

</com.google.android.material.card.MaterialCardView>
