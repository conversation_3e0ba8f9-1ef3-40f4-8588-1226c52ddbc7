<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white">

    <!-- App Bar -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        android:background="@drawable/gradient_header_background"
        android:fitsSystemWindows="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingHorizontal="10dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Settings"
                android:textSize="20sp"
                android:fontFamily="@font/poppins_bold"
                android:textColor="@android:color/white"
                android:gravity="center"/>



        </LinearLayout>

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Each setting item -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/cardAbout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardBackgroundColor="@android:color/transparent"
                app:cardCornerRadius="5dp"
               >

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:src="@drawable/about"
                        app:tint="@color/insta_purple"
                        android:layout_marginEnd="10dp"/>

                    <TextView
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        android:text="About"
                        android:textSize="18sp"
                        android:fontFamily="@font/poppins_regular"
                        android:textColor="@android:color/black" />
                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/open"/>
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Privacy Policy -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/cardPrivacy"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardBackgroundColor="@android:color/transparent"
                app:cardCornerRadius="5dp"
                >

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">
                    <ImageView
                        android:layout_width="25dp"
                        android:layout_height="25dp"
                        android:src="@drawable/menu_privecy_policy"
                        android:layout_marginEnd="10dp"
                        app:tint="@color/insta_orange"/>
                    <TextView
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        android:text="Privacy Policy"
                        android:textSize="18sp"
                        android:fontFamily="@font/poppins_regular"
                        android:textColor="@android:color/black" />
                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/open"/>

                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Contact Us -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/contactUs"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="5dp"
                app:cardBackgroundColor="@android:color/transparent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">
                    <ImageView
                        android:layout_width="25dp"
                        android:layout_height="25dp"
                        android:src="@drawable/email"
                        android:layout_marginEnd="10dp"/>
                    <TextView
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        android:text="Contact Us"
                        android:textSize="18sp"
                        android:fontFamily="@font/poppins_regular"
                        android:textColor="@android:color/black" />
                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/open"/>

                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Rate Us -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/rateApp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="5dp"
               app:cardBackgroundColor="@android:color/transparent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">
                    <ImageView
                        android:layout_width="25dp"
                        android:layout_height="25dp"
                        android:src="@drawable/menu_rate_app"
                        android:layout_marginEnd="10dp"
                        app:tint="@color/insta_orange"/>
                    <TextView
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        android:text="Rate Us"
                        android:textSize="18sp"
                        android:fontFamily="@font/poppins_regular"
                        android:textColor="@android:color/black" />
                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/open"/>
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Other Apps -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/otherApps"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="5dp"
                app:cardBackgroundColor="@android:color/transparent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">
                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/list"
                        android:layout_marginEnd="10dp"/>
                    <TextView
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        android:text="Other Apps"
                        android:textSize="18sp"
                        android:fontFamily="@font/poppins_regular"
                        android:textColor="@android:color/black" />
                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/open"/>
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/storagePermissionCard"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="5dp"
                app:cardBackgroundColor="@android:color/transparent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="25dp"
                        android:layout_height="25dp"
                        android:src="@drawable/ic_storage"
                        android:layout_marginEnd="10dp"
                        app:tint="@color/insta_orange"/>

                    <TextView
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        android:text="Storage Permission"
                        android:textSize="18sp"
                        android:fontFamily="@font/poppins_regular"
                        android:textColor="@android:color/black" />

                    <ImageView
                        android:id="@+id/storageStatusIcon"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/check" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>
            <!-- App Version -->
            <TextView
                android:id="@+id/appVersion"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/version_app"
                android:textAlignment="center"
                android:fontFamily="@font/poppins_regular"
                android:textColor="@color/gray"
                android:textSize="14sp"
                android:layout_marginTop="24dp" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
    <!-- Bottom Navigation Bar - 3 Items Only -->
    <LinearLayout
        android:id="@+id/bottomNavigation"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:background="#fff"
        android:orientation="horizontal"
        android:layout_gravity="bottom"
        android:gravity="bottom"
        android:paddingHorizontal="16dp">

        <!-- Home -->
        <LinearLayout
            android:id="@+id/navHome"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="10dp"
            android:background="@drawable/nav_item_background">

            <ImageView
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:src="@drawable/menu_home"

                app:tint="@android:color/darker_gray"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Home"
                android:fontFamily="@font/poppins_bold"
                android:textSize="12sp"
                android:textColor="@android:color/darker_gray"
                android:textStyle="bold"
                android:layout_marginTop="4dp" />

        </LinearLayout>

        <!-- Downloads -->
        <LinearLayout
            android:id="@+id/navDownloads"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="10dp"
            android:background="@drawable/nav_item_background">

            <ImageView
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:src="@drawable/menu_downloads"
                app:tint="@android:color/darker_gray" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Downloads"
                android:fontFamily="@font/poppins_regular"

                android:textSize="12sp"
                android:textColor="@color/gray"
                android:layout_marginTop="4dp" />

        </LinearLayout>

        <!-- Settings -->
        <LinearLayout
            android:id="@+id/navSettings"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="10dp"
            android:background="@drawable/nav_item_background">

            <ImageView
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:src="@drawable/setting"
                app:tint="@color/insta_purple"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Settings"
                android:textSize="12sp"
                android:textColor="@color/insta_purple"
                android:fontFamily="@font/poppins_bold"
                android:layout_marginTop="4dp" />

        </LinearLayout>

    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>
