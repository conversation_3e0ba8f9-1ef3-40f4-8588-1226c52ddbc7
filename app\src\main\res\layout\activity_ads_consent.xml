<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/gradient_header_background"
    android:padding="24dp">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:minHeight="400dp">


    <!-- Header Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:layout_marginTop="40dp"
        android:layout_marginBottom="40dp">

        <!-- App Icon -->
        <ImageView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:src="@drawable/icon_no_background"
            android:layout_marginBottom="16dp" />

        <!-- Welcome Title -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Welcome to SaveIt!"
            android:textSize="28sp"
            android:textStyle="bold"
            android:textColor="@color/white"
            android:gravity="center"
            android:layout_marginBottom="8dp" />

        <!-- Subtitle -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Free Instagram content downloader"
            android:textSize="16sp"
            android:textColor="@color/white"
            android:alpha="0.9"
            android:gravity="center" />

    </LinearLayout>

    <!-- Ads Notice Card -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="16dp"
        app:cardElevation="8dp"
        android:layout_marginBottom="24dp"
        app:cardBackgroundColor="@color/white">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- Ads Icon -->
            <ImageView
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:src="@drawable/ic_ads"
                android:layout_gravity="center"
                android:layout_marginBottom="16dp"
                app:tint="@color/insta_red" />

            <!-- Ads Title -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Ads and Privacy Notice"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="@color/black"
                android:layout_gravity="center"
                android:layout_marginBottom="12dp" />

            <!-- Ads Description -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/description_admob"
                android:textSize="14sp"
                android:textColor="@color/black"
                android:alpha="0.8"
                android:gravity="center"
                android:lineSpacingExtra="4dp"
                android:layout_marginBottom="16dp" />

            <!-- Features List -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginBottom="16dp">

                <!-- Feature 1 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/ic_info"
                        android:layout_marginEnd="12dp"
                        android:layout_gravity="center_vertical"
                        app:tint="@color/insta_red" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Ads help keep this app free"
                        android:textSize="14sp"
                        android:textColor="@color/black"
                        android:alpha="0.8" />

                </LinearLayout>

                <!-- Feature 2 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/ic_shield"
                        android:layout_marginEnd="12dp"
                        android:layout_gravity="center_vertical"
                        app:tint="@color/green" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Your privacy is protected"
                        android:textSize="14sp"
                        android:textColor="@color/black"
                        android:alpha="0.8" />

                </LinearLayout>

                <!-- Feature 3 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/ic_target"
                        android:layout_marginEnd="12dp"
                        android:layout_gravity="center_vertical"
                        app:tint="@color/insta_orange" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Personalized ads for better experience"
                        android:textSize="14sp"
                        android:textColor="@color/black"
                        android:alpha="0.8" />

                </LinearLayout>

                <!-- Feature 4 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/setting"
                        android:layout_marginEnd="12dp"
                        android:layout_gravity="center_vertical"
                        app:tint="@color/insta_purple" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="You can manage ad preferences anytime"
                        android:textSize="14sp"
                        android:textColor="@color/black"
                        android:alpha="0.8" />

                </LinearLayout>

            </LinearLayout>

            <!-- Privacy Policy Link -->
            <TextView
                android:id="@+id/tvPrivacyPolicy"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="📋 Read our Privacy Policy"
                android:textSize="14sp"
                android:textColor="@color/insta_red"
                android:textStyle="bold"
                android:gravity="center"
                android:padding="8dp"
                android:background="?android:attr/selectableItemBackground"
                android:layout_marginBottom="8dp" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Spacer -->
    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <!-- Buttons -->
        <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Accept Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnAcceptAds"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="Accept • Continue"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/white"
            app:backgroundTint="@color/insta_red"
            app:cornerRadius="28dp"
            app:icon="@drawable/ic_check"
            app:iconTint="@color/white"
            android:layout_marginBottom="12dp" />

        <!-- Manage Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnManageAds"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:text="Manage Ad Preferences"
            android:textSize="14sp"
            android:textColor="@color/white"
            android:alpha="0.8"
            style="@style/Widget.Material3.Button.TextButton"
            app:rippleColor="@color/white" />

    </LinearLayout>
    </LinearLayout>
</ScrollView>
