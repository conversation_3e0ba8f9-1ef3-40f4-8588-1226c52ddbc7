package com.app.videofbdownloadfree

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.media.RingtoneManager
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage

/**
 * Firebase Cloud Messaging Service for SaveIt app
 * Handles incoming push notifications and token management
 */
class SaveItFirebaseMessagingService : FirebaseMessagingService() {

    companion object {
        private const val TAG = "FCMService"
        private const val CHANNEL_ID = "saveit_notifications"
        private const val CHANNEL_NAME = "SaveIt Notifications"
        private const val CHANNEL_DESCRIPTION = "Notifications for SaveIt app updates and features"
    }

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "Firebase Messaging Service created")
        createNotificationChannel()
    }

    /**
     * Called when a new FCM token is generated
     */
    override fun onNewToken(token: String) {
        Log.d(TAG, "New FCM token received: $token")
        
        // Send token to your server or save locally
        sendTokenToServer(token)
        
        // Save token locally for future use
        saveTokenLocally(token)
        
        // Log analytics
        AnalyticsManager.logEvent("fcm_token_refreshed", android.os.Bundle().apply {
            putString("token_length", token.length.toString())
        })
    }

    /**
     * Called when a message is received
     */
    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        Log.d(TAG, "Message received from: ${remoteMessage.from}")

        // Log message reception
        AnalyticsManager.logEvent("fcm_message_received", android.os.Bundle().apply {
            putString("from", remoteMessage.from ?: "unknown")
            putString("message_id", remoteMessage.messageId ?: "unknown")
            putString("has_data", remoteMessage.data.isNotEmpty().toString())
            putString("has_notification", (remoteMessage.notification != null).toString())
        })

        // Handle data payload
        if (remoteMessage.data.isNotEmpty()) {
            Log.d(TAG, "Message data payload: ${remoteMessage.data}")
            handleDataMessage(remoteMessage.data)
        }

        // Handle notification payload
        remoteMessage.notification?.let { notification ->
            Log.d(TAG, "Message notification: ${notification.title} - ${notification.body}")
            showNotification(
                title = notification.title ?: "SaveIt",
                body = notification.body ?: "New notification",
                data = remoteMessage.data
            )
        }
    }

    /**
     * Handle data messages (background/foreground)
     */
    private fun handleDataMessage(data: Map<String, String>) {
        try {
            val type = data["type"]
            val title = data["title"] ?: "SaveIt"
            val body = data["body"] ?: "New update available"
            
            when (type) {
                "app_update" -> {
                    showNotification(title, body, data, "update")
                    Log.d(TAG, "App update notification received")
                }
                "feature_announcement" -> {
                    showNotification(title, body, data, "feature")
                    Log.d(TAG, "Feature announcement received")
                }
                "maintenance" -> {
                    showNotification(title, body, data, "maintenance")
                    Log.d(TAG, "Maintenance notification received")
                }
                else -> {
                    showNotification(title, body, data)
                    Log.d(TAG, "General notification received")
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error handling data message: ${e.message}")
            CrashlyticsManager.logException(e, "FCM data message handling failed")
        }
    }

    /**
     * Show notification to user
     */
    private fun showNotification(
        title: String,
        body: String,
        data: Map<String, String> = emptyMap(),
        type: String = "general"
    ) {
        try {
            Log.d(TAG, "Attempting to show notification: $title")

            // Check if notifications are enabled
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            if (!notificationManager.areNotificationsEnabled()) {
                Log.w(TAG, "Notifications are disabled by user - cannot show notification")
                return
            }

            // Check notification permission for Android 13+
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                if (!NotificationPermissionManager.isNotificationPermissionGranted(this)) {
                    Log.w(TAG, "Notification permission not granted - cannot show notification")
                    return
                }
            }
            val intent = Intent(this, MainActivity::class.java).apply {
                addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                // Add data to intent if needed
                data.forEach { (key, value) ->
                    putExtra(key, value)
                }
            }

            val pendingIntent = PendingIntent.getActivity(
                this, 
                0, 
                intent, 
                PendingIntent.FLAG_ONE_SHOT or PendingIntent.FLAG_IMMUTABLE
            )

            val defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
            
            val notificationBuilder = NotificationCompat.Builder(this, CHANNEL_ID)
                .setSmallIcon(android.R.drawable.ic_dialog_info) // Use system icon for better compatibility
                .setLargeIcon(android.graphics.BitmapFactory.decodeResource(resources, R.drawable.icon))
                .setContentTitle(title)
                .setContentText(body)
                .setAutoCancel(true)
                .setSound(defaultSoundUri)
                .setContentIntent(pendingIntent)
                .setPriority(NotificationCompat.PRIORITY_HIGH) // Higher priority for better visibility
                .setStyle(NotificationCompat.BigTextStyle().bigText(body))
                .setDefaults(NotificationCompat.DEFAULT_ALL) // Add default sound, vibration, lights

            // Customize based on notification type
            when (type) {
                "update" -> {
                    notificationBuilder.setColor(getColor(R.color.insta_purple))
                }
                "feature" -> {
                    notificationBuilder.setColor(getColor(R.color.insta_pink))
                }
                "maintenance" -> {
                    notificationBuilder.setColor(getColor(R.color.orange))
                }
            }

            val notification = notificationBuilder.build()
            val notificationId = System.currentTimeMillis().toInt()

            notificationManager.notify(notificationId, notification)

            Log.d(TAG, "Notification shown successfully:")
            Log.d(TAG, "- Title: $title")
            Log.d(TAG, "- Body: $body")
            Log.d(TAG, "- Type: $type")
            Log.d(TAG, "- Notification ID: $notificationId")
            Log.d(TAG, "- Channel ID: $CHANNEL_ID")
            
            // Log analytics
            AnalyticsManager.logEvent("notification_shown", android.os.Bundle().apply {
                putString("type", type)
                putString("title", title)
            })
            
        } catch (e: Exception) {
            Log.e(TAG, "Error showing notification: ${e.message}")
            CrashlyticsManager.logException(e, "Notification display failed")
        }
    }

    /**
     * Create notification channel for Android O and above
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_HIGH // Higher importance for better visibility
            ).apply {
                description = CHANNEL_DESCRIPTION
                enableLights(true)
                lightColor = getColor(R.color.insta_purple)
                enableVibration(true)
                setShowBadge(true)
                lockscreenVisibility = android.app.Notification.VISIBILITY_PUBLIC
            }

            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)

            Log.d(TAG, "Notification channel created with HIGH importance")

            // Check if notifications are enabled
            val areNotificationsEnabled = notificationManager.areNotificationsEnabled()
            Log.d(TAG, "Notifications enabled: $areNotificationsEnabled")

            if (!areNotificationsEnabled) {
                Log.w(TAG, "Notifications are disabled by user!")
            }
        }
    }

    /**
     * Send token to your server (implement as needed)
     */
    private fun sendTokenToServer(token: String) {
        try {
            // TODO: Implement server communication
            // This is where you would send the token to your backend server
            Log.d(TAG, "Token should be sent to server: $token")
            
            // For now, just log it
            AnalyticsManager.logEvent("fcm_token_server_sync", android.os.Bundle().apply {
                putString("status", "pending")
            })
            
        } catch (e: Exception) {
            Log.e(TAG, "Error sending token to server: ${e.message}")
        }
    }

    /**
     * Save token locally for future use
     */
    private fun saveTokenLocally(token: String) {
        try {
            val sharedPrefs = getSharedPreferences("fcm_prefs", Context.MODE_PRIVATE)
            sharedPrefs.edit()
                .putString("fcm_token", token)
                .putLong("token_timestamp", System.currentTimeMillis())
                .apply()
                
            Log.d(TAG, "FCM token saved locally")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error saving token locally: ${e.message}")
        }
    }

    /**
     * Test function to show a local notification (for debugging)
     */
    fun showTestNotification() {
        Log.d(TAG, "Showing test notification...")
        showNotification(
            title = "SaveIt Test Notification",
            body = "This is a test notification to verify FCM is working correctly.",
            type = "test"
        )
    }
}
