<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <string-array name="reply_entries">
        <item>Reply</item>
        <item>Reply to all</item>
    </string-array>
    <string-array name="reply_values">
        <item>reply</item>
        <item>reply_all</item>
    </string-array>
    <color name="black">#000000</color>
    <color name="gray">#9E9E9E</color>
    <color name="green">#4CAF50</color>
    <color name="insta_deep_purple">#5851DB</color>
    <color name="insta_orange">#F56040</color>
    <color name="insta_pink">#9D00FF</color>
    <color name="insta_purple">#6200EE</color>
    <color name="insta_red">#8D61EF</color>
    <color name="insta_yellow">#FCAF45</color>
    <color name="instagram_red">#E1306C</color>
    <color name="light_gray">#F5F5F5</color>
    <color name="orange">#FF9500</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="white">#FFFFFF</color>
    <color name="your_default_color">#FFBB86FC</color>
    <color name="your_pressed_color">#FF3700B3</color>
    <color name="your_selected_color">#FF6200EE</color>
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>
    <dimen name="fab_margin">16dp</dimen>
    <dimen name="nav_header_height">176dp</dimen>
    <dimen name="nav_header_vertical_spacing">8dp</dimen>
    <string name="about_url">https://kurodev.blogspot.com/p/saveit-about.html</string>
    <string name="admob_id">ca-app-pub-1773354726031886~4208237154</string>
    <string name="app_name">SaveIt Instagram Saver</string>
    <string name="attachment_summary_off">Only download attachments when manually requested</string>
    <string name="attachment_summary_on">Automatically download attachments for incoming emails
    </string>
    <string name="attachment_title">Download incoming attachments</string>
    <string name="btn_no_internet">Check</string>
    <string name="close">Close</string>
    <string name="com.google.firebase.crashlytics.mapping_file_id" ns1:ignore="UnusedResources,TypographyDashes" translatable="false">00000000000000000000000000000000</string>
    <string name="description_admob">This app is free and supported by ads. We use personalized ads to provide you with a better experience.</string>
    <string name="description_storage">You will find all downloaded files in the SaveIt folder in your phone storage</string>
    <string name="download_now">Download Now</string>
    <string name="downloading">⏳ Downloading…</string>
    <string name="easy_fast_secure">Easy • Fast • Secure</string>
    <string name="facebook_app_id">1234567890123456</string>
    <string name="facebook_client_token">abcdef1234567890abcdef1234567890</string>
    <string name="free_downloads_5_5">Free Downloads: 5/5</string>
    <string name="fullscreen_image">Full Screen Image</string>
    <string name="gcm_defaultSenderId" translatable="false">347906987430</string>
    <string name="google_api_key" translatable="false">AIzaSyBKvoxWWvv3YV0ZBQzWAfoYX83L1MrcRKo</string>
    <string name="google_app_id" translatable="false">1:347906987430:android:3ba2c82a9bccc92138ab67</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyBKvoxWWvv3YV0ZBQzWAfoYX83L1MrcRKo</string>
    <string name="google_storage_bucket" translatable="false">saveit-28d9c.firebasestorage.app</string>
    <string name="how_to_use">How to Use</string>
    <string name="mail_contact"><EMAIL></string>
    <string name="messages_header">Messages</string>
    <string name="nav_header_desc">Navigation header</string>
    <string name="nav_header_subtitle">Instagram Downloader</string>
    <string name="nav_header_title">SaveIt</string>
    <string name="no_internet">No Internet Found</string>
    <string name="no_internet_description">Please Check your internet!</string>
    <string name="package_name">com.app.videofbdownloadfree</string>
    <string name="paste">Paste</string>
    <string name="paste_url_here">Paste URL Here...</string>
    <string name="preview_image">Preview Image</string>
    <string name="preview_video">Preview Video</string>
    <string name="privacy_url">https://kurodev.blogspot.com/p/fbdownloader-video-or-storie-privacy.html</string>
    <string name="project_id" translatable="false">saveit-28d9c</string>
    <string name="reply_title">Default reply action</string>
    <string name="saveit">SaveIt</string>
    <string name="signature_title">Your signature</string>
    <string name="sync_header">Sync</string>
    <string name="sync_title">Sync email periodically</string>
    <string name="usage1">First Go to instagram and copy link reel or post you want to download</string>
    <string name="usage2">Click In button Paste to get like from your Clipboard</string>
    <string name="usage2_2">Then click download to start downloading</string>
    <string name="usage3">Watch and Chill with your downloads In build-in Player</string>
    <string name="version_app">Version 4.5.1</string>
    <style name="CollapsedAppBarTextAppearance" parent="TextAppearance.AppCompat.Widget.ActionBar.Title">
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:shadowColor">#80000000</item>
        <item name="android:shadowDx">1</item>
        <item name="android:shadowDy">1</item>
        <item name="android:shadowRadius">2</item>
    </style>
    <style name="ExpandedAppBarTextAppearance" parent="TextAppearance.AppCompat.Display1">
        <item name="android:textSize">32sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:shadowColor">#80000000</item>
        <item name="android:shadowDx">2</item>
        <item name="android:shadowDy">2</item>
        <item name="android:shadowRadius">4</item>
    </style>
    <style name="Theme.InstaLoader" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/insta_red</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">false</item>
        
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightNavigationBar">false</item>
        
        <item name="android:windowLayoutInDisplayCutoutMode" ns1:targetApi="p">shortEdges</item>
        <item name="android:enforceNavigationBarContrast" ns1:targetApi="q">false</item>
        <item name="android:enforceStatusBarContrast" ns1:targetApi="q">false</item>
        
    </style>
</resources>