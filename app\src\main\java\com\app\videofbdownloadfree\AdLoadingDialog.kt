package com.app.videofbdownloadfree

import android.app.Dialog
import android.content.Context
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.TextView

/**
 * Smart dialog to show ad loading progress - only shows when needed
 */
class AdLoadingDialog(private val context: Context) {

    private var dialog: Dialog? = null
    private var loadingIcon: ImageView? = null
    private var loadingText: TextView? = null
    private var showHandler: Handler? = null
    private var showRunnable: Runnable? = null
    private var isShowing = false

    companion object {
        private const val SHOW_DELAY = 800L // Only show if loading takes more than 800ms
    }
    
    fun show(message: String = "Loading ad...") {
        if (isShowing) return

        // Cancel any pending show
        showHandler?.removeCallbacks(showRunnable ?: return)

        showHandler = Handler(Looper.getMainLooper())
        showRunnable = Runnable {
            if (!isShowing) {
                showImmediately(message)
            }
        }

        // Only show dialog if loading takes more than SHOW_DELAY
        showHandler?.postDelayed(showRunnable!!, SHOW_DELAY)
    }

    fun showImmediately(message: String = "Loading ad...") {
        if (isShowing) return

        try {
            dismiss() // Close any existing dialog

            dialog = Dialog(context, android.R.style.Theme_Translucent_NoTitleBar)
            val view = LayoutInflater.from(context).inflate(R.layout.dialog_ad_loading, null)

            loadingIcon = view.findViewById(R.id.loadingIcon)
            loadingText = view.findViewById(R.id.loadingText)

            loadingText?.text = message

            dialog?.setContentView(view)
            dialog?.setCancelable(false)
            dialog?.show()

            isShowing = true

        } catch (e: Exception) {
            // Ignore errors
        }
    }
    
    fun updateMessage(message: String) {
        try {
            loadingText?.text = message
        } catch (e: Exception) {
            // Ignore errors
        }
    }
    
    fun dismiss() {
        // Cancel pending show
        showHandler?.removeCallbacks(showRunnable ?: return)

        if (isShowing) {
            try {
                dialog?.dismiss()
                dialog = null
                isShowing = false
            } catch (e: Exception) {
                // Ignore errors
            }
        }

        showHandler = null
        showRunnable = null
    }

    fun isShowing(): Boolean {
        return isShowing && dialog?.isShowing == true
    }
}
