package com.app.videofbdownloadfree

import android.content.Context
import android.util.Log
import com.facebook.ads.AdSettings
import com.google.android.gms.ads.AdRequest


/**
 * Helper class for Facebook Audience Network Bidding integration with AdMob
 */
object FacebookBiddingHelper {
    private const val TAG = "FacebookBidding"

    /**
     * Configure AdRequest for Facebook Bidding
     * This ensures Facebook participates in AdMob bidding
     */
    fun configureAdRequestForBidding(builder: AdRequest.Builder): AdRequest.Builder {
        try {
            if (!AdConfig.facebookBiddingEnabled) {
                Log.d(TAG, "Facebook bidding is disabled")
                return builder
            }

            // Facebook bidding is handled automatically by AdMob mediation
            // No additional configuration needed in AdRequest
            Log.d(TAG, "AdRequest configured for Facebook bidding")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error configuring AdRequest for Facebook bidding: ${e.message}")
            CrashlyticsManager.logException(e, "Facebook bidding configuration failed")
        }
        
        return builder
    }

    /**
     * Set Facebook test mode for debugging
     * Only use in debug builds
     */
    fun setTestMode(enabled: Boolean) {
        if (BuildConfig.DEBUG) {
            try {
                AdSettings.setTestMode(enabled)
                Log.d(TAG, "Facebook test mode ${if (enabled) "enabled" else "disabled"}")
            } catch (e: Exception) {
                Log.e(TAG, "Error setting Facebook test mode: ${e.message}")
            }
        }
    }

    /**
     * Add test device for Facebook ads
     * Only use in debug builds
     */
    fun addTestDevice(deviceId: String) {
        if (BuildConfig.DEBUG) {
            try {
                AdSettings.addTestDevice(deviceId)
                Log.d(TAG, "Facebook test device added: $deviceId")
            } catch (e: Exception) {
                Log.e(TAG, "Error adding Facebook test device: ${e.message}")
            }
        }
    }

    /**
     * Check if Facebook bidding is properly configured
     */
    fun isBiddingConfigured(): Boolean {
        return try {
            AdConfig.facebookBiddingEnabled && 
            FacebookAudienceNetworkManager.isInitialized()
        } catch (e: Exception) {
            Log.e(TAG, "Error checking Facebook bidding configuration: ${e.message}")
            false
        }
    }

    /**
     * Get Facebook bidding status for logging
     */
    fun getBiddingStatus(): String {
        return try {
            val status = StringBuilder()
            status.append("Facebook Bidding Status:\n")
            status.append("- Enabled: ${AdConfig.facebookBiddingEnabled}\n")
            status.append("- Initialized: ${FacebookAudienceNetworkManager.isInitialized()}\n")
            status.append("- Test Mode: ${AdConfig.facebookTestMode}\n")
            status.toString()
        } catch (e: Exception) {
            "Error getting Facebook bidding status: ${e.message}"
        }
    }

    /**
     * Log Facebook bidding metrics
     */
    fun logBiddingMetrics(adType: String, success: Boolean, revenue: Double = 0.0) {
        try {
            if (isBiddingConfigured()) {
                Log.d(TAG, "Facebook bidding metrics - Type: $adType, Success: $success, Revenue: $revenue")
                
                // Log to analytics
                AnalyticsManager.logEvent("facebook_bidding_metrics", android.os.Bundle().apply {
                    putString("ad_type", adType)
                    putString("success", success.toString())
                    putString("revenue", revenue.toString())
                })
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error logging Facebook bidding metrics: ${e.message}")
        }
    }

    /**
     * Handle Facebook bidding errors
     */
    fun handleBiddingError(error: String, adType: String) {
        try {
            Log.e(TAG, "Facebook bidding error for $adType: $error")
            
            // Log to crashlytics
            CrashlyticsManager.logException(
                Exception("Facebook bidding error: $error"), 
                "Facebook bidding failed for $adType"
            )
            
            // Log to analytics
            AnalyticsManager.logError("facebook_bidding_error", error, "FacebookBiddingHelper")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error handling Facebook bidding error: ${e.message}")
        }
    }
}
