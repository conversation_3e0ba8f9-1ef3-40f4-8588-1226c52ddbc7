package com.app.videofbdownloadfree

import android.content.Context

object AdConfig {


    // Production Ad IDs (replace with your real IDs)
    var bannerAdUnitId: String = "ca-app-pub-1773354726031886/4647951701"
    var bannerPlayerAdUnitId: String = "ca-app-pub-1773354726031886/7787376089"
    var bannerRecentBig: String = "ca-app-pub-1773354726031886/7903744398"
    var interstitialAdUnitId: String = "ca-app-pub-1773354726031886/3143298346"
    var nativeAdUnitId: String = "ca-app-pub-1773354726031886/5976511090"
    var openAppAdUnitId: String = "ca-app-pub-1773354726031886/6159595494"
    var rewardedAdUnitId: String = "ca-app-pub-1773354726031886/8442784533"
    var admobId: String = "ca-app-pub-1773354726031886~4208237154"

    // Ad frequency settings
    const val INTERSTITIAL_FREQUENCY = 3 // Show every 3 downloads
    const val BANNER_REFRESH_INTERVAL = 120000L // 2 minutes
    const val AD_COOLDOWN_TIME = 120000L // 2 minutes between ads

    // Enable/disable ads for testing
    var adsEnabled = true
    var testMode = BuildConfig.DEBUG

    // Facebook Audience Network settings for Bidding
    var facebookBiddingEnabled = true
    var facebookTestMode = BuildConfig.DEBUG
    fun getBannerAdCode(): String {
        return bannerAdUnitId
    }

    fun isPersonalizedAdsEnabled(context: android.content.Context): Boolean {
        return AdsConsentActivity.isAdsConsentGiven(context)
    }
}