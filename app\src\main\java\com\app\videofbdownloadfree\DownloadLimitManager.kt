package com.app.videofbdownloadfree

import android.content.Context
import android.content.SharedPreferences
import android.util.Log

/**
 * Manager for download limits and rewarded ads
 */
class DownloadLimitManager(private val context: Context) {
    
    companion object {
        private const val TAG = "DownloadLimitManager"
        private const val PREFS_NAME = "download_limits"
        private const val KEY_DOWNLOADS_LEFT = "downloads_left"
        private const val KEY_LAST_RESET_TIME = "last_reset_time"
        private const val KEY_TOTAL_DOWNLOADS = "total_downloads"
        private const val MAX_FREE_DOWNLOADS = 5
        private const val DAILY_RESET_INTERVAL = 24 * 60 * 60 * 1000L // 24 hours
    }
    
    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    
    /**
     * Get remaining downloads
     */
    fun getRemainingDownloads(): Int {
        checkDailyReset()
        return prefs.getInt(KEY_DOWNLOADS_LEFT, MAX_FREE_DOWNLOADS)
    }

    /**
     * Check if daily reset is needed
     */
    private fun checkDailyReset() {
        val lastResetTime = prefs.getLong(KEY_LAST_RESET_TIME, 0)
        val currentTime = System.currentTimeMillis()

        if (currentTime - lastResetTime >= DAILY_RESET_INTERVAL) {
            // Reset daily downloads
            prefs.edit()
                .putInt(KEY_DOWNLOADS_LEFT, MAX_FREE_DOWNLOADS)
                .putLong(KEY_LAST_RESET_TIME, currentTime)
                .apply()
            Log.d(TAG, "Daily downloads reset")
        }
    }
    
    /**
     * Check if user can download
     */
    fun canDownload(): Boolean {
        val remaining = getRemainingDownloads()
        Log.d(TAG, "Remaining downloads: $remaining")
        return remaining > 0
    }
    
    /**
     * Use one download
     */
    fun useDownload(): Boolean {
        val remaining = getRemainingDownloads()
        if (remaining > 0) {
            val newRemaining = remaining - 1
            val totalDownloads = prefs.getInt(KEY_TOTAL_DOWNLOADS, 0) + 1
            prefs.edit()
                .putInt(KEY_DOWNLOADS_LEFT, newRemaining)
                .putInt(KEY_TOTAL_DOWNLOADS, totalDownloads)
                .apply()
            Log.d(TAG, "Download used. Remaining: $newRemaining, Total: $totalDownloads")
            return true
        }
        Log.d(TAG, "No downloads remaining")
        return false
    }
    
    /**
     * Add downloads after watching rewarded ad
     */
    fun addDownloadsFromReward() {
        val newRemaining = MAX_FREE_DOWNLOADS
        prefs.edit().putInt(KEY_DOWNLOADS_LEFT, newRemaining).apply()
        Log.d(TAG, "Downloads refilled to: $newRemaining")
    }
    
    /**
     * Get download status message
     */
    fun getDownloadStatusMessage(): String {
        val remaining = getRemainingDownloads()
        return when {
            remaining > 0 -> "التحميلات المتبقية: $remaining"
            else -> "شاهد إعلان للحصول على 5 تحميلات مجانية"
        }
    }
    
    /**
     * Reset downloads (for testing)
     */
    fun resetDownloads() {
        prefs.edit().putInt(KEY_DOWNLOADS_LEFT, MAX_FREE_DOWNLOADS).apply()
        Log.d(TAG, "Downloads reset to: $MAX_FREE_DOWNLOADS")
    }
    
    /**
     * Get download limit info
     */
    fun getDownloadLimitInfo(): String {
        val remaining = getRemainingDownloads()
        return "التحميلات المجانية: $remaining/$MAX_FREE_DOWNLOADS"
    }
}
