package com.app.videofbdownloadfree

import android.app.Activity
import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner

/**
 * Smart Ad Refresh Manager
 * Manages automatic refresh of all ads with intelligent timing
 */
class AdRefreshManager(private val activity: Activity) : DefaultLifecycleObserver {
    
    companion object {
        private const val TAG = "AdRefreshManager"
        private const val INTERSTITIAL_REFRESH_INTERVAL = 3 * 60 * 1000L // 3 minutes
        private const val BANNER_REFRESH_INTERVAL = 2 * 60 * 1000L // 2 minutes
        private const val APP_OPEN_REFRESH_INTERVAL = 5 * 60 * 1000L // 5 minutes
    }
    
    private var interstitialHandler: Handler? = null
    private var bannerHandler: Handler? = null
    private var appOpenHandler: Handler? = null
    
    private var interstitialRunnable: Runnable? = null
    private var bannerRunnable: Runnable? = null
    private var appOpenRunnable: Runnable? = null
    
    private var isActive = false
    private var refreshCount = 0
    
    // Ad managers
    private var interstitialAdManager: InterstitialAdManager? = null
    private var bannerAdManager: BannerAdManager? = null
    private var appOpenAdManager: AppOpenAdManager? = null
    private var mediaAdapter: MediaAdapterWithBannerAds? = null
    
    /**
     * Initialize with ad managers
     */
    fun initialize(
        interstitialManager: InterstitialAdManager? = null,
        bannerManager: BannerAdManager? = null,
        appOpenManager: AppOpenAdManager? = null,
        mediaAdapterWithBanners: MediaAdapterWithBannerAds? = null
    ) {
        this.interstitialAdManager = interstitialManager
        this.bannerAdManager = bannerManager
        this.appOpenAdManager = appOpenManager
        this.mediaAdapter = mediaAdapterWithBanners

        Log.d(TAG, "AdRefreshManager initialized")
    }
    
    /**
     * Start automatic refresh
     */
    fun startAutoRefresh() {
        if (isActive) return
        
        isActive = true
        refreshCount = 0
        
        // Start interstitial refresh
        startInterstitialRefresh()
        
        // Start banner refresh
        startBannerRefresh()
        
        // Start app open refresh
        startAppOpenRefresh()
        
        Log.d(TAG, "Auto refresh started")
        AnalyticsManager.logEvent("ad_refresh_started")
    }
    
    /**
     * Stop automatic refresh
     */
    fun stopAutoRefresh() {
        isActive = false
        
        cancelAllRefresh()
        
        Log.d(TAG, "Auto refresh stopped")
        AnalyticsManager.logEvent("ad_refresh_stopped")
    }
    
    /**
     * Start interstitial ad refresh
     */
    private fun startInterstitialRefresh() {
        interstitialHandler = Handler(Looper.getMainLooper())
        interstitialRunnable = object : Runnable {
            override fun run() {
                if (isActive && refreshCount < 20) { // Max 20 refreshes per session
                    try {
                        interstitialAdManager?.preloadAd()
                        refreshCount++
                        
                        Log.d(TAG, "Interstitial ad refreshed (count: $refreshCount)")
                        AnalyticsManager.logEvent("interstitial_auto_refresh")
                        
                        // Schedule next refresh
                        interstitialHandler?.postDelayed(this, INTERSTITIAL_REFRESH_INTERVAL)
                        
                    } catch (e: Exception) {
                        Log.e(TAG, "Failed to refresh interstitial ad: ${e.message}")
                        CrashlyticsManager.logException(e, "Interstitial auto refresh failed")
                    }
                }
            }
        }
        
        // Start first refresh after initial delay
        interstitialHandler?.postDelayed(interstitialRunnable!!, INTERSTITIAL_REFRESH_INTERVAL)
    }
    
    /**
     * Start banner ad refresh
     */
    private fun startBannerRefresh() {
        bannerHandler = Handler(Looper.getMainLooper())
        bannerRunnable = object : Runnable {
            override fun run() {
                if (isActive && refreshCount < 30) { // Max 30 refreshes per session
                    try {
                        bannerAdManager?.forceRefresh()
                        mediaAdapter?.refreshBannerAds()

                        Log.d(TAG, "Banner ads refreshed")
                        AnalyticsManager.logEvent("banner_auto_refresh")
                        
                        // Schedule next refresh
                        bannerHandler?.postDelayed(this, BANNER_REFRESH_INTERVAL)
                        
                    } catch (e: Exception) {
                        Log.e(TAG, "Failed to refresh banner ad: ${e.message}")
                        CrashlyticsManager.logException(e, "Banner auto refresh failed")
                    }
                }
            }
        }
        
        // Start first refresh after initial delay
        bannerHandler?.postDelayed(bannerRunnable!!, BANNER_REFRESH_INTERVAL)
    }
    
    /**
     * Start app open ad refresh
     */
    private fun startAppOpenRefresh() {
        appOpenHandler = Handler(Looper.getMainLooper())
        appOpenRunnable = object : Runnable {
            override fun run() {
                if (isActive && refreshCount < 10) { // Max 10 refreshes per session
                    try {
                        appOpenAdManager?.loadAd()
                        
                        Log.d(TAG, "App open ad refreshed")
                        AnalyticsManager.logEvent("app_open_auto_refresh")
                        
                        // Schedule next refresh
                        appOpenHandler?.postDelayed(this, APP_OPEN_REFRESH_INTERVAL)
                        
                    } catch (e: Exception) {
                        Log.e(TAG, "Failed to refresh app open ad: ${e.message}")
                        CrashlyticsManager.logException(e, "App open auto refresh failed")
                    }
                }
            }
        }
        
        // Start first refresh after initial delay
        appOpenHandler?.postDelayed(appOpenRunnable!!, APP_OPEN_REFRESH_INTERVAL)
    }
    
    /**
     * Cancel all refresh operations
     */
    private fun cancelAllRefresh() {
        // Cancel interstitial refresh
        interstitialRunnable?.let { runnable ->
            interstitialHandler?.removeCallbacks(runnable)
        }
        interstitialRunnable = null
        interstitialHandler = null
        
        // Cancel banner refresh
        bannerRunnable?.let { runnable ->
            bannerHandler?.removeCallbacks(runnable)
        }
        bannerRunnable = null
        bannerHandler = null
        
        // Cancel app open refresh
        appOpenRunnable?.let { runnable ->
            appOpenHandler?.removeCallbacks(runnable)
        }
        appOpenRunnable = null
        appOpenHandler = null
    }
    
    /**
     * Force refresh all ads
     */
    fun forceRefreshAll() {
        try {
            interstitialAdManager?.preloadAd()
            bannerAdManager?.forceRefresh()
            mediaAdapter?.refreshBannerAds()
            appOpenAdManager?.loadAd()

            Log.d(TAG, "All ads force refreshed")
            AnalyticsManager.logEvent("ads_force_refresh_all")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to force refresh all ads: ${e.message}")
            CrashlyticsManager.logException(e, "Force refresh all failed")
        }
    }
    
    /**
     * Get refresh statistics
     */
    fun getRefreshStats(): Map<String, Any> {
        return mapOf(
            "total_refreshes" to refreshCount,
            "is_active" to isActive,
            "banner_refreshes" to (bannerAdManager?.getRefreshCount() ?: 0)
        )
    }
    
    // Lifecycle methods
    override fun onResume(owner: LifecycleOwner) {
        super.onResume(owner)
        if (!isActive) {
            startAutoRefresh()
        }
    }
    
    override fun onPause(owner: LifecycleOwner) {
        super.onPause(owner)
        stopAutoRefresh()
    }
    
    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        destroy()
    }

    /**
     * Destroy and cleanup
     */
    fun destroy() {
        stopAutoRefresh()

        // Clear references
        interstitialAdManager = null
        bannerAdManager = null
        appOpenAdManager = null
        mediaAdapter = null

        Log.d(TAG, "AdRefreshManager destroyed")
    }
}
