package com.app.videofbdownloadfree

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.google.android.gms.ads.*
import kotlinx.coroutines.launch
import java.io.File

class SplashActivity : AppCompatActivity() {

    private lateinit var networkManager: NetworkManager
    private var tasksCompleted = 0
    private val totalTasks = 4
    private var minTimeElapsed = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_splash)

        // Initialize managers
        CrashlyticsManager.initialize(this)
        AnalyticsManager.initialize(this)
        networkManager = NetworkManager.getInstance(this)

        // Log events
        CrashlyticsManager.log("SplashActivity started")
        AnalyticsManager.logScreenView("Splash", "SplashActivity")

        // Start all tasks in parallel
        lifecycleScope.launch {
            RemoteConfigManager.init()
            taskCompleted()
        }

        loadAds()
        loadPython()
        createDirectories()

        // Minimum splash time (reduced to 2.5 seconds)
        Handler(Looper.getMainLooper()).postDelayed({
            minTimeElapsed = true
            checkAndProceed()
        }, 2500)
    }

    private fun loadPython() {
        lifecycleScope.launch {
            try {
                if (!com.chaquo.python.Python.isStarted()) {
                    com.chaquo.python.Python.start(com.chaquo.python.android.AndroidPlatform(this@SplashActivity))
                }
                CrashlyticsManager.log("Python initialized")
            } catch (e: Exception) {
                CrashlyticsManager.logException(e, "Python init failed")
            }
            taskCompleted()
        }
    }

    private fun createDirectories() {
        lifecycleScope.launch {
            try {
                val baseDir = File(getExternalFilesDir(null),"SaveIt")
                baseDir.mkdirs()

                // Create Videos and Images subdirectories
                java.io.File(baseDir, "Videos").mkdirs()
                java.io.File(baseDir, "Images").mkdirs()
            } catch (_: Exception) { }
            taskCompleted()
        }
    }

    private fun loadAds() {
        try {
            MobileAds.initialize(this) {
                // Initialization complete
                taskCompleted()
            }
        } catch (_: Exception) {
            taskCompleted()
        }
    }

    private fun taskCompleted() {
        tasksCompleted++
        checkAndProceed()
    }

    private fun checkAndProceed() {
        if (tasksCompleted >= totalTasks && minTimeElapsed) {
            if (networkManager.isNetworkAvailable()) {
                goToMainActivity()
            } else {
                goToNoInternetActivity()
            }
        }
    }

    private fun goToMainActivity() {
        // Check if we should show ads consent activity first
        if (AdsConsentActivity.shouldShowAdsConsentActivity(this)) {
            startActivity(Intent(this, AdsConsentActivity::class.java))
        } else if (PermissionsActivity.shouldShowPermissionsActivity(this)) {
            // If ads consent is done, check permissions
            startActivity(Intent(this, PermissionsActivity::class.java))
        } else {
            // Both consents are done, go to main activity
            startActivity(Intent(this, MainActivity::class.java))
        }
        finish()
    }

    private fun goToNoInternetActivity() {
        startActivity(Intent(this, NoInternetActivity::class.java))
        finish()
    }
}
