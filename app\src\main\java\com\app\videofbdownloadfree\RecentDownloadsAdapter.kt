package com.app.videofbdownloadfree

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

data class RecentDownloadItem(
    val file: File,
    val userName: String = "Instagram User",
    val description: String = "",
    val downloadTime: Long = System.currentTimeMillis()
)

class RecentDownloadsAdapter(
    private val context: Context,
    private var items: List<RecentDownloadItem> = emptyList(),
    private val onItemClick: (RecentDownloadItem) -> Unit = {},
    private val onActionClick: (RecentDownloadItem) -> Unit = {}
) : RecyclerView.Adapter<RecentDownloadsAdapter.ViewHolder>() {

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val thumbnailImage: ImageView = view.findViewById(R.id.thumbnailImage)
        val playIcon: ImageView = view.findViewById(R.id.playIcon)
        val videoDuration: TextView = view.findViewById(R.id.videoDuration)
        val profileImage: ImageView = view.findViewById(R.id.profileImage)
        val userName: TextView = view.findViewById(R.id.userName)
        val description: TextView = view.findViewById(R.id.description)
        val actionButton: ImageButton = view.findViewById(R.id.actionButton)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_recent_download, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = items[position]
        val file = item.file

        // Set username
        holder.userName.text = item.userName

        // Set description
        holder.description.text = if (item.description.isNotEmpty()) {
            item.description
        } else {
            "Behind the scene from the last..."
        }

        // Determine file type and set appropriate elements
        val isVideo = isVideoFile(file)
        if (isVideo) {
            holder.playIcon.visibility = View.VISIBLE
            holder.videoDuration.visibility = View.VISIBLE
            holder.videoDuration.text = "🎬" // Video indicator
        } else {
            holder.playIcon.visibility = View.GONE
            holder.videoDuration.visibility = View.VISIBLE
            holder.videoDuration.text = "📷" // Photo indicator
        }

        // Load thumbnail
        loadThumbnail(holder.thumbnailImage, file, isVideo)

        // Load profile image based on file type
        loadProfileImage(holder.profileImage, isVideo)

        // Set click listeners
        holder.itemView.setOnClickListener {
            onItemClick(item)
        }

        holder.actionButton.setOnClickListener {
            onActionClick(item)
        }
    }

    override fun getItemCount(): Int = items.size

    fun updateItems(newItems: List<RecentDownloadItem>) {
        items = newItems
        notifyDataSetChanged()
    }

    private fun isVideoFile(file: File): Boolean {
        val videoExtensions = listOf("mp4", "mov", "avi", "mkv", "webm", "3gp")
        return videoExtensions.contains(file.extension.lowercase())
    }

    private fun loadThumbnail(imageView: ImageView, file: File, isVideo: Boolean) {
        try {
            Glide.with(context)
                .load(file)
                .centerCrop()
                .placeholder(R.drawable.placeholder_image)
                .error(R.drawable.placeholder_image)
                .into(imageView)
        } catch (e: Exception) {
            imageView.setImageResource(R.drawable.placeholder_image)
        }
    }

    private fun loadProfileImage(imageView: ImageView, isVideo: Boolean) {
        // Different colors for video vs photo creators
        val videoColors = listOf(
            "#E91E63", "#9C27B0", "#673AB7", "#3F51B5"  // Purple/Pink tones for video
        )
        val photoColors = listOf(
            "#2196F3", "#00BCD4", "#4CAF50", "#FF9800"  // Blue/Green/Orange tones for photos
        )

        val colors = if (isVideo) videoColors else photoColors
        val randomColor = colors.random()

        try {
            // Set colored background based on content type
            imageView.setBackgroundColor(android.graphics.Color.parseColor(randomColor))

            // Use different icons for video vs photo
            val iconResource = if (isVideo) {
                R.drawable.folder // You can change this to a video icon if available
            } else {
                R.drawable.folder // You can change this to a photo icon if available
            }

            imageView.setImageResource(iconResource)
            imageView.scaleType = ImageView.ScaleType.CENTER
        } catch (e: Exception) {
            imageView.setImageResource(R.drawable.folder)
        }
    }

    private fun formatFileSize(bytes: Long): String {
        val kb = bytes / 1024.0
        val mb = kb / 1024.0
        val gb = mb / 1024.0

        return when {
            gb >= 1 -> String.format("%.1f GB", gb)
            mb >= 1 -> String.format("%.1f MB", mb)
            kb >= 1 -> String.format("%.1f KB", kb)
            else -> "$bytes B"
        }
    }

    private fun getTimeAgo(timestamp: Long): String {
        val now = System.currentTimeMillis()
        val diff = now - timestamp

        val seconds = diff / 1000
        val minutes = seconds / 60
        val hours = minutes / 60
        val days = hours / 24

        return when {
            days > 0 -> "${days}d ago"
            hours > 0 -> "${hours}h ago"
            minutes > 0 -> "${minutes}m ago"
            else -> "Just now"
        }
    }
}
