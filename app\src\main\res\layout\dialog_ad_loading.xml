<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:background="#80000000">

    <!-- Loading Card -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:background="@drawable/loading_card_background"
        android:padding="32dp"
        android:minWidth="200dp">

        <!-- Loading Icon -->
        <ImageView
            android:id="@+id/loadingIcon"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@android:drawable/stat_sys_download"
            android:tint="@color/insta_purple"
            android:layout_marginBottom="16dp" />

        <!-- Loading Spinner -->
        <ProgressBar
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:indeterminate="true"
            android:indeterminateTint="@color/insta_purple"
            android:layout_marginBottom="16dp" />

        <!-- Loading Text -->
        <TextView
            android:id="@+id/loadingText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Loading ad..."
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/insta_purple"
            android:gravity="center" />

        <!-- Description -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Please wait..."
            android:textSize="12sp"
            android:textColor="#666666"
            android:layout_marginTop="8dp"
            android:gravity="center" />

    </LinearLayout>

</LinearLayout>
