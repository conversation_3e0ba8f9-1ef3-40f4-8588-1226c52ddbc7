package com.app.videofbdownloadfree

import android.app.Activity
import android.util.Log
import com.google.android.gms.ads.AdError
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.FullScreenContentCallback
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.interstitial.InterstitialAd
import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback

/**
 * Manager for Interstitial Ads
 */
class InterstitialAdManager(private val activity: Activity) {
    
    companion object {
        private const val TAG = "InterstitialAdManager"
    }
    
    private var interstitialAd: InterstitialAd? = null
    private var isLoading = false
    private var adShowCount = 0
    private var lastAdShowTime = 0L
    private var loadingDialog: AdLoadingDialog? = null
    
    // Use config values for better control
    private val AD_SHOW_FREQUENCY = AdConfig.INTERSTITIAL_FREQUENCY
    private val AD_COOLDOWN_TIME = AdConfig.AD_COOLDOWN_TIME
    
    /**
     * Load interstitial ad with loading dialog
     */
    fun loadInterstitialAd(
        onAdLoaded: () -> Unit = {},
        onAdFailedToLoad: (String) -> Unit = {},
        showLoadingDialog: Boolean = false
    ) {
        if (isLoading) {
            Log.d(TAG, "Ad is already loading")
            return
        }

        isLoading = true

        // Show loading dialog if requested
        if (showLoadingDialog) {
            if (loadingDialog == null) {
                loadingDialog = AdLoadingDialog(activity)
            }
            loadingDialog?.show("Loading ad...")
        }

        val adUnitId = AdConfig.interstitialAdUnitId
        Log.d(TAG, "Loading interstitial ad with unit ID: $adUnitId")

        // Configure AdRequest for Facebook Bidding
        val adRequestBuilder = AdRequest.Builder()
        val adRequest = FacebookBiddingHelper.configureAdRequestForBidding(adRequestBuilder).build()
        InterstitialAd.load(activity, adUnitId, adRequest, object : InterstitialAdLoadCallback() {
            override fun onAdFailedToLoad(adError: LoadAdError) {
                Log.e(TAG, "Interstitial ad failed to load: ${adError.message}")
                Log.e(TAG, "Error code: ${adError.code}")
                interstitialAd = null
                isLoading = false

                // Hide loading dialog
                loadingDialog?.dismiss()

                // Log analytics
                AnalyticsManager.logAdEvent("interstitial", "load", false)

                onAdFailedToLoad("Error ${adError.code}: ${adError.message}")
            }

            override fun onAdLoaded(ad: InterstitialAd) {
                Log.d(TAG, "Interstitial ad loaded successfully")
                interstitialAd = ad
                isLoading = false

                // Hide loading dialog
                loadingDialog?.dismiss()

                // Log analytics
                AnalyticsManager.logAdEvent("interstitial", "load", true)

                onAdLoaded()
            }
        })
    }
    
    /**
     * Show interstitial ad if conditions are met
     */
    fun showInterstitialAd(
        onAdShown: () -> Unit = {},
        onAdDismissed: () -> Unit = {},
        onAdFailedToShow: (String) -> Unit = {},
        forceShow: Boolean = false
    ) {
        // Check if ads are enabled
        if (!AdConfig.adsEnabled) {
            Log.d(TAG, "Ads disabled")
            onAdDismissed()
            return
        }

        val currentTime = System.currentTimeMillis()

        // Check if we should show ad based on frequency and cooldown
        if (!forceShow && !shouldShowAd(currentTime)) {
            Log.d(TAG, "Ad not shown due to frequency/cooldown restrictions")
            onAdDismissed()
            return
        }
        
        val ad = interstitialAd
        if (ad != null) {
            Log.d(TAG, "Showing interstitial ad")
            
            ad.fullScreenContentCallback = object : FullScreenContentCallback() {
                override fun onAdClicked() {
                    Log.d(TAG, "Interstitial ad was clicked")
                }
                
                override fun onAdDismissedFullScreenContent() {
                    Log.d(TAG, "Interstitial ad dismissed")
                    interstitialAd = null
                    lastAdShowTime = currentTime
                    adShowCount++
                    onAdDismissed()
                    // Load next ad
                    loadInterstitialAd()
                }
                
                override fun onAdFailedToShowFullScreenContent(adError: AdError) {
                    Log.e(TAG, "Interstitial ad failed to show: ${adError.message}")
                    interstitialAd = null
                    onAdFailedToShow(adError.message)
                    // Load next ad
                    loadInterstitialAd()
                }
                
                override fun onAdImpression() {
                    Log.d(TAG, "Interstitial ad recorded an impression")
                }
                
                override fun onAdShowedFullScreenContent() {
                    Log.d(TAG, "Interstitial ad showed fullscreen content")
                    onAdShown()
                }
            }
            
            ad.show(activity)
        } else {
            Log.d(TAG, "Interstitial ad is not ready")
            onAdFailedToShow("Ad not ready")
            // Try to load ad
            loadInterstitialAd()
        }
    }
    
    /**
     * Check if ad should be shown based on frequency and cooldown
     */
    private fun shouldShowAd(currentTime: Long): Boolean {
        val timeSinceLastAd = currentTime - lastAdShowTime
        val frequencyMet = adShowCount % AD_SHOW_FREQUENCY == 0 && adShowCount > 0
        val cooldownPassed = timeSinceLastAd >= AD_COOLDOWN_TIME
        
        Log.d(TAG, "Ad show check - Count: $adShowCount, Time since last: ${timeSinceLastAd}ms, Frequency met: $frequencyMet, Cooldown passed: $cooldownPassed")
        
        return frequencyMet || cooldownPassed
    }
    
    /**
     * Check if interstitial ad is ready
     */
    fun isAdReady(): Boolean {
        return interstitialAd != null
    }
    
    /**
     * Get ad status
     */
    fun getAdStatus(): String {
        return when {
            isLoading -> "Loading interstitial ad..."
            interstitialAd != null -> "Interstitial ad ready"
            else -> "Interstitial ad not available"
        }
    }
    
    /**
     * Force show ad (for testing or special cases)
     */
    fun forceShowAd(
        onAdShown: () -> Unit = {},
        onAdDismissed: () -> Unit = {},
        onAdFailedToShow: (String) -> Unit = {}
    ) {
        showInterstitialAd(onAdShown, onAdDismissed, onAdFailedToShow, forceShow = true)
    }

    /**
     * Show ad with smart loading - only show loading dialog if needed
     */
    fun showAdWithLoading(
        onAdShown: () -> Unit = {},
        onAdDismissed: () -> Unit = {},
        onAdFailedToShow: (String) -> Unit = {}
    ) {
        // Check if ads are enabled
        if (!AdConfig.adsEnabled) {
            Log.d(TAG, "Ads disabled, skipping")
            onAdDismissed()
            return
        }

        // Try to get preloaded ad first
        val preloadedAd = AdPreloader.getPreloadedInterstitial(activity)
        if (preloadedAd != null) {
            interstitialAd = preloadedAd
            showInterstitialAd(onAdShown, onAdDismissed, onAdFailedToShow, forceShow = true)
            return
        }

        if (isAdReady()) {
            // Ad is ready, show immediately
            showInterstitialAd(onAdShown, onAdDismissed, onAdFailedToShow, forceShow = true)
        } else {
            // Ad not ready, load with loading dialog
            loadInterstitialAd(
                onAdLoaded = {
                    // Ad loaded, show it
                    showInterstitialAd(onAdShown, onAdDismissed, onAdFailedToShow, forceShow = true)
                },
                onAdFailedToLoad = { error ->
                    Log.w(TAG, "Failed to load ad for immediate show: $error")
                    onAdFailedToShow(error)
                },
                showLoadingDialog = true
            )
        }
    }
    
    /**
     * Increment download counter for ad frequency
     */
    fun incrementDownloadCounter() {
        adShowCount++
        Log.d(TAG, "Download counter incremented to: $adShowCount")
    }
    
    /**
     * Reset ad counters (for testing)
     */
    fun resetCounters() {
        adShowCount = 0
        lastAdShowTime = 0L
        Log.d(TAG, "Ad counters reset")
    }
    
    /**
     * Preload ad
     */
    fun preloadAd() {
        if (interstitialAd == null && !isLoading) {
            loadInterstitialAd()
        }
    }
    
    /**
     * Destroy ad
     */
    fun destroy() {
        interstitialAd = null
        isLoading = false
        loadingDialog?.dismiss()
        loadingDialog = null
        Log.d(TAG, "InterstitialAdManager destroyed")
    }
}
