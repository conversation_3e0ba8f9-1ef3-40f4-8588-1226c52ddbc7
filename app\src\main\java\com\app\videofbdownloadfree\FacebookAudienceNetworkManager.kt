package com.app.videofbdownloadfree

import android.content.Context
import android.util.Log
import com.facebook.ads.AudienceNetworkAds
import com.facebook.ads.AdSettings

/**
 * Manager for Facebook Audience Network initialization and configuration
 * Used for AdMob Bidding integration
 */
object FacebookAudienceNetworkManager {
    private const val TAG = "FacebookAudienceNetwork"
    private var isInitialized = false

    /**
     * Initialize Facebook Audience Network
     * Call this in Application onCreate or MainActivity
     */
    fun initialize(context: Context) {
        if (isInitialized) {
            Log.d(TAG, "Facebook Audience Network already initialized")
            return
        }

        try {
            Log.d(TAG, "Initializing Facebook Audience Network...")
            
            // Initialize Facebook Audience Network
            AudienceNetworkAds.initialize(context)
            
            // Enable test mode in debug builds
            if (BuildConfig.DEBUG) {
                AdSettings.setTestMode(true)
                Log.d(TAG, "Facebook Audience Network test mode enabled")
            }
            
            // Set mixed audience for better targeting
            AdSettings.setMixedAudience(false)
            
            isInitialized = true
            Log.d(TAG, "Facebook Audience Network initialized successfully")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing Facebook Audience Network: ${e.message}")
            CrashlyticsManager.logException(e, "Facebook Audience Network initialization failed")
        }
    }

    /**
     * Check if Facebook Audience Network is initialized
     */
    fun isInitialized(): Boolean {
        return isInitialized
    }

    /**
     * Enable test mode for Facebook ads
     * Only use in debug builds
     */
    fun enableTestMode(enable: Boolean) {
        if (BuildConfig.DEBUG) {
            AdSettings.setTestMode(enable)
            Log.d(TAG, "Facebook test mode ${if (enable) "enabled" else "disabled"}")
        }
    }

    /**
     * Add test device for Facebook ads
     * Only use in debug builds
     */
    fun addTestDevice(deviceId: String) {
        if (BuildConfig.DEBUG) {
            AdSettings.addTestDevice(deviceId)
            Log.d(TAG, "Facebook test device added: $deviceId")
        }
    }

    /**
     * Set data processing options for CCPA compliance
     */
    fun setDataProcessingOptions(context: Context, options: Array<String>, country: Int = 0, state: Int = 0) {
        try {
            AdSettings.setDataProcessingOptions(options, country, state)
            Log.d(TAG, "Facebook data processing options set")
        } catch (e: Exception) {
            Log.e(TAG, "Error setting Facebook data processing options: ${e.message}")
        }
    }

    /**
     * Clear Facebook Audience Network cache
     */
    fun clearCache() {
        try {
            // Facebook doesn't provide direct cache clearing
            // This is handled automatically by the SDK
            Log.d(TAG, "Facebook cache clearing requested")
        } catch (e: Exception) {
            Log.e(TAG, "Error clearing Facebook cache: ${e.message}")
        }
    }
}
