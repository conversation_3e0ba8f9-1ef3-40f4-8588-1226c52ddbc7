package com.app.videofbdownloadfree

import android.app.Activity
import android.util.Log
import com.google.android.gms.ads.AdError
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.FullScreenContentCallback
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.rewarded.RewardedAd
import com.google.android.gms.ads.rewarded.RewardedAdLoadCallback

/**
 * Manager for Rewarded Ads
 */
class RewardedAdManager(private val activity: Activity) {
    
    companion object {
        private const val TAG = "RewardedAdManager"
    }
    
    private var rewardedAd: RewardedAd? = null
    private var isLoading = false
    
    /**
     * Load rewarded ad
     */
    fun loadRewardedAd(onAdLoaded: () -> Unit = {}, onAdFailedToLoad: (String) -> Unit = {}) {
        if (!AdConfig.adsEnabled) {
            onAdFailedToLoad("Ads disabled")
            return
        }

        // Try to get preloaded ad first
        val preloadedAd = AdPreloader.getPreloadedRewarded(activity)
        if (preloadedAd != null) {
            rewardedAd = preloadedAd
            onAdLoaded()
            return
        }

        if (isLoading) {
            Log.d(TAG, "Ad is already loading")
            onAdFailedToLoad("Ad is already loading")
            return
        }

        isLoading = true
        val adUnitId = AdConfig.rewardedAdUnitId

        Log.d(TAG, "Loading rewarded ad with unit ID: $adUnitId")

        // Configure AdRequest for Facebook Bidding
        val adRequestBuilder = AdRequest.Builder()
        val adRequest = FacebookBiddingHelper.configureAdRequestForBidding(adRequestBuilder).build()
        RewardedAd.load(activity, adUnitId, adRequest, object : RewardedAdLoadCallback() {
            override fun onAdFailedToLoad(adError: LoadAdError) {
                Log.e(TAG, "Rewarded ad failed to load: ${adError.message}")
                rewardedAd = null
                isLoading = false
                onAdFailedToLoad("Error ${adError.code}: ${adError.message}")
            }

            override fun onAdLoaded(ad: RewardedAd) {
                Log.d(TAG, "Rewarded ad loaded successfully")
                rewardedAd = ad
                isLoading = false
                onAdLoaded()
            }
        })
    }
    
    /**
     * Show rewarded ad with smart loading
     */
    fun showRewardedAd(
        onUserEarnedReward: () -> Unit,
        onAdDismissed: () -> Unit = {},
        onAdFailedToShow: (String) -> Unit = {}
    ) {
        // Check if ads are enabled
        if (!AdConfig.adsEnabled) {
            Log.d(TAG, "Ads disabled, skipping")
            onAdDismissed()
            return
        }

        // Try to get preloaded ad first
        val preloadedAd = AdPreloader.getPreloadedRewarded(activity)
        if (preloadedAd != null) {
            rewardedAd = preloadedAd
        }

        val ad = rewardedAd
        if (ad != null) {
            Log.d(TAG, "Showing rewarded ad")
            
            ad.fullScreenContentCallback = object : FullScreenContentCallback() {
                override fun onAdClicked() {
                    Log.d(TAG, "Rewarded ad was clicked")
                }
                
                override fun onAdDismissedFullScreenContent() {
                    Log.d(TAG, "Rewarded ad dismissed")
                    rewardedAd = null
                    onAdDismissed()
                    // Load next ad
                    loadRewardedAd()
                }
                
                override fun onAdFailedToShowFullScreenContent(adError: AdError) {
                    Log.e(TAG, "Rewarded ad failed to show: ${adError.message}")
                    rewardedAd = null
                    onAdFailedToShow(adError.message)
                    // Load next ad
                    loadRewardedAd()
                }
                
                override fun onAdImpression() {
                    Log.d(TAG, "Rewarded ad recorded an impression")
                }
                
                override fun onAdShowedFullScreenContent() {
                    Log.d(TAG, "Rewarded ad showed fullscreen content")
                }
            }
            
            ad.show(activity) { rewardItem ->
                Log.d(TAG, "User earned reward: ${rewardItem.amount} ${rewardItem.type}")
                onUserEarnedReward()
            }
        } else {
            Log.e(TAG, "Rewarded ad is not ready")
            onAdFailedToShow("الإعلان غير جاهز، جاري التحميل...")
            // Try to load ad
            loadRewardedAd()
        }
    }
    
    /**
     * Check if rewarded ad is ready
     */
    fun isAdReady(): Boolean {
        return rewardedAd != null || AdPreloader.isRewardedReady()
    }
    
    /**
     * Get ad status
     */
    fun getAdStatus(): String {
        return when {
            isLoading -> "جاري تحميل الإعلان..."
            rewardedAd != null -> "الإعلان جاهز"
            else -> "الإعلان غير متاح"
        }
    }
    
    /**
     * Preload ad
     */
    fun preloadAd() {
        if (rewardedAd == null && !isLoading) {
            loadRewardedAd()
        }
    }
    
    /**
     * Destroy ad
     */
    fun destroy() {
        rewardedAd = null
        isLoading = false
        Log.d(TAG, "RewardedAdManager destroyed")
    }
}
