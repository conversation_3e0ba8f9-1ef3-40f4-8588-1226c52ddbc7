<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res"><file name="btn_background_tint_selector" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\color\btn_background_tint_selector.xml" qualifiers="" type="color"/><file name="ic_launcher_background" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\color\ic_launcher_background.xml" qualifiers="" type="color"/><file name="tab_button_text_color_selector" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\color\tab_button_text_color_selector.xml" qualifiers="" type="color"/><file name="about" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\about.webp" qualifiers="" type="drawable"/><file name="ad_badge_background" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\ad_badge_background.xml" qualifiers="" type="drawable"/><file name="ad_container_background" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\ad_container_background.xml" qualifiers="" type="drawable"/><file name="ad_label_background" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\ad_label_background.xml" qualifiers="" type="drawable"/><file name="app_icon_background" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\app_icon_background.xml" qualifiers="" type="drawable"/><file name="back" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\back.webp" qualifiers="" type="drawable"/><file name="bottom_nav_background" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\bottom_nav_background.xml" qualifiers="" type="drawable"/><file name="btn_background_selector" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\btn_background_selector.xml" qualifiers="" type="drawable"/><file name="card_background" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\card_background.xml" qualifiers="" type="drawable"/><file name="check" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\check.xml" qualifiers="" type="drawable"/><file name="circle_backgound" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\circle_backgound.xml" qualifiers="" type="drawable"/><file name="close" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\close.xml" qualifiers="" type="drawable"/><file name="dialog_background" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\dialog_background.xml" qualifiers="" type="drawable"/><file name="duration_badge_background" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\duration_badge_background.xml" qualifiers="" type="drawable"/><file name="edittext_background" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\edittext_background.xml" qualifiers="" type="drawable"/><file name="email" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\email.xml" qualifiers="" type="drawable"/><file name="folder" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\folder.webp" qualifiers="" type="drawable"/><file name="gradient_header_background" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\gradient_header_background.xml" qualifiers="" type="drawable"/><file name="gradient_icon" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\gradient_icon.xml" qualifiers="" type="drawable"/><file name="gradient_overlay_bottom" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\gradient_overlay_bottom.xml" qualifiers="" type="drawable"/><file name="gradient_overlay_top" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\gradient_overlay_top.xml" qualifiers="" type="drawable"/><file name="help" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\help.png" qualifiers="" type="drawable"/><file name="icon" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\icon.png" qualifiers="" type="drawable"/><file name="icon_no_background" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\icon_no_background.png" qualifiers="" type="drawable"/><file name="ic_ads" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\ic_ads.xml" qualifiers="" type="drawable"/><file name="ic_ads_24" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\ic_ads_24.xml" qualifiers="" type="drawable"/><file name="ic_check" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\ic_check.xml" qualifiers="" type="drawable"/><file name="ic_close" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\ic_close.xml" qualifiers="" type="drawable"/><file name="ic_dark_mode" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\ic_dark_mode.xml" qualifiers="" type="drawable"/><file name="ic_home" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\ic_home.xml" qualifiers="" type="drawable"/><file name="ic_info" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\ic_info.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_premium" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\ic_premium.xml" qualifiers="" type="drawable"/><file name="ic_shield" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\ic_shield.xml" qualifiers="" type="drawable"/><file name="ic_star_empty" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\ic_star_empty.xml" qualifiers="" type="drawable"/><file name="ic_star_filled" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\ic_star_filled.xml" qualifiers="" type="drawable"/><file name="ic_storage" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\ic_storage.xml" qualifiers="" type="drawable"/><file name="ic_target" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\ic_target.xml" qualifiers="" type="drawable"/><file name="ic_video" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\ic_video.xml" qualifiers="" type="drawable"/><file name="images" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\images.webp" qualifiers="" type="drawable"/><file name="im_pause" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\im_pause.png" qualifiers="" type="drawable"/><file name="im_play" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\im_play.png" qualifiers="" type="drawable"/><file name="input_background" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\input_background.xml" qualifiers="" type="drawable"/><file name="list" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\list.xml" qualifiers="" type="drawable"/><file name="loading_card_background" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\loading_card_background.xml" qualifiers="" type="drawable"/><file name="main_header_gradient" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\main_header_gradient.xml" qualifiers="" type="drawable"/><file name="media_type_icon_background" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\media_type_icon_background.xml" qualifiers="" type="drawable"/><file name="menu_downloads" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\menu_downloads.png" qualifiers="" type="drawable"/><file name="menu_home" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\menu_home.png" qualifiers="" type="drawable"/><file name="menu_privecy_policy" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\menu_privecy_policy.png" qualifiers="" type="drawable"/><file name="menu_rate_app" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\menu_rate_app.png" qualifiers="" type="drawable"/><file name="minus" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\minus.xml" qualifiers="" type="drawable"/><file name="nav_item_background" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\nav_item_background.xml" qualifiers="" type="drawable"/><file name="next" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\next.webp" qualifiers="" type="drawable"/><file name="no_internet_image" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\no_internet_image.png" qualifiers="" type="drawable"/><file name="open" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\open.xml" qualifiers="" type="drawable"/><file name="play_icon_background" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\play_icon_background.xml" qualifiers="" type="drawable"/><file name="play_pause" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\play_pause.xml" qualifiers="" type="drawable"/><file name="police" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\police.xml" qualifiers="" type="drawable"/><file name="premium_badge_background" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\premium_badge_background.xml" qualifiers="" type="drawable"/><file name="previous" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\previous.xml" qualifiers="" type="drawable"/><file name="progress_rounded" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\progress_rounded.xml" qualifiers="" type="drawable"/><file name="reels" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\reels.webp" qualifiers="" type="drawable"/><file name="rounded_background" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\rounded_background.xml" qualifiers="" type="drawable"/><file name="rounded_button" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\rounded_button.xml" qualifiers="" type="drawable"/><file name="rounded_corner_background" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\rounded_corner_background.xml" qualifiers="" type="drawable"/><file name="setting" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\setting.png" qualifiers="" type="drawable"/><file name="soundplus" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\soundplus.webp" qualifiers="" type="drawable"/><file name="star" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\star.xml" qualifiers="" type="drawable"/><file name="star1" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\star1.webp" qualifiers="" type="drawable"/><file name="star2" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\star2.webp" qualifiers="" type="drawable"/><file name="star3" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\star3.webp" qualifiers="" type="drawable"/><file name="star4" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\star4.webp" qualifiers="" type="drawable"/><file name="star5" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\star5.webp" qualifiers="" type="drawable"/><file name="star_default" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\star_default.webp" qualifiers="" type="drawable"/><file name="status_background" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\status_background.xml" qualifiers="" type="drawable"/><file name="tab_button_background_selector" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\tab_button_background_selector.xml" qualifiers="" type="drawable"/><file name="usage1" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\usage1.png" qualifiers="" type="drawable"/><file name="usage2" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\usage2.png" qualifiers="" type="drawable"/><file name="usage3" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\usage3.png" qualifiers="" type="drawable"/><file name="volume_high" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\volume_high.xml" qualifiers="" type="drawable"/><file name="volume_medium" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\volume_medium.xml" qualifiers="" type="drawable"/><file name="welcome_cats" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\welcome_cats.png" qualifiers="" type="drawable"/><file name="poppins_bold" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\font\poppins_bold.ttf" qualifiers="" type="font"/><file name="poppins_regular" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\font\poppins_regular.ttf" qualifiers="" type="font"/><file name="activity_ads_consent" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\layout\activity_ads_consent.xml" qualifiers="" type="layout"/><file name="activity_main" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_no_internet" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\layout\activity_no_internet.xml" qualifiers="" type="layout"/><file name="activity_permissions" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\layout\activity_permissions.xml" qualifiers="" type="layout"/><file name="activity_recent_downloads" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\layout\activity_recent_downloads.xml" qualifiers="" type="layout"/><file name="activity_splash" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\layout\activity_splash.xml" qualifiers="" type="layout"/><file name="dialog_ad_loading" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\layout\dialog_ad_loading.xml" qualifiers="" type="layout"/><file name="dialog_how_to_use" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\layout\dialog_how_to_use.xml" qualifiers="" type="layout"/><file name="dialog_rate_app" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\layout\dialog_rate_app.xml" qualifiers="" type="layout"/><file name="fullscreen_preview" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\layout\fullscreen_preview.xml" qualifiers="" type="layout"/><file name="item_banner_ad_square" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\layout\item_banner_ad_square.xml" qualifiers="" type="layout"/><file name="native_ad_floating" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\layout\native_ad_floating.xml" qualifiers="" type="layout"/><file name="native_ad_grid_large" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\layout\native_ad_grid_large.xml" qualifiers="" type="layout"/><file name="native_ad_layout" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\layout\native_ad_layout.xml" qualifiers="" type="layout"/><file name="native_ad_simple" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\layout\native_ad_simple.xml" qualifiers="" type="layout"/><file name="native_ad_small_layout" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\layout\native_ad_small_layout.xml" qualifiers="" type="layout"/><file name="settings_preferences" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\layout\settings_preferences.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\mipmap-hdpi\ic_launcher_foreground.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\mipmap-mdpi\ic_launcher_foreground.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\mipmap-xhdpi\ic_launcher_foreground.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\mipmap-xxhdpi\ic_launcher_foreground.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\mipmap-xxxhdpi\ic_launcher_foreground.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\values\arrays.xml" qualifiers=""><string-array name="reply_entries">
        <item>Reply</item>
        <item>Reply to all</item>
    </string-array><string-array name="reply_values">
        <item>reply</item>
        <item>reply_all</item>
    </string-array></file><file path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\values\colors.xml" qualifiers=""><color name="insta_purple">#6200EE</color><color name="insta_pink">#9D00FF</color><color name="insta_orange">#F56040</color><color name="insta_yellow">#FCAF45</color><color name="insta_red">#8D61EF</color><color name="instagram_red">#E1306C</color><color name="insta_deep_purple">#5851DB</color><color name="white">#FFFFFF</color><color name="black">#000000</color><color name="light_gray">#F5F5F5</color><color name="green">#4CAF50</color><color name="orange">#FF9500</color><color name="gray">#9E9E9E</color><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="your_selected_color">#FF6200EE</color><color name="your_pressed_color">#FF3700B3</color><color name="your_default_color">#FFBB86FC</color></file><file path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="activity_horizontal_margin">16dp</dimen><dimen name="activity_vertical_margin">16dp</dimen><dimen name="nav_header_vertical_spacing">8dp</dimen><dimen name="nav_header_height">176dp</dimen><dimen name="fab_margin">16dp</dimen></file><file path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">SaveIt Instagram Saver</string><string name="fullscreen_image">Full Screen Image</string><string name="close">Close</string><string name="nav_header_title">SaveIt</string><string name="nav_header_subtitle">Instagram Downloader</string><string name="nav_header_desc">Navigation header</string><string name="preview_video">Preview Video</string><string name="preview_image">Preview Image</string><string name="package_name">com.app.videofbdownloadfree</string><string name="usage1">First Go to instagram and copy link reel or post you want to download</string><string name="usage2">Click In button Paste to get like from your Clipboard</string><string name="usage2_2">Then click download to start downloading</string><string name="usage3">Watch and Chill with your downloads In build-in Player</string><string name="download_now">Download Now</string><string name="free_downloads_5_5">Free Downloads: 5/5</string><string name="downloading">⏳ Downloading…</string><string name="how_to_use">How to Use</string><string name="easy_fast_secure">Easy • Fast • Secure</string><string name="saveit">SaveIt</string><string name="paste_url_here">Paste URL Here...</string><string name="paste">Paste</string><string name="description_storage">You will find all downloaded files in the SaveIt folder in your phone storage</string><string name="no_internet">No Internet Found</string><string name="no_internet_description">Please Check your internet!</string><string name="btn_no_internet">Check</string><string name="admob_id">ca-app-pub-1773354726031886~4208237154</string><string name="description_admob">This app is free and supported by ads. We use personalized ads to provide you with a better experience.</string><string name="messages_header">Messages</string><string name="sync_header">Sync</string><string name="signature_title">Your signature</string><string name="reply_title">Default reply action</string><string name="sync_title">Sync email periodically</string><string name="attachment_title">Download incoming attachments</string><string name="attachment_summary_on">Automatically download attachments for incoming emails
    </string><string name="attachment_summary_off">Only download attachments when manually requested</string><string name="privacy_url">https://kurodev.blogspot.com/p/fbdownloader-video-or-storie-privacy.html</string><string name="about_url">https://kurodev.blogspot.com/p/saveit-about.html</string><string name="mail_contact"><EMAIL></string><string name="version_app">Version 4.5.1</string><string name="facebook_app_id">499608471956919</string><string name="facebook_client_token">********************************</string></file><file path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.InstaLoader" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/insta_red</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">false</item>
        
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightNavigationBar">false</item>
        
        <item name="android:windowLayoutInDisplayCutoutMode" ns1:targetApi="p">shortEdges</item>
        <item name="android:enforceNavigationBarContrast" ns1:targetApi="q">false</item>
        <item name="android:enforceStatusBarContrast" ns1:targetApi="q">false</item>
        
    </style><style name="CollapsedAppBarTextAppearance" parent="TextAppearance.AppCompat.Widget.ActionBar.Title">
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:shadowColor">#80000000</item>
        <item name="android:shadowDx">1</item>
        <item name="android:shadowDy">1</item>
        <item name="android:shadowRadius">2</item>
    </style><style name="ExpandedAppBarTextAppearance" parent="TextAppearance.AppCompat.Display1">
        <item name="android:textSize">32sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:shadowColor">#80000000</item>
        <item name="android:shadowDx">2</item>
        <item name="android:shadowDy">2</item>
        <item name="android:shadowRadius">4</item>
    </style></file><file name="backup_rules" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="file_paths" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/><file name="gma_ad_services_config" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\xml\gma_ad_services_config.xml" qualifiers="" type="xml"/><file name="link_icon" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\link_icon.png" qualifiers="" type="drawable"/><file name="contentpaste" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\contentpaste.xml" qualifiers="" type="drawable"/><file name="download_btn" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\download_btn.png" qualifiers="" type="drawable"/><file name="skip_next" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\skip_next.xml" qualifiers="" type="drawable"/><file name="download_button_selector" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\download_button_selector.xml" qualifiers="" type="drawable"/><file name="circle_background" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\circle_background.xml" qualifiers="" type="drawable"/><file name="ic_more_vert" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\ic_more_vert.xml" qualifiers="" type="drawable"/><file name="placeholder_image" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\drawable\placeholder_image.xml" qualifiers="" type="drawable"/><file name="item_recent_download" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\res\layout\item_recent_download.xml" qualifiers="" type="layout"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\build\generated\res\resValues\debug"/><source path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\build\generated\res\processDebugGoogleServices"/><source path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\build\generated\res\injectCrashlyticsMappingFileIdDebug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\build\generated\res\resValues\debug"/><source path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\build\generated\res\processDebugGoogleServices"><file path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\build\generated\res\processDebugGoogleServices\values\values.xml" qualifiers=""><string name="gcm_defaultSenderId" translatable="false">347906987430</string><string name="google_api_key" translatable="false">AIzaSyBKvoxWWvv3YV0ZBQzWAfoYX83L1MrcRKo</string><string name="google_app_id" translatable="false">1:347906987430:android:3ba2c82a9bccc92138ab67</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyBKvoxWWvv3YV0ZBQzWAfoYX83L1MrcRKo</string><string name="google_storage_bucket" translatable="false">saveit-28d9c.firebasestorage.app</string><string name="project_id" translatable="false">saveit-28d9c</string></file></source><source path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\build\generated\res\injectCrashlyticsMappingFileIdDebug"><file path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\build\generated\res\injectCrashlyticsMappingFileIdDebug\values\com_google_firebase_crashlytics_mappingfileid.xml" qualifiers=""><string name="com.google.firebase.crashlytics.mapping_file_id" ns1:ignore="UnusedResources,TypographyDashes" translatable="false">00000000000000000000000000000000</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices" generated-set="res-processDebugGoogleServices$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-injectCrashlyticsMappingFileIdDebug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-injectCrashlyticsMappingFileIdDebug" generated-set="res-injectCrashlyticsMappingFileIdDebug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>