package com.app.videofbdownloadfree

import android.content.Context
import android.net.Uri
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import com.google.android.gms.ads.AdListener
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.AdSize
import com.google.android.gms.ads.AdView
import com.google.android.gms.ads.LoadAdError
import java.io.File

/**
 * Media Adapter with Square Banner Ads for RecyclerView
 * Shows banner ads after every 2 rows (6 items in grid)
 */
class MediaAdapterWithBannerAds(
    private val context: Context,
    private var mediaFiles: List<File>,
    private val onItemClick: (File) -> Unit
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        private const val TYPE_MEDIA = 0
        private const val TYPE_BANNER_AD = 1
        private const val AD_FREQUENCY = 6 // Show ad every 6 items (2 rows in grid)
        private const val TAG = "MediaAdapterWithBannerAds"
        private const val MAX_ADS_PER_SESSION = 10 // Limit ads per session
    }

    private val bannerAds = mutableMapOf<Int, AdView>()

    // Glide options for better performance
    private val glideOptions = RequestOptions()
        .diskCacheStrategy(DiskCacheStrategy.ALL)
        .skipMemoryCache(false)
        .override(200, 200)

    inner class MediaViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val thumbnail: ImageView = itemView.findViewById(R.id.mediaThumbnail)
        val playIconContainer: View = itemView.findViewById(R.id.playIconContainer)
        val durationBadge: android.widget.TextView = itemView.findViewById(R.id.durationBadge)
        val mediaTypeIcon: ImageView = itemView.findViewById(R.id.mediaTypeIcon)
    }

    inner class BannerAdViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val adContainer: ViewGroup = itemView.findViewById(R.id.bannerAdContainer)
    }

    override fun getItemViewType(position: Int): Int {
        return if (isAdPosition(position)) TYPE_BANNER_AD else TYPE_MEDIA
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            TYPE_BANNER_AD -> {
                val adView = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_banner_ad_square, parent, false)
                BannerAdViewHolder(adView)
            }
            else -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_media, parent, false)
                MediaViewHolder(view)
            }
        }
    }

    override fun getItemCount(): Int {
        // Add extra items for ads
        val adCount = mediaFiles.size / AD_FREQUENCY
        return mediaFiles.size + adCount
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val startTime = System.currentTimeMillis()


        when (holder.itemViewType) {
            TYPE_BANNER_AD -> {
                PerformanceManager.startTrace("load_banner_ad")
                val adHolder = holder as BannerAdViewHolder
                loadBannerAd(adHolder, position)
            }
            TYPE_MEDIA -> {
                PerformanceManager.startTrace("bind_media_item")

                val mediaHolder = holder as MediaViewHolder
                val mediaPosition = getMediaPosition(position)
                if (mediaPosition < mediaFiles.size) {
                    bindMediaItem(mediaHolder, mediaFiles[mediaPosition])
                }
                PerformanceManager.stopTrace("bind_media_item")
            }
        }
    }

    private fun bindMediaItem(holder: MediaViewHolder, file: File) {
        // Clear previous image to prevent recycling issues
        holder.thumbnail.setImageDrawable(null)

        // Load thumbnail with Glide
        Glide.with(context)
            .load(Uri.fromFile(file))
            .apply(glideOptions)
            .centerCrop()
            .into(holder.thumbnail)

        val extension = file.extension.lowercase()
        if (extension in listOf("mp4", "mov", "avi", "mkv")) {
            // For videos
            holder.playIconContainer.visibility = View.VISIBLE
            holder.durationBadge.visibility = View.VISIBLE
            holder.mediaTypeIcon.setImageResource(android.R.drawable.ic_media_play)
            holder.durationBadge.text = "Video"
        } else if (extension in listOf("jpg", "jpeg", "png", "webp")) {
            // For images
            holder.playIconContainer.visibility = View.GONE
            holder.durationBadge.visibility = View.GONE
            holder.mediaTypeIcon.setImageResource(android.R.drawable.ic_menu_camera)
        } else {
            // Unknown file type
            holder.playIconContainer.visibility = View.GONE
            holder.durationBadge.visibility = View.VISIBLE
            holder.durationBadge.text = "File"
            holder.mediaTypeIcon.setImageResource(android.R.drawable.ic_menu_gallery)
        }

        holder.itemView.setOnClickListener {
            onItemClick(file)
        }
    }

    private fun loadBannerAd(holder: BannerAdViewHolder, position: Int) {
        val startTime = System.currentTimeMillis()
        try {
            // Check if ads are enabled
            if (!AdConfig.adsEnabled) {
                holder.adContainer.visibility = View.GONE
                return
            }

            // Limit number of ads per session
            if (bannerAds.size >= MAX_ADS_PER_SESSION) {
                holder.adContainer.visibility = View.GONE
                return
            }

            // Check if we already have an ad for this position
            bannerAds[position]?.let { existingAdView ->
                try {
                    // Remove from previous parent if any
                    (existingAdView.parent as? ViewGroup)?.removeView(existingAdView)

                    holder.adContainer.removeAllViews()
                    holder.adContainer.addView(existingAdView)
                    Log.d(TAG, "Reused existing banner ad for position $position")
                    return
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to reuse banner ad for position $position: ${e.message}")
                    bannerAds.remove(position) // Remove corrupted ad
                }
            }

            // Create new banner ad
            val adView = AdView(context).apply {
                setAdSize(AdSize.MEDIUM_RECTANGLE) // 300x250 - square banner
                adUnitId = AdConfig.bannerAdUnitId
                
                adListener = object : AdListener() {
                    override fun onAdLoaded() {
                        val loadTime = System.currentTimeMillis() - startTime
                        Log.d(TAG, "Banner ad loaded for position $position")
                            PerformanceManager.recordAdLoadPerformance(
                                adType = "banner",
                                success = true,
                                loadTimeMs = loadTime
                            )
                        // Log analytics
                        AnalyticsManager.logAdEvent("banner_square", "load", true)
                        CrashlyticsManager.logAdEvent("banner_square", "load", true)
                        PerformanceManager.stopTrace("load_banner_ad")

                    }
                    
                    override fun onAdFailedToLoad(adError: LoadAdError) {
                        Log.e(TAG, "Banner ad failed to load for position $position: ${adError.message}")
                        val loadTime = System.currentTimeMillis() - startTime

                        // تسجيل فشل تحميل الإعلان
                        PerformanceManager.recordAdLoadPerformance(
                            adType = "banner",
                            success = false,
                            loadTimeMs = loadTime
                        )
                        // Log analytics
                        AnalyticsManager.logAdEvent("banner_square", "load", false)
                        CrashlyticsManager.logAdEvent("banner_square", "load", false)
                        
                        // Hide the ad container
                        holder.adContainer.visibility = View.GONE
                        PerformanceManager.stopTrace("load_banner_ad")

                    }
                    
                    override fun onAdClicked() {
                        Log.d(TAG, "Banner ad clicked for position $position")
                        AnalyticsManager.logAdEvent("banner_square", "click", true)
                        CrashlyticsManager.logAdEvent("banner_square", "click", true)
                    }
                    
                    override fun onAdOpened() {
                        Log.d(TAG, "Banner ad opened for position $position")
                        AnalyticsManager.logAdEvent("banner_square", "open", true)
                    }
                    
                    override fun onAdClosed() {
                        Log.d(TAG, "Banner ad closed for position $position")
                        AnalyticsManager.logAdEvent("banner_square", "close", true)
                    }
                }
            }

            // Store the ad view
            bannerAds[position] = adView

            // Add to container with full width
            holder.adContainer.removeAllViews()

            // Set layout params for full width
            val layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            adView.layoutParams = layoutParams

            holder.adContainer.addView(adView)
            holder.adContainer.visibility = View.VISIBLE

            // Load the ad with Facebook Bidding support
            val adRequestBuilder = AdRequest.Builder()
            val adRequest = FacebookBiddingHelper.configureAdRequestForBidding(adRequestBuilder).build()
            adView.loadAd(adRequest)
            
            Log.d(TAG, "Loading banner ad for position $position...")

        } catch (e: Exception) {
            Log.e(TAG, "Exception in loadBannerAd for position $position: ${e.message}")
            CrashlyticsManager.logException(e, "Banner ad load failed for position $position")
            
            // Hide the ad container on error
            holder.adContainer.visibility = View.GONE
        }
    }

    private fun isAdPosition(position: Int): Boolean {
        return (position + 1) % (AD_FREQUENCY + 1) == 0 && position > 0
    }

    private fun getMediaPosition(position: Int): Int {
        val adsBefore = position / (AD_FREQUENCY + 1)
        return position - adsBefore
    }

    /**
     * Update data
     */
    fun updateData(newFiles: List<File>) {
        mediaFiles = newFiles
        notifyDataSetChanged()
    }

    /**
     * Refresh all banner ads
     */
    fun refreshBannerAds() {
        try {
            bannerAds.values.forEach { adView ->
                val adRequest = AdRequest.Builder().build()
                adView.loadAd(adRequest)
            }
            Log.d(TAG, "Refreshed ${bannerAds.size} banner ads")
            AnalyticsManager.logEvent("banner_ads_refreshed")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to refresh banner ads: ${e.message}")
            CrashlyticsManager.logException(e, "Banner ads refresh failed")
        }
    }

    /**
     * Clean up banner ads when adapter is destroyed
     */
    fun destroy() {
        try {
            bannerAds.values.forEach { adView ->
                adView.destroy()
            }
            bannerAds.clear()
            Log.d(TAG, "Banner ads destroyed")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to destroy banner ads: ${e.message}")
        }
    }
}
