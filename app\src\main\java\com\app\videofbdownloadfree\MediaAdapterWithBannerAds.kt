package com.app.videofbdownloadfree

import android.content.Context
import android.net.Uri
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import com.google.android.gms.ads.AdListener
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.AdSize
import com.google.android.gms.ads.AdView
import com.google.android.gms.ads.LoadAdError
import java.io.File

/**
 * Media Adapter with Square Banner Ads for RecyclerView
 * Shows banner ads after every 2 rows (6 items in grid)
 */
class MediaAdapterWithBannerAds(
    private val context: Context,
    private var mediaFiles: List<File>,
    private val onItemClick: (File) -> Unit
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        private const val TYPE_MEDIA = 0
        private const val TYPE_BANNER_AD = 1
        private const val AD_FREQUENCY = 4 // Show ad every 4 items (for linear layout)
        private const val TAG = "MediaAdapterWithBannerAds"
        private const val MAX_ADS_PER_SESSION = 10 // Limit ads per session
    }

    private val bannerAds = mutableMapOf<Int, AdView>()

    // Glide options for better performance
    private val glideOptions = RequestOptions()
        .diskCacheStrategy(DiskCacheStrategy.ALL)
        .skipMemoryCache(false)
        .override(200, 200)

    inner class MediaViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val thumbnail: ImageView = itemView.findViewById(R.id.mediaThumbnail)
        val playIcon: ImageView = itemView.findViewById(R.id.playIcon)
        val videoDuration: android.widget.TextView = itemView.findViewById(R.id.videoDuration)
        val profileImage: ImageView = itemView.findViewById(R.id.profileImage)
        val downloadDate: android.widget.TextView = itemView.findViewById(R.id.downloadDate)
        val fileInfo: android.widget.TextView = itemView.findViewById(R.id.fileInfo)
        val actionButton: ImageView = itemView.findViewById(R.id.actionButton)
    }

    inner class BannerAdViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val adContainer: ViewGroup = itemView.findViewById(R.id.bannerAdContainer)
    }

    override fun getItemViewType(position: Int): Int {
        return if (isAdPosition(position)) TYPE_BANNER_AD else TYPE_MEDIA
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            TYPE_BANNER_AD -> {
                val adView = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_banner_ad_square, parent, false)
                BannerAdViewHolder(adView)
            }
            else -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_media, parent, false)
                MediaViewHolder(view)
            }
        }
    }

    override fun getItemCount(): Int {
        // Add extra items for ads
        val adCount = mediaFiles.size / AD_FREQUENCY
        return mediaFiles.size + adCount
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val startTime = System.currentTimeMillis()


        when (holder.itemViewType) {
            TYPE_BANNER_AD -> {
                PerformanceManager.startTrace("load_banner_ad")
                val adHolder = holder as BannerAdViewHolder
                loadBannerAd(adHolder, position)
            }
            TYPE_MEDIA -> {
                PerformanceManager.startTrace("bind_media_item")

                val mediaHolder = holder as MediaViewHolder
                val mediaPosition = getMediaPosition(position)
                if (mediaPosition < mediaFiles.size) {
                    bindMediaItem(mediaHolder, mediaFiles[mediaPosition])
                }
                PerformanceManager.stopTrace("bind_media_item")
            }
        }
    }

    private fun bindMediaItem(holder: MediaViewHolder, file: File) {
        // Clear previous image to prevent recycling issues
        holder.thumbnail.setImageDrawable(null)

        // Load thumbnail with Glide
        Glide.with(context)
            .load(Uri.fromFile(file))
            .apply(glideOptions)
            .centerCrop()
            .into(holder.thumbnail)

        // Set download date
        holder.downloadDate.text = formatDownloadDate(file.lastModified())

        // Set file type and size
        holder.fileInfo.text = generateFileDescription(file)

        // Determine file type and set appropriate elements
        val isVideo = isVideoFile(file)
        if (isVideo) {
            holder.playIcon.visibility = View.VISIBLE
            holder.videoDuration.visibility = View.VISIBLE
            holder.videoDuration.text = "01" // You can implement actual duration calculation
        } else {
            holder.playIcon.visibility = View.GONE
            holder.videoDuration.visibility = View.GONE
        }

        // Load profile image
        loadProfileImage(holder.profileImage)

        // Set click listeners
        holder.itemView.setOnClickListener {
            onItemClick(file)
        }

        holder.actionButton.setOnClickListener {
            // Handle action button click (share, delete, etc.)
            showFileActionMenu(file)
        }
    }

    private fun loadBannerAd(holder: BannerAdViewHolder, position: Int) {
        val startTime = System.currentTimeMillis()
        try {
            // Check if ads are enabled
            if (!AdConfig.adsEnabled) {
                holder.adContainer.visibility = View.GONE
                return
            }

            // Limit number of ads per session
            if (bannerAds.size >= MAX_ADS_PER_SESSION) {
                holder.adContainer.visibility = View.GONE
                return
            }

            // Check if we already have an ad for this position
            bannerAds[position]?.let { existingAdView ->
                try {
                    // Remove from previous parent if any
                    (existingAdView.parent as? ViewGroup)?.removeView(existingAdView)

                    holder.adContainer.removeAllViews()
                    holder.adContainer.addView(existingAdView)
                    Log.d(TAG, "Reused existing banner ad for position $position")
                    return
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to reuse banner ad for position $position: ${e.message}")
                    bannerAds.remove(position) // Remove corrupted ad
                }
            }

            // Create new banner ad
            val adView = AdView(context).apply {
                setAdSize(AdSize.LARGE_BANNER) // 320x100 - better for linear layout
                adUnitId = AdConfig.bannerAdUnitId
                
                adListener = object : AdListener() {
                    override fun onAdLoaded() {
                        val loadTime = System.currentTimeMillis() - startTime
                        Log.d(TAG, "Banner ad loaded for position $position")
                            PerformanceManager.recordAdLoadPerformance(
                                adType = "banner",
                                success = true,
                                loadTimeMs = loadTime
                            )
                        // Log analytics
                        AnalyticsManager.logAdEvent("banner_square", "load", true)
                        CrashlyticsManager.logAdEvent("banner_square", "load", true)
                        PerformanceManager.stopTrace("load_banner_ad")

                    }
                    
                    override fun onAdFailedToLoad(adError: LoadAdError) {
                        Log.e(TAG, "Banner ad failed to load for position $position: ${adError.message}")
                        val loadTime = System.currentTimeMillis() - startTime

                        // تسجيل فشل تحميل الإعلان
                        PerformanceManager.recordAdLoadPerformance(
                            adType = "banner",
                            success = false,
                            loadTimeMs = loadTime
                        )
                        // Log analytics
                        AnalyticsManager.logAdEvent("banner_square", "load", false)
                        CrashlyticsManager.logAdEvent("banner_square", "load", false)
                        
                        // Hide the ad container
                        holder.adContainer.visibility = View.GONE
                        PerformanceManager.stopTrace("load_banner_ad")

                    }
                    
                    override fun onAdClicked() {
                        Log.d(TAG, "Banner ad clicked for position $position")
                        AnalyticsManager.logAdEvent("banner_square", "click", true)
                        CrashlyticsManager.logAdEvent("banner_square", "click", true)
                    }
                    
                    override fun onAdOpened() {
                        Log.d(TAG, "Banner ad opened for position $position")
                        AnalyticsManager.logAdEvent("banner_square", "open", true)
                    }
                    
                    override fun onAdClosed() {
                        Log.d(TAG, "Banner ad closed for position $position")
                        AnalyticsManager.logAdEvent("banner_square", "close", true)
                    }
                }
            }

            // Store the ad view
            bannerAds[position] = adView

            // Add to container with full width
            holder.adContainer.removeAllViews()

            // Set layout params for full width
            val layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            adView.layoutParams = layoutParams

            holder.adContainer.addView(adView)
            holder.adContainer.visibility = View.VISIBLE

            // Load the ad with Facebook Bidding support
            val adRequestBuilder = AdRequest.Builder()
            val adRequest = FacebookBiddingHelper.configureAdRequestForBidding(adRequestBuilder).build()
            adView.loadAd(adRequest)
            
            Log.d(TAG, "Loading banner ad for position $position...")

        } catch (e: Exception) {
            Log.e(TAG, "Exception in loadBannerAd for position $position: ${e.message}")
            CrashlyticsManager.logException(e, "Banner ad load failed for position $position")
            
            // Hide the ad container on error
            holder.adContainer.visibility = View.GONE
        }
    }

    private fun isAdPosition(position: Int): Boolean {
        return (position + 1) % (AD_FREQUENCY + 1) == 0 && position > 0
    }

    private fun getMediaPosition(position: Int): Int {
        val adsBefore = position / (AD_FREQUENCY + 1)
        return position - adsBefore
    }

    /**
     * Update data
     */
    fun updateData(newFiles: List<File>) {
        mediaFiles = newFiles
        notifyDataSetChanged()
    }

    /**
     * Refresh all banner ads
     */
    fun refreshBannerAds() {
        try {
            bannerAds.values.forEach { adView ->
                val adRequest = AdRequest.Builder().build()
                adView.loadAd(adRequest)
            }
            Log.d(TAG, "Refreshed ${bannerAds.size} banner ads")
            AnalyticsManager.logEvent("banner_ads_refreshed")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to refresh banner ads: ${e.message}")
            CrashlyticsManager.logException(e, "Banner ads refresh failed")
        }
    }

    /**
     * Clean up banner ads when adapter is destroyed
     */
    fun destroy() {
        try {
            bannerAds.values.forEach { adView ->
                adView.destroy()
            }
            bannerAds.clear()
            Log.d(TAG, "Banner ads destroyed")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to destroy banner ads: ${e.message}")
        }
    }

    // Helper functions
    private fun isVideoFile(file: File): Boolean {
        val videoExtensions = listOf("mp4", "mov", "avi", "mkv", "webm", "3gp")
        return videoExtensions.contains(file.extension.lowercase())
    }

    private fun formatDownloadDate(timestamp: Long): String {
        val now = System.currentTimeMillis()
        val diff = now - timestamp

        return when {
            diff < 60000 -> "📅 Just now"
            diff < 3600000 -> {
                val minutes = (diff / 60000).toInt()
                if (minutes == 1) "📅 1 minute ago" else "📅 ${minutes} minutes ago"
            }
            diff < 86400000 -> {
                val hours = (diff / 3600000).toInt()
                if (hours == 1) "📅 1 hour ago" else "📅 ${hours} hours ago"
            }
            diff < 604800000 -> {
                val days = (diff / 86400000).toInt()
                when (days) {
                    1 -> "📅 Yesterday"
                    2 -> "📅 2 days ago"
                    else -> "📅 ${days} days ago"
                }
            }
            diff < 2592000000L -> {
                val weeks = (diff / 604800000).toInt()
                if (weeks == 1) "📅 1 week ago" else "📅 ${weeks} weeks ago"
            }
            else -> {
                val dateFormat = java.text.SimpleDateFormat("📅 MMM dd, yyyy", java.util.Locale.getDefault())
                dateFormat.format(java.util.Date(timestamp))
            }
        }
    }

    private fun generateFileDescription(file: File): String {
        val fileType = getFileTypeWithIcon(file)
        val fileSize = formatFileSize(file.length())
        return "$fileType • $fileSize"
    }

    private fun getFileTypeWithIcon(file: File): String {
        val extension = file.extension.lowercase()
        return when (extension) {
            "mp4" -> "🎬 MP4 Video"
            "mov" -> "🎬 MOV Video"
            "avi" -> "🎬 AVI Video"
            "mkv" -> "🎬 MKV Video"
            "webm" -> "🎬 WebM Video"
            "3gp" -> "🎬 3GP Video"
            "jpg", "jpeg" -> "📷 JPEG Image"
            "png" -> "📷 PNG Image"
            "gif" -> "📷 GIF Image"
            "webp" -> "📷 WebP Image"
            "bmp" -> "📷 BMP Image"
            "tiff" -> "📷 TIFF Image"
            else -> {
                if (isVideoFile(file)) "🎬 Video File"
                else "📷 Image File"
            }
        }
    }

    private fun formatFileSize(bytes: Long): String {
        if (bytes <= 0) return "0 B"

        val kb = bytes / 1024.0
        val mb = kb / 1024.0
        val gb = mb / 1024.0

        return when {
            gb >= 1 -> String.format("%.1f GB", gb)
            mb >= 1 -> String.format("%.1f MB", mb)
            kb >= 1 -> String.format("%.1f KB", kb)
            else -> "$bytes B"
        }
    }

    private fun loadProfileImage(imageView: ImageView) {
        try {
            // Use the same style as item_recent_download.xml
            imageView.setImageResource(R.drawable.circle_backgound)
            imageView.setBackgroundColor(android.graphics.Color.parseColor("#9C27B0")) // insta_purple
            imageView.scaleType = ImageView.ScaleType.CENTER_CROP
        } catch (e: Exception) {
            imageView.setImageResource(R.drawable.circle_backgound)
            imageView.setBackgroundColor(android.graphics.Color.parseColor("#9C27B0"))
        }
    }

    private fun showFileActionMenu(file: File) {
        // This should be implemented in the activity/fragment that uses this adapter
        // For now, we'll just log it
        Log.d(TAG, "Action menu requested for file: ${file.name}")
    }
}
