-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:77:9-85:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:81:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:79:13-64
	android:exported
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:80:13-37
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:78:13-62
manifest
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:2:1-146:12
INJECTED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:2:1-146:12
INJECTED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:2:1-146:12
INJECTED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:2:1-146:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b04ae20513d4bbf33fbc1e5158096c1\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bdae3aff31b101560f2f3e958dbb2207\transformed\constraintlayout-2.2.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.airbnb.android:lottie:6.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\01bc3666ea39d73f8db0ee9d57ba259b\transformed\jetified-lottie-6.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6f2af2ba51f46d616f14eba4157ce36\transformed\jetified-appcompat-resources-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.preference:preference-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7f75aba89aec943f1a7325e120222f8\transformed\jetified-preference-ktx-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19f3705e3499cc83b2f3aa3cd543b099\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-perf-ktx:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7e9769f3c4b2af8270ad0d6acca89ac\transformed\jetified-firebase-perf-ktx-21.0.2\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-perf:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2329f23c4c3a4bf92b4344d445f02482\transformed\jetified-firebase-perf-21.0.2\AndroidManifest.xml:3:1-23:12
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9197c9cddad2e7ee60ceb2271f7affe0\transformed\appcompat-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6c770c62d3bd53c760435d86e187a34\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9618c8be50c53e76ba7e0043f9d26c1\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-ads:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d0e40e3188866457cee9d0d9de29220\transformed\jetified-play-services-ads-23.5.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e7ef40689e0a48cd0b64e82375d8c5b5\transformed\jetified-glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment-ktx:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbf34e3ed1f6a39abe00495c184a84bb\transformed\jetified-fragment-ktx-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:2:1-32:12
MERGED from [com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec49ca28e4aa5b972b86f27aa77e6aa7\transformed\jetified-firebase-analytics-ktx-22.1.2\AndroidManifest.xml:2:1-17:12
MERGED from [com.google.firebase:firebase-analytics:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f4b0366e9a0d19a278a2fd8b25628d0\transformed\jetified-firebase-analytics-22.1.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:17:1-43:12
MERGED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:17:1-112:12
MERGED from [com.google.android.gms:play-services-ads-base:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91698ad08fc81ae5be6cc96447f841bb\transformed\jetified-play-services-ads-base-23.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd2018f48bcd81640b4f5d5fd9dd29e4\transformed\jetified-play-services-measurement-sdk-22.1.2\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b8b6f31f4cdd068d3cff6bcf581de0b\transformed\jetified-play-services-measurement-impl-22.1.2\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2dfdb8cc08ec45f954637567cc222868\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b5adbea2b2de4a9208e52b723394713\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-config:22.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc2b22665e024b976aa2bd68e6e79f9\transformed\jetified-firebase-config-22.0.1\AndroidManifest.xml:17:1-38:12
MERGED from [com.google.firebase:firebase-appcheck-playintegrity:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430b373a0231a5ac5a64f84d80e35563\transformed\jetified-firebase-appcheck-playintegrity-18.0.0\AndroidManifest.xml:15:1-31:12
MERGED from [com.google.firebase:firebase-crashlytics-ktx:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2af3c9a2590e755876f4d3a775a327e7\transformed\jetified-firebase-crashlytics-ktx-19.2.1\AndroidManifest.xml:15:1-30:12
MERGED from [com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07a2722339bfa0219c25a815d830727d\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-appcheck-ktx:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8aed30e267a3f7957cac475003383c4c\transformed\jetified-firebase-appcheck-ktx-18.0.0\AndroidManifest.xml:15:1-30:12
MERGED from [com.google.firebase:firebase-messaging-ktx:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7d62d36630b8c2669020ef927747254\transformed\jetified-firebase-messaging-ktx-24.1.0\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd549eab9cdb581d5cd730ab22aa1b6\transformed\jetified-firebase-crashlytics-19.2.1\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34162c290514c368f494368d6102f2e8\transformed\jetified-firebase-sessions-2.0.6\AndroidManifest.xml:15:1-35:12
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ce1bda87d7244e8b25ef2bc59b4ecef\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82e68bdc17783eab343147c24d5a4c00\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:15:1-38:12
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e0e1853c5f0749217e440541350556b\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:15:1-34:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25a61a6d44aa83fddee0cb806da6dac6\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b66f86b5b86572c0017201fb289d4897\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47db0c3c64e81c1ebdefe5fa45c68024\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\071658833db178a8621052ffa0849746\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95795258aaeb4e5dd94e03d37b6a147e\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a209725736a512aeeb07effe1494f5b6\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9bca8234a4b643ae95843e35b988c904\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3710d0e93f03964a76ec86efe3ed1436\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0a02b766862637af50254ff229eacac8\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8b9c16e76007874623f211d246eb559\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e68a9805621fd88d94e06e591971e4ac\transformed\jetified-activity-ktx-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8658245f9f116f7a0606fe75b576fa8a\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e51bc40c246215e828403dc89cf28533\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1594b91b3237b12c5b8de2d779fb6d84\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-service:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e79c16e286002ad93abc4373a03a532a\transformed\jetified-lifecycle-service-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e964b93ee241cb4af129d24ec51057e\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f186eec4313af989a8b4c1b8b4609e2d\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0a6539eb97cc032587a4f17e60e2563\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9baa7d41a08b5564af2c938570c83cc2\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ccfe778fc55484235e32b815c40a1b9\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cc06ca8b1e2a8bbc982f00e98575583\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fabfb640e1873288b60a2d5467c32aa8\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd4dde74fe4031e7f9743f34c05041e2\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4baec771a637d57bc55ee706b2cda87a\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23c7b3148a83432ca440c27e9e880ef9\transformed\jetified-window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d9e1f1746b77264d21e356ef9c22536f\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\903e092fc049e07eb9802b2b1d837d55\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aefa0be2a1379bf7c5579172ab80b455\transformed\jetified-datastore-preferences-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b0b5f2d1e20d5a1ab7fb579eff1b5bcf\transformed\jetified-datastore-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-installations-interop:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\89ab9a5ecadcf23563ef4d34e9fd2fd6\transformed\jetified-firebase-installations-interop-17.2.0\AndroidManifest.xml:15:1-19:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d3d528a100de93235d3fb7a9f7af7c1\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81eea7aaa10c95fd99b2df2f9f2424a3\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dcc9d526818c7a75e0d7c18a57184ef4\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aedb6a59da514a3b9c3c71add6ffdec1\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d801c455f340efa8f8416b20622ffc0f\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64d1cc8f3e9c59c96e0297d996aad4c8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\836afbbd056193ebe0d05e0e9f9eac28\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4fa85e59f683340418a53175da069a1c\transformed\jetified-play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e491e8b5e4e0e985082dae465865637\transformed\jetified-play-services-measurement-base-22.1.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c9f1b0cfb3dce6ae7cf4b0f010a4436\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.android.ump:user-messaging-platform:3.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78dc3e21d72b30c0d92d1a962e73a42d\transformed\jetified-user-messaging-platform-3.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c493a0f9edf13d8fdda87e908a4e054e\transformed\jetified-firebase-measurement-connector-20.0.1\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c96b0d8b72c615a79b6ffc473f6a8013\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8dbee909b09865798a439777072c94a2\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98aae4b6350182b44e966e62e2c5a645\transformed\jetified-activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b806ec781ae1ce36ed8118aba1ca70f1\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bce3d13ef6f348e92b95b877ccafee43\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ae7f2fd03dcf3bccc16ddcd5a521f2\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bee9c2993c1609ded6505ae49d8d8031\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\01e1e5e969c3bf2e9c6e929b66b5ab2c\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f68490873ecad1a081b71c74677c3a2e\transformed\webkit-1.11.0-alpha02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b93becd8c8b6aa1bbfd194f4b5feb5bc\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6070ab8100afe1ab261c3bcfc08d0c3\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d58a419118e34eadf0fdff30391c321\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\27c4499ff58b4f7040d1a915337327cf\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e501709b9c45d80c2b6c63be26e6b92\transformed\jetified-core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ace883f1f6be3a16ed67352822cfbdf\transformed\jetified-firebase-config-interop-16.0.1\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf01dc2d83636955506f1575030bb524\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85713e3337aa4c8e7156fef507f40140\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:15:1-35:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b819e2784a2353fd138c4ac129072b84\transformed\jetified-firebase-encoders-json-18.0.1\AndroidManifest.xml:15:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f44bdfbd585fb11d9f29b6f118a1a08\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\93dce291c2195822c3dbddf179a99a85\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1de57f015cb1bae3bdf697d0a607169b\transformed\jetified-gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\326af24406bc23afec3138985d4b7de3\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1f0a5d4ad88ed29dfa4e8cb817057e6\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c21f2fdc6e1ad86a4084687c6614b0d\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\33cfb90f1efcd4db7a9b11cd6d413abe\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bffd1302d81df26aa24278a4897d6076\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b4b836beb76ecd4143a424981f55942\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdc16e1b8883ecf192e9948d1c13071a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:15:1-39:12
MERGED from [com.google.android.datatransport:transport-api:3.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45ab382fba70a81ee5ed6379713375d1\transformed\jetified-transport-api-3.2.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23645d451edec15b499b2c0a1186f68f\transformed\room-runtime-2.2.5\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c12f8a296e9d8daeef51baf2b2bcfe7\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae5814bd972a5a72a00e6d2fd44260e0\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3a7cefa3057a45b67b60968858143e14\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42670948f940cba2894fd9ecca1a0771\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7e13cfcbc333fa1eec9d55be51c4d126\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db23cd459340ffd4d7fbd558134afc88\transformed\sqlite-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c0110004ccd14b78db2ff0aaf909b96\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75fe446d44febdb8131d04f82e66e347\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55bdeb67dc5809a0f541d489bc2e700d\transformed\multidex-2.0.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0536355a89664736f345890e2e0e7534\transformed\jetified-protolite-well-known-types-18.0.0\AndroidManifest.xml:2:1-11:12
	package
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:2:11-69
	tools:ignore
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:4:5-43
application
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:6:5-111:19
INJECTED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:6:5-111:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b04ae20513d4bbf33fbc1e5158096c1\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b04ae20513d4bbf33fbc1e5158096c1\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bdae3aff31b101560f2f3e958dbb2207\transformed\constraintlayout-2.2.0\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bdae3aff31b101560f2f3e958dbb2207\transformed\constraintlayout-2.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:lottie:6.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\01bc3666ea39d73f8db0ee9d57ba259b\transformed\jetified-lottie-6.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:lottie:6.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\01bc3666ea39d73f8db0ee9d57ba259b\transformed\jetified-lottie-6.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-perf-ktx:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7e9769f3c4b2af8270ad0d6acca89ac\transformed\jetified-firebase-perf-ktx-21.0.2\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-perf-ktx:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7e9769f3c4b2af8270ad0d6acca89ac\transformed\jetified-firebase-perf-ktx-21.0.2\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-perf:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2329f23c4c3a4bf92b4344d445f02482\transformed\jetified-firebase-perf-21.0.2\AndroidManifest.xml:10:5-21:19
MERGED from [com.google.firebase:firebase-perf:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2329f23c4c3a4bf92b4344d445f02482\transformed\jetified-firebase-perf-21.0.2\AndroidManifest.xml:10:5-21:19
MERGED from [com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:19:5-30:19
MERGED from [com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:19:5-30:19
MERGED from [com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec49ca28e4aa5b972b86f27aa77e6aa7\transformed\jetified-firebase-analytics-ktx-22.1.2\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec49ca28e4aa5b972b86f27aa77e6aa7\transformed\jetified-firebase-analytics-ktx-22.1.2\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-analytics:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f4b0366e9a0d19a278a2fd8b25628d0\transformed\jetified-firebase-analytics-22.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f4b0366e9a0d19a278a2fd8b25628d0\transformed\jetified-firebase-analytics-22.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:70:5-110:19
MERGED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:70:5-110:19
MERGED from [com.google.android.gms:play-services-ads-base:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91698ad08fc81ae5be6cc96447f841bb\transformed\jetified-play-services-ads-base-23.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-ads-base:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91698ad08fc81ae5be6cc96447f841bb\transformed\jetified-play-services-ads-base-23.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd2018f48bcd81640b4f5d5fd9dd29e4\transformed\jetified-play-services-measurement-sdk-22.1.2\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd2018f48bcd81640b4f5d5fd9dd29e4\transformed\jetified-play-services-measurement-sdk-22.1.2\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b8b6f31f4cdd068d3cff6bcf581de0b\transformed\jetified-play-services-measurement-impl-22.1.2\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b8b6f31f4cdd068d3cff6bcf581de0b\transformed\jetified-play-services-measurement-impl-22.1.2\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2dfdb8cc08ec45f954637567cc222868\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2dfdb8cc08ec45f954637567cc222868\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b5adbea2b2de4a9208e52b723394713\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b5adbea2b2de4a9208e52b723394713\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-config:22.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc2b22665e024b976aa2bd68e6e79f9\transformed\jetified-firebase-config-22.0.1\AndroidManifest.xml:25:5-36:19
MERGED from [com.google.firebase:firebase-config:22.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc2b22665e024b976aa2bd68e6e79f9\transformed\jetified-firebase-config-22.0.1\AndroidManifest.xml:25:5-36:19
MERGED from [com.google.firebase:firebase-appcheck-playintegrity:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430b373a0231a5ac5a64f84d80e35563\transformed\jetified-firebase-appcheck-playintegrity-18.0.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-appcheck-playintegrity:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430b373a0231a5ac5a64f84d80e35563\transformed\jetified-firebase-appcheck-playintegrity-18.0.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-crashlytics-ktx:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2af3c9a2590e755876f4d3a775a327e7\transformed\jetified-firebase-crashlytics-ktx-19.2.1\AndroidManifest.xml:20:5-28:19
MERGED from [com.google.firebase:firebase-crashlytics-ktx:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2af3c9a2590e755876f4d3a775a327e7\transformed\jetified-firebase-crashlytics-ktx-19.2.1\AndroidManifest.xml:20:5-28:19
MERGED from [com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07a2722339bfa0219c25a815d830727d\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07a2722339bfa0219c25a815d830727d\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-appcheck-ktx:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8aed30e267a3f7957cac475003383c4c\transformed\jetified-firebase-appcheck-ktx-18.0.0\AndroidManifest.xml:20:5-28:19
MERGED from [com.google.firebase:firebase-appcheck-ktx:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8aed30e267a3f7957cac475003383c4c\transformed\jetified-firebase-appcheck-ktx-18.0.0\AndroidManifest.xml:20:5-28:19
MERGED from [com.google.firebase:firebase-messaging-ktx:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7d62d36630b8c2669020ef927747254\transformed\jetified-firebase-messaging-ktx-24.1.0\AndroidManifest.xml:22:5-30:19
MERGED from [com.google.firebase:firebase-messaging-ktx:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7d62d36630b8c2669020ef927747254\transformed\jetified-firebase-messaging-ktx-24.1.0\AndroidManifest.xml:22:5-30:19
MERGED from [com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd549eab9cdb581d5cd730ab22aa1b6\transformed\jetified-firebase-crashlytics-19.2.1\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd549eab9cdb581d5cd730ab22aa1b6\transformed\jetified-firebase-crashlytics-19.2.1\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34162c290514c368f494368d6102f2e8\transformed\jetified-firebase-sessions-2.0.6\AndroidManifest.xml:21:5-33:19
MERGED from [com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34162c290514c368f494368d6102f2e8\transformed\jetified-firebase-sessions-2.0.6\AndroidManifest.xml:21:5-33:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ce1bda87d7244e8b25ef2bc59b4ecef\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ce1bda87d7244e8b25ef2bc59b4ecef\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82e68bdc17783eab343147c24d5a4c00\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:25:5-36:19
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82e68bdc17783eab343147c24d5a4c00\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:25:5-36:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e0e1853c5f0749217e440541350556b\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:21:5-32:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e0e1853c5f0749217e440541350556b\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:21:5-32:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25a61a6d44aa83fddee0cb806da6dac6\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25a61a6d44aa83fddee0cb806da6dac6\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b66f86b5b86572c0017201fb289d4897\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b66f86b5b86572c0017201fb289d4897\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:5:5-6:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\071658833db178a8621052ffa0849746\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\071658833db178a8621052ffa0849746\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3710d0e93f03964a76ec86efe3ed1436\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3710d0e93f03964a76ec86efe3ed1436\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd4dde74fe4031e7f9743f34c05041e2\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd4dde74fe4031e7f9743f34c05041e2\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23c7b3148a83432ca440c27e9e880ef9\transformed\jetified-window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23c7b3148a83432ca440c27e9e880ef9\transformed\jetified-window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\903e092fc049e07eb9802b2b1d837d55\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\903e092fc049e07eb9802b2b1d837d55\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d3d528a100de93235d3fb7a9f7af7c1\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d3d528a100de93235d3fb7a9f7af7c1\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81eea7aaa10c95fd99b2df2f9f2424a3\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81eea7aaa10c95fd99b2df2f9f2424a3\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dcc9d526818c7a75e0d7c18a57184ef4\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dcc9d526818c7a75e0d7c18a57184ef4\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64d1cc8f3e9c59c96e0297d996aad4c8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64d1cc8f3e9c59c96e0297d996aad4c8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\836afbbd056193ebe0d05e0e9f9eac28\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\836afbbd056193ebe0d05e0e9f9eac28\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e491e8b5e4e0e985082dae465865637\transformed\jetified-play-services-measurement-base-22.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e491e8b5e4e0e985082dae465865637\transformed\jetified-play-services-measurement-base-22.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c9f1b0cfb3dce6ae7cf4b0f010a4436\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:10:5-16:19
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c9f1b0cfb3dce6ae7cf4b0f010a4436\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:10:5-16:19
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c493a0f9edf13d8fdda87e908a4e054e\transformed\jetified-firebase-measurement-connector-20.0.1\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c493a0f9edf13d8fdda87e908a4e054e\transformed\jetified-firebase-measurement-connector-20.0.1\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c96b0d8b72c615a79b6ffc473f6a8013\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c96b0d8b72c615a79b6ffc473f6a8013\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d58a419118e34eadf0fdff30391c321\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d58a419118e34eadf0fdff30391c321\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf01dc2d83636955506f1575030bb524\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf01dc2d83636955506f1575030bb524\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85713e3337aa4c8e7156fef507f40140\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:25:5-33:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85713e3337aa4c8e7156fef507f40140\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:25:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1f0a5d4ad88ed29dfa4e8cb817057e6\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1f0a5d4ad88ed29dfa4e8cb817057e6\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b4b836beb76ecd4143a424981f55942\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b4b836beb76ecd4143a424981f55942\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdc16e1b8883ecf192e9948d1c13071a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:23:5-37:19
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdc16e1b8883ecf192e9948d1c13071a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:23:5-37:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23645d451edec15b499b2c0a1186f68f\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23645d451edec15b499b2c0a1186f68f\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml
	android:requestLegacyExternalStorage
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:15:9-52
	android:preserveLegacyExternalStorage
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:16:9-53
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d58a419118e34eadf0fdff30391c321\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:13:9-35
	android:label
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:12:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:10:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:17:9-29
	android:icon
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:11:9-38
	android:allowBackup
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:8:9-35
	android:theme
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:14:9-49
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:9:9-65
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:7:9-38
meta-data#com.google.android.gms.ads.APPLICATION_ID
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:18:9-20:47
	android:value
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:20:13-45
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:19:13-69
meta-data#com.facebook.sdk.ApplicationId
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:23:9-25:55
	android:value
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:25:13-52
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:24:13-58
meta-data#com.facebook.sdk.ClientToken
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:26:9-28:61
	android:value
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:28:13-58
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:27:13-56
property#android.adservices.AD_SERVICES_CONFIG
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:31:9-34:48
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:30:9-32:61
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:30:9-32:61
MERGED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:107:9-109:62
MERGED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:107:9-109:62
	android:resource
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:33:13-59
		REJECTED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:32:13-58
		REJECTED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:109:13-59
	tools:replace
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:34:13-45
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:32:13-65
activity#com.app.videofbdownloadfree.SplashActivity
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:37:9-44:20
	android:exported
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:39:13-36
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:38:13-43
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:40:13-43:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:41:17-69
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:41:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:42:17-77
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:42:27-74
activity#com.app.videofbdownloadfree.NoInternetActivity
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:46:9-48:40
	android:exported
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:48:13-37
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:47:13-47
activity#com.app.videofbdownloadfree.MainActivity
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:50:9-53:70
	android:windowSoftInputMode
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:53:13-67
	android:exported
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:52:13-37
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:51:13-41
activity#com.app.videofbdownloadfree.RecentDownloadsActivity
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:56:9-58:40
	android:exported
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:58:13-37
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:57:13-52
activity#com.app.videofbdownloadfree.PermissionsActivity
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:61:9-64:56
	android:exported
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:63:13-37
	android:theme
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:64:13-53
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:62:13-48
activity#com.app.videofbdownloadfree.AdsConsentActivity
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:67:9-70:56
	android:exported
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:69:13-37
	android:theme
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:70:13-53
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:68:13-47
activity#com.app.videofbdownloadfree.SettingsActivity
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:72:9-54
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:72:19-51
activity#com.app.videofbdownloadfree.VideoPlayerActivity
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:73:9-57
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:73:19-54
service#com.app.videofbdownloadfree.SaveItFirebaseMessagingService
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:88:9-94:19
	android:exported
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:90:13-37
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:89:13-59
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:91:13-93:29
action#com.google.firebase.MESSAGING_EVENT
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:92:17-78
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:92:25-75
meta-data#com.google.firebase.messaging.default_notification_icon
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:97:9-99:49
	android:resource
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:99:13-46
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:98:13-83
meta-data#com.google.firebase.messaging.default_notification_color
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:102:9-104:54
	android:resource
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:104:13-51
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:103:13-84
meta-data#com.google.firebase.messaging.default_notification_channel_id
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:107:9-109:52
	android:value
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:109:13-49
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:108:13-89
uses-feature#android.hardware.camera
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:113:5-85
	android:required
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:113:58-82
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:113:19-57
uses-feature#android.hardware.telephony
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:114:5-88
	android:required
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:114:61-85
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:114:19-60
uses-feature#android.hardware.location.gps
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:115:5-91
	android:required
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:115:64-88
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:115:19-63
uses-feature#android.hardware.sensor.accelerometer
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:116:5-99
	android:required
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:116:72-96
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:116:19-71
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:119:5-67
MERGED from [com.google.firebase:firebase-perf:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2329f23c4c3a4bf92b4344d445f02482\transformed\jetified-firebase-perf-21.0.2\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-perf:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2329f23c4c3a4bf92b4344d445f02482\transformed\jetified-firebase-perf-21.0.2\AndroidManifest.xml:8:5-67
MERGED from [com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:15:5-67
MERGED from [com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:15:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b8b6f31f4cdd068d3cff6bcf581de0b\transformed\jetified-play-services-measurement-impl-22.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b8b6f31f4cdd068d3cff6bcf581de0b\transformed\jetified-play-services-measurement-impl-22.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-config:22.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc2b22665e024b976aa2bd68e6e79f9\transformed\jetified-firebase-config-22.0.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-config:22.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc2b22665e024b976aa2bd68e6e79f9\transformed\jetified-firebase-config-22.0.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd549eab9cdb581d5cd730ab22aa1b6\transformed\jetified-firebase-crashlytics-19.2.1\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd549eab9cdb581d5cd730ab22aa1b6\transformed\jetified-firebase-crashlytics-19.2.1\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ce1bda87d7244e8b25ef2bc59b4ecef\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ce1bda87d7244e8b25ef2bc59b4ecef\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82e68bdc17783eab343147c24d5a4c00\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82e68bdc17783eab343147c24d5a4c00\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aedb6a59da514a3b9c3c71add6ffdec1\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aedb6a59da514a3b9c3c71add6ffdec1\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4fa85e59f683340418a53175da069a1c\transformed\jetified-play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4fa85e59f683340418a53175da069a1c\transformed\jetified-play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85713e3337aa4c8e7156fef507f40140\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85713e3337aa4c8e7156fef507f40140\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:23:5-67
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:119:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:120:5-79
MERGED from [com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:16:5-79
MERGED from [com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:16:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b8b6f31f4cdd068d3cff6bcf581de0b\transformed\jetified-play-services-measurement-impl-22.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b8b6f31f4cdd068d3cff6bcf581de0b\transformed\jetified-play-services-measurement-impl-22.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.firebase:firebase-config:22.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc2b22665e024b976aa2bd68e6e79f9\transformed\jetified-firebase-config-22.0.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-config:22.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc2b22665e024b976aa2bd68e6e79f9\transformed\jetified-firebase-config-22.0.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ce1bda87d7244e8b25ef2bc59b4ecef\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ce1bda87d7244e8b25ef2bc59b4ecef\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82e68bdc17783eab343147c24d5a4c00\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82e68bdc17783eab343147c24d5a4c00\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:22:5-79
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:26:5-79
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aedb6a59da514a3b9c3c71add6ffdec1\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aedb6a59da514a3b9c3c71add6ffdec1\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4fa85e59f683340418a53175da069a1c\transformed\jetified-play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4fa85e59f683340418a53175da069a1c\transformed\jetified-play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85713e3337aa4c8e7156fef507f40140\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85713e3337aa4c8e7156fef507f40140\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdc16e1b8883ecf192e9948d1c13071a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:20:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdc16e1b8883ecf192e9948d1c13071a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:20:5-79
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:120:22-76
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:123:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b8b6f31f4cdd068d3cff6bcf581de0b\transformed\jetified-play-services-measurement-impl-22.1.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b8b6f31f4cdd068d3cff6bcf581de0b\transformed\jetified-play-services-measurement-impl-22.1.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:24:5-68
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aedb6a59da514a3b9c3c71add6ffdec1\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aedb6a59da514a3b9c3c71add6ffdec1\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4fa85e59f683340418a53175da069a1c\transformed\jetified-play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4fa85e59f683340418a53175da069a1c\transformed\jetified-play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:25:5-68
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:123:22-65
uses-permission#android.permission.VIBRATE
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:124:5-66
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:124:22-63
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:127:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:23:5-77
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:127:22-74
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:128:5-129:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:129:9-35
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:128:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:130:5-131:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:131:9-35
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:130:22-78
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:133:5-76
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:133:22-73
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:134:5-75
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:134:22-72
uses-permission#android.permission.READ_MEDIA_VISUAL_USER_SELECTED
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:136:5-90
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:136:22-87
uses-feature#android.software.leanback
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:138:5-87
	android:required
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:138:60-84
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:138:19-59
uses-feature#android.hardware.type.automotive
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:140:5-94
	android:required
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:140:67-91
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:140:19-66
uses-feature#org.chromium.arc
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:142:5-78
	android:required
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:142:51-75
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:142:19-50
uses-feature#android.hardware.touchscreen
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:143:5-145:36
	android:required
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:145:9-33
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:144:9-52
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:82:13-84:54
	android:resource
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:84:17-51
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml:83:17-67
uses-sdk
INJECTED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b04ae20513d4bbf33fbc1e5158096c1\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b04ae20513d4bbf33fbc1e5158096c1\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bdae3aff31b101560f2f3e958dbb2207\transformed\constraintlayout-2.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bdae3aff31b101560f2f3e958dbb2207\transformed\constraintlayout-2.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\01bc3666ea39d73f8db0ee9d57ba259b\transformed\jetified-lottie-6.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\01bc3666ea39d73f8db0ee9d57ba259b\transformed\jetified-lottie-6.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6f2af2ba51f46d616f14eba4157ce36\transformed\jetified-appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6f2af2ba51f46d616f14eba4157ce36\transformed\jetified-appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.preference:preference-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7f75aba89aec943f1a7325e120222f8\transformed\jetified-preference-ktx-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7f75aba89aec943f1a7325e120222f8\transformed\jetified-preference-ktx-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19f3705e3499cc83b2f3aa3cd543b099\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19f3705e3499cc83b2f3aa3cd543b099\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-perf-ktx:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7e9769f3c4b2af8270ad0d6acca89ac\transformed\jetified-firebase-perf-ktx-21.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-perf-ktx:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7e9769f3c4b2af8270ad0d6acca89ac\transformed\jetified-firebase-perf-ktx-21.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-perf:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2329f23c4c3a4bf92b4344d445f02482\transformed\jetified-firebase-perf-21.0.2\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-perf:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2329f23c4c3a4bf92b4344d445f02482\transformed\jetified-firebase-perf-21.0.2\AndroidManifest.xml:6:5-44
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9197c9cddad2e7ee60ceb2271f7affe0\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9197c9cddad2e7ee60ceb2271f7affe0\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6c770c62d3bd53c760435d86e187a34\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6c770c62d3bd53c760435d86e187a34\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9618c8be50c53e76ba7e0043f9d26c1\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9618c8be50c53e76ba7e0043f9d26c1\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d0e40e3188866457cee9d0d9de29220\transformed\jetified-play-services-ads-23.5.0\AndroidManifest.xml:21:5-23:52
MERGED from [com.google.android.gms:play-services-ads:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d0e40e3188866457cee9d0d9de29220\transformed\jetified-play-services-ads-23.5.0\AndroidManifest.xml:21:5-23:52
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e7ef40689e0a48cd0b64e82375d8c5b5\transformed\jetified-glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e7ef40689e0a48cd0b64e82375d8c5b5\transformed\jetified-glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.fragment:fragment-ktx:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbf34e3ed1f6a39abe00495c184a84bb\transformed\jetified-fragment-ktx-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbf34e3ed1f6a39abe00495c184a84bb\transformed\jetified-fragment-ktx-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec49ca28e4aa5b972b86f27aa77e6aa7\transformed\jetified-firebase-analytics-ktx-22.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec49ca28e4aa5b972b86f27aa77e6aa7\transformed\jetified-firebase-analytics-ktx-22.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f4b0366e9a0d19a278a2fd8b25628d0\transformed\jetified-firebase-analytics-22.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f4b0366e9a0d19a278a2fd8b25628d0\transformed\jetified-firebase-analytics-22.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-ads-base:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91698ad08fc81ae5be6cc96447f841bb\transformed\jetified-play-services-ads-base-23.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads-base:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91698ad08fc81ae5be6cc96447f841bb\transformed\jetified-play-services-ads-base-23.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd2018f48bcd81640b4f5d5fd9dd29e4\transformed\jetified-play-services-measurement-sdk-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd2018f48bcd81640b4f5d5fd9dd29e4\transformed\jetified-play-services-measurement-sdk-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b8b6f31f4cdd068d3cff6bcf581de0b\transformed\jetified-play-services-measurement-impl-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b8b6f31f4cdd068d3cff6bcf581de0b\transformed\jetified-play-services-measurement-impl-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2dfdb8cc08ec45f954637567cc222868\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2dfdb8cc08ec45f954637567cc222868\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b5adbea2b2de4a9208e52b723394713\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b5adbea2b2de4a9208e52b723394713\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-config:22.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc2b22665e024b976aa2bd68e6e79f9\transformed\jetified-firebase-config-22.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-config:22.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc2b22665e024b976aa2bd68e6e79f9\transformed\jetified-firebase-config-22.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-appcheck-playintegrity:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430b373a0231a5ac5a64f84d80e35563\transformed\jetified-firebase-appcheck-playintegrity-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck-playintegrity:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430b373a0231a5ac5a64f84d80e35563\transformed\jetified-firebase-appcheck-playintegrity-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-crashlytics-ktx:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2af3c9a2590e755876f4d3a775a327e7\transformed\jetified-firebase-crashlytics-ktx-19.2.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-crashlytics-ktx:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2af3c9a2590e755876f4d3a775a327e7\transformed\jetified-firebase-crashlytics-ktx-19.2.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07a2722339bfa0219c25a815d830727d\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07a2722339bfa0219c25a815d830727d\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-appcheck-ktx:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8aed30e267a3f7957cac475003383c4c\transformed\jetified-firebase-appcheck-ktx-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck-ktx:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8aed30e267a3f7957cac475003383c4c\transformed\jetified-firebase-appcheck-ktx-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-messaging-ktx:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7d62d36630b8c2669020ef927747254\transformed\jetified-firebase-messaging-ktx-24.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging-ktx:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7d62d36630b8c2669020ef927747254\transformed\jetified-firebase-messaging-ktx-24.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd549eab9cdb581d5cd730ab22aa1b6\transformed\jetified-firebase-crashlytics-19.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd549eab9cdb581d5cd730ab22aa1b6\transformed\jetified-firebase-crashlytics-19.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34162c290514c368f494368d6102f2e8\transformed\jetified-firebase-sessions-2.0.6\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34162c290514c368f494368d6102f2e8\transformed\jetified-firebase-sessions-2.0.6\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ce1bda87d7244e8b25ef2bc59b4ecef\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ce1bda87d7244e8b25ef2bc59b4ecef\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82e68bdc17783eab343147c24d5a4c00\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82e68bdc17783eab343147c24d5a4c00\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e0e1853c5f0749217e440541350556b\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e0e1853c5f0749217e440541350556b\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25a61a6d44aa83fddee0cb806da6dac6\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25a61a6d44aa83fddee0cb806da6dac6\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b66f86b5b86572c0017201fb289d4897\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b66f86b5b86572c0017201fb289d4897\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:4:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47db0c3c64e81c1ebdefe5fa45c68024\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47db0c3c64e81c1ebdefe5fa45c68024\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\071658833db178a8621052ffa0849746\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\071658833db178a8621052ffa0849746\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95795258aaeb4e5dd94e03d37b6a147e\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95795258aaeb4e5dd94e03d37b6a147e\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a209725736a512aeeb07effe1494f5b6\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a209725736a512aeeb07effe1494f5b6\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9bca8234a4b643ae95843e35b988c904\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9bca8234a4b643ae95843e35b988c904\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3710d0e93f03964a76ec86efe3ed1436\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3710d0e93f03964a76ec86efe3ed1436\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0a02b766862637af50254ff229eacac8\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0a02b766862637af50254ff229eacac8\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8b9c16e76007874623f211d246eb559\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8b9c16e76007874623f211d246eb559\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e68a9805621fd88d94e06e591971e4ac\transformed\jetified-activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e68a9805621fd88d94e06e591971e4ac\transformed\jetified-activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8658245f9f116f7a0606fe75b576fa8a\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8658245f9f116f7a0606fe75b576fa8a\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e51bc40c246215e828403dc89cf28533\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e51bc40c246215e828403dc89cf28533\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1594b91b3237b12c5b8de2d779fb6d84\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1594b91b3237b12c5b8de2d779fb6d84\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e79c16e286002ad93abc4373a03a532a\transformed\jetified-lifecycle-service-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e79c16e286002ad93abc4373a03a532a\transformed\jetified-lifecycle-service-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e964b93ee241cb4af129d24ec51057e\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e964b93ee241cb4af129d24ec51057e\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f186eec4313af989a8b4c1b8b4609e2d\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f186eec4313af989a8b4c1b8b4609e2d\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0a6539eb97cc032587a4f17e60e2563\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0a6539eb97cc032587a4f17e60e2563\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9baa7d41a08b5564af2c938570c83cc2\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9baa7d41a08b5564af2c938570c83cc2\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ccfe778fc55484235e32b815c40a1b9\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ccfe778fc55484235e32b815c40a1b9\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cc06ca8b1e2a8bbc982f00e98575583\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cc06ca8b1e2a8bbc982f00e98575583\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fabfb640e1873288b60a2d5467c32aa8\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fabfb640e1873288b60a2d5467c32aa8\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd4dde74fe4031e7f9743f34c05041e2\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd4dde74fe4031e7f9743f34c05041e2\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4baec771a637d57bc55ee706b2cda87a\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4baec771a637d57bc55ee706b2cda87a\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23c7b3148a83432ca440c27e9e880ef9\transformed\jetified-window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23c7b3148a83432ca440c27e9e880ef9\transformed\jetified-window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d9e1f1746b77264d21e356ef9c22536f\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d9e1f1746b77264d21e356ef9c22536f\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\903e092fc049e07eb9802b2b1d837d55\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\903e092fc049e07eb9802b2b1d837d55\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aefa0be2a1379bf7c5579172ab80b455\transformed\jetified-datastore-preferences-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aefa0be2a1379bf7c5579172ab80b455\transformed\jetified-datastore-preferences-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b0b5f2d1e20d5a1ab7fb579eff1b5bcf\transformed\jetified-datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b0b5f2d1e20d5a1ab7fb579eff1b5bcf\transformed\jetified-datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-installations-interop:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\89ab9a5ecadcf23563ef4d34e9fd2fd6\transformed\jetified-firebase-installations-interop-17.2.0\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\89ab9a5ecadcf23563ef4d34e9fd2fd6\transformed\jetified-firebase-installations-interop-17.2.0\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d3d528a100de93235d3fb7a9f7af7c1\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d3d528a100de93235d3fb7a9f7af7c1\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81eea7aaa10c95fd99b2df2f9f2424a3\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81eea7aaa10c95fd99b2df2f9f2424a3\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dcc9d526818c7a75e0d7c18a57184ef4\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dcc9d526818c7a75e0d7c18a57184ef4\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aedb6a59da514a3b9c3c71add6ffdec1\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aedb6a59da514a3b9c3c71add6ffdec1\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d801c455f340efa8f8416b20622ffc0f\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d801c455f340efa8f8416b20622ffc0f\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64d1cc8f3e9c59c96e0297d996aad4c8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64d1cc8f3e9c59c96e0297d996aad4c8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\836afbbd056193ebe0d05e0e9f9eac28\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\836afbbd056193ebe0d05e0e9f9eac28\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4fa85e59f683340418a53175da069a1c\transformed\jetified-play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4fa85e59f683340418a53175da069a1c\transformed\jetified-play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e491e8b5e4e0e985082dae465865637\transformed\jetified-play-services-measurement-base-22.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e491e8b5e4e0e985082dae465865637\transformed\jetified-play-services-measurement-base-22.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c9f1b0cfb3dce6ae7cf4b0f010a4436\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c9f1b0cfb3dce6ae7cf4b0f010a4436\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.ump:user-messaging-platform:3.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78dc3e21d72b30c0d92d1a962e73a42d\transformed\jetified-user-messaging-platform-3.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.ump:user-messaging-platform:3.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78dc3e21d72b30c0d92d1a962e73a42d\transformed\jetified-user-messaging-platform-3.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c493a0f9edf13d8fdda87e908a4e054e\transformed\jetified-firebase-measurement-connector-20.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c493a0f9edf13d8fdda87e908a4e054e\transformed\jetified-firebase-measurement-connector-20.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c96b0d8b72c615a79b6ffc473f6a8013\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c96b0d8b72c615a79b6ffc473f6a8013\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8dbee909b09865798a439777072c94a2\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8dbee909b09865798a439777072c94a2\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98aae4b6350182b44e966e62e2c5a645\transformed\jetified-activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98aae4b6350182b44e966e62e2c5a645\transformed\jetified-activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b806ec781ae1ce36ed8118aba1ca70f1\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b806ec781ae1ce36ed8118aba1ca70f1\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bce3d13ef6f348e92b95b877ccafee43\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bce3d13ef6f348e92b95b877ccafee43\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ae7f2fd03dcf3bccc16ddcd5a521f2\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ae7f2fd03dcf3bccc16ddcd5a521f2\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bee9c2993c1609ded6505ae49d8d8031\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bee9c2993c1609ded6505ae49d8d8031\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\01e1e5e969c3bf2e9c6e929b66b5ab2c\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\01e1e5e969c3bf2e9c6e929b66b5ab2c\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f68490873ecad1a081b71c74677c3a2e\transformed\webkit-1.11.0-alpha02\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f68490873ecad1a081b71c74677c3a2e\transformed\webkit-1.11.0-alpha02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b93becd8c8b6aa1bbfd194f4b5feb5bc\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b93becd8c8b6aa1bbfd194f4b5feb5bc\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6070ab8100afe1ab261c3bcfc08d0c3\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6070ab8100afe1ab261c3bcfc08d0c3\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d58a419118e34eadf0fdff30391c321\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d58a419118e34eadf0fdff30391c321\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\27c4499ff58b4f7040d1a915337327cf\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\27c4499ff58b4f7040d1a915337327cf\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e501709b9c45d80c2b6c63be26e6b92\transformed\jetified-core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e501709b9c45d80c2b6c63be26e6b92\transformed\jetified-core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ace883f1f6be3a16ed67352822cfbdf\transformed\jetified-firebase-config-interop-16.0.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ace883f1f6be3a16ed67352822cfbdf\transformed\jetified-firebase-config-interop-16.0.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf01dc2d83636955506f1575030bb524\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf01dc2d83636955506f1575030bb524\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85713e3337aa4c8e7156fef507f40140\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85713e3337aa4c8e7156fef507f40140\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b819e2784a2353fd138c4ac129072b84\transformed\jetified-firebase-encoders-json-18.0.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b819e2784a2353fd138c4ac129072b84\transformed\jetified-firebase-encoders-json-18.0.1\AndroidManifest.xml:18:5-20:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f44bdfbd585fb11d9f29b6f118a1a08\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f44bdfbd585fb11d9f29b6f118a1a08\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\93dce291c2195822c3dbddf179a99a85\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\93dce291c2195822c3dbddf179a99a85\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1de57f015cb1bae3bdf697d0a607169b\transformed\jetified-gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1de57f015cb1bae3bdf697d0a607169b\transformed\jetified-gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\326af24406bc23afec3138985d4b7de3\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\326af24406bc23afec3138985d4b7de3\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1f0a5d4ad88ed29dfa4e8cb817057e6\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1f0a5d4ad88ed29dfa4e8cb817057e6\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c21f2fdc6e1ad86a4084687c6614b0d\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c21f2fdc6e1ad86a4084687c6614b0d\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\33cfb90f1efcd4db7a9b11cd6d413abe\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\33cfb90f1efcd4db7a9b11cd6d413abe\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bffd1302d81df26aa24278a4897d6076\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bffd1302d81df26aa24278a4897d6076\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b4b836beb76ecd4143a424981f55942\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b4b836beb76ecd4143a424981f55942\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdc16e1b8883ecf192e9948d1c13071a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdc16e1b8883ecf192e9948d1c13071a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45ab382fba70a81ee5ed6379713375d1\transformed\jetified-transport-api-3.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45ab382fba70a81ee5ed6379713375d1\transformed\jetified-transport-api-3.2.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23645d451edec15b499b2c0a1186f68f\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23645d451edec15b499b2c0a1186f68f\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c12f8a296e9d8daeef51baf2b2bcfe7\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c12f8a296e9d8daeef51baf2b2bcfe7\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae5814bd972a5a72a00e6d2fd44260e0\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae5814bd972a5a72a00e6d2fd44260e0\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3a7cefa3057a45b67b60968858143e14\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3a7cefa3057a45b67b60968858143e14\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42670948f940cba2894fd9ecca1a0771\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42670948f940cba2894fd9ecca1a0771\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7e13cfcbc333fa1eec9d55be51c4d126\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7e13cfcbc333fa1eec9d55be51c4d126\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db23cd459340ffd4d7fbd558134afc88\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db23cd459340ffd4d7fbd558134afc88\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c0110004ccd14b78db2ff0aaf909b96\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c0110004ccd14b78db2ff0aaf909b96\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75fe446d44febdb8131d04f82e66e347\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75fe446d44febdb8131d04f82e66e347\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55bdeb67dc5809a0f541d489bc2e700d\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55bdeb67dc5809a0f541d489bc2e700d\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0536355a89664736f345890e2e0e7534\transformed\jetified-protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0536355a89664736f345890e2e0e7534\transformed\jetified-protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from [com.google.android.gms:play-services-ads:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d0e40e3188866457cee9d0d9de29220\transformed\jetified-play-services-ads-23.5.0\AndroidManifest.xml:23:9-49
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\AndroidManifest.xml
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-perf-ktx:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7e9769f3c4b2af8270ad0d6acca89ac\transformed\jetified-firebase-perf-ktx-21.0.2\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-perf:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2329f23c4c3a4bf92b4344d445f02482\transformed\jetified-firebase-perf-21.0.2\AndroidManifest.xml:11:9-20:19
MERGED from [com.google.firebase:firebase-perf:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2329f23c4c3a4bf92b4344d445f02482\transformed\jetified-firebase-perf-21.0.2\AndroidManifest.xml:11:9-20:19
MERGED from [com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec49ca28e4aa5b972b86f27aa77e6aa7\transformed\jetified-firebase-analytics-ktx-22.1.2\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec49ca28e4aa5b972b86f27aa77e6aa7\transformed\jetified-firebase-analytics-ktx-22.1.2\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.firebase:firebase-config:22.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc2b22665e024b976aa2bd68e6e79f9\transformed\jetified-firebase-config-22.0.1\AndroidManifest.xml:26:9-35:19
MERGED from [com.google.firebase:firebase-config:22.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc2b22665e024b976aa2bd68e6e79f9\transformed\jetified-firebase-config-22.0.1\AndroidManifest.xml:26:9-35:19
MERGED from [com.google.firebase:firebase-appcheck-playintegrity:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430b373a0231a5ac5a64f84d80e35563\transformed\jetified-firebase-appcheck-playintegrity-18.0.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-appcheck-playintegrity:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430b373a0231a5ac5a64f84d80e35563\transformed\jetified-firebase-appcheck-playintegrity-18.0.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-crashlytics-ktx:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2af3c9a2590e755876f4d3a775a327e7\transformed\jetified-firebase-crashlytics-ktx-19.2.1\AndroidManifest.xml:21:9-27:19
MERGED from [com.google.firebase:firebase-crashlytics-ktx:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2af3c9a2590e755876f4d3a775a327e7\transformed\jetified-firebase-crashlytics-ktx-19.2.1\AndroidManifest.xml:21:9-27:19
MERGED from [com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07a2722339bfa0219c25a815d830727d\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07a2722339bfa0219c25a815d830727d\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-appcheck-ktx:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8aed30e267a3f7957cac475003383c4c\transformed\jetified-firebase-appcheck-ktx-18.0.0\AndroidManifest.xml:21:9-27:19
MERGED from [com.google.firebase:firebase-appcheck-ktx:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8aed30e267a3f7957cac475003383c4c\transformed\jetified-firebase-appcheck-ktx-18.0.0\AndroidManifest.xml:21:9-27:19
MERGED from [com.google.firebase:firebase-messaging-ktx:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7d62d36630b8c2669020ef927747254\transformed\jetified-firebase-messaging-ktx-24.1.0\AndroidManifest.xml:23:9-29:19
MERGED from [com.google.firebase:firebase-messaging-ktx:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7d62d36630b8c2669020ef927747254\transformed\jetified-firebase-messaging-ktx-24.1.0\AndroidManifest.xml:23:9-29:19
MERGED from [com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd549eab9cdb581d5cd730ab22aa1b6\transformed\jetified-firebase-crashlytics-19.2.1\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd549eab9cdb581d5cd730ab22aa1b6\transformed\jetified-firebase-crashlytics-19.2.1\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34162c290514c368f494368d6102f2e8\transformed\jetified-firebase-sessions-2.0.6\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34162c290514c368f494368d6102f2e8\transformed\jetified-firebase-sessions-2.0.6\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ce1bda87d7244e8b25ef2bc59b4ecef\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ce1bda87d7244e8b25ef2bc59b4ecef\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82e68bdc17783eab343147c24d5a4c00\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:26:9-35:19
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82e68bdc17783eab343147c24d5a4c00\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:26:9-35:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e0e1853c5f0749217e440541350556b\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:22:9-31:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e0e1853c5f0749217e440541350556b\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:22:9-31:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25a61a6d44aa83fddee0cb806da6dac6\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25a61a6d44aa83fddee0cb806da6dac6\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c9f1b0cfb3dce6ae7cf4b0f010a4436\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:11:9-15:19
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c9f1b0cfb3dce6ae7cf4b0f010a4436\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:11:9-15:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf01dc2d83636955506f1575030bb524\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf01dc2d83636955506f1575030bb524\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [com.google.firebase:firebase-perf-ktx:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7e9769f3c4b2af8270ad0d6acca89ac\transformed\jetified-firebase-perf-ktx-21.0.2\AndroidManifest.xml:11:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-perf-ktx:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7e9769f3c4b2af8270ad0d6acca89ac\transformed\jetified-firebase-perf-ktx-21.0.2\AndroidManifest.xml:10:13-84
meta-data#com.google.firebase.components:com.google.firebase.perf.ktx.FirebasePerfLegacyRegistrar
ADDED from [com.google.firebase:firebase-perf-ktx:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7e9769f3c4b2af8270ad0d6acca89ac\transformed\jetified-firebase-perf-ktx-21.0.2\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-perf-ktx:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7e9769f3c4b2af8270ad0d6acca89ac\transformed\jetified-firebase-perf-ktx-21.0.2\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-perf-ktx:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7e9769f3c4b2af8270ad0d6acca89ac\transformed\jetified-firebase-perf-ktx-21.0.2\AndroidManifest.xml:13:17-119
meta-data#com.google.firebase.components:com.google.firebase.perf.FirebasePerfKtxRegistrar
ADDED from [com.google.firebase:firebase-perf:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2329f23c4c3a4bf92b4344d445f02482\transformed\jetified-firebase-perf-21.0.2\AndroidManifest.xml:14:13-16:85
	android:value
		ADDED from [com.google.firebase:firebase-perf:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2329f23c4c3a4bf92b4344d445f02482\transformed\jetified-firebase-perf-21.0.2\AndroidManifest.xml:16:17-82
	android:name
		ADDED from [com.google.firebase:firebase-perf:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2329f23c4c3a4bf92b4344d445f02482\transformed\jetified-firebase-perf-21.0.2\AndroidManifest.xml:15:17-112
meta-data#com.google.firebase.components:com.google.firebase.perf.FirebasePerfRegistrar
ADDED from [com.google.firebase:firebase-perf:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2329f23c4c3a4bf92b4344d445f02482\transformed\jetified-firebase-perf-21.0.2\AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-perf:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2329f23c4c3a4bf92b4344d445f02482\transformed\jetified-firebase-perf-21.0.2\AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-perf:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2329f23c4c3a4bf92b4344d445f02482\transformed\jetified-firebase-perf-21.0.2\AndroidManifest.xml:18:17-109
queries
ADDED from [com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:11:5-13:15
MERGED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:35:5-68:15
MERGED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:35:5-68:15
package#com.facebook.katana
ADDED from [com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:12:9-55
	android:name
		ADDED from [com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:12:18-52
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:17:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b8b6f31f4cdd068d3cff6bcf581de0b\transformed\jetified-play-services-measurement-impl-22.1.2\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b8b6f31f4cdd068d3cff6bcf581de0b\transformed\jetified-play-services-measurement-impl-22.1.2\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2dfdb8cc08ec45f954637567cc222868\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2dfdb8cc08ec45f954637567cc222868\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4fa85e59f683340418a53175da069a1c\transformed\jetified-play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4fa85e59f683340418a53175da069a1c\transformed\jetified-play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:17:22-76
activity#com.facebook.ads.AudienceNetworkActivity
ADDED from [com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:20:9-24:75
	android:exported
		ADDED from [com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:23:13-37
	android:configChanges
		ADDED from [com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:22:13-106
	android:theme
		ADDED from [com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:24:13-72
	android:name
		ADDED from [com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:21:13-68
provider#com.facebook.ads.AudienceNetworkContentProvider
ADDED from [com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:26:9-29:40
	android:authorities
		ADDED from [com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:28:13-82
	android:exported
		ADDED from [com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:29:13-37
	android:name
		ADDED from [com.facebook.android:audience-network-sdk:6.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\AndroidManifest.xml:27:13-75
meta-data#com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar
ADDED from [com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec49ca28e4aa5b972b86f27aa77e6aa7\transformed\jetified-firebase-analytics-ktx-22.1.2\AndroidManifest.xml:11:13-13:85
	android:value
		ADDED from [com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec49ca28e4aa5b972b86f27aa77e6aa7\transformed\jetified-firebase-analytics-ktx-22.1.2\AndroidManifest.xml:13:17-82
	android:name
		ADDED from [com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec49ca28e4aa5b972b86f27aa77e6aa7\transformed\jetified-firebase-analytics-ktx-22.1.2\AndroidManifest.xml:12:17-129
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:28:5-88
MERGED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:28:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4fa85e59f683340418a53175da069a1c\transformed\jetified-play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4fa85e59f683340418a53175da069a1c\transformed\jetified-play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4fa85e59f683340418a53175da069a1c\transformed\jetified-play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4fa85e59f683340418a53175da069a1c\transformed\jetified-play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:27:22-79
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:37:13-39:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:39:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80568f57bbcb189ab12548948af80740\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:38:17-139
uses-permission#android.permission.ACCESS_ADSERVICES_TOPICS
ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:29:5-83
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:29:22-80
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:30:5-32:31
REJECTED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:27:5-81
	tools:node
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:32:9-28
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:31:9-65
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:38:9-44:18
action#android.intent.action.VIEW
ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:39:13-65
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:39:21-62
category#android.intent.category.BROWSABLE
ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:41:13-74
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:41:23-71
data
ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:43:13-44
	android:scheme
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:43:19-41
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:47:9-49:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:48:13-90
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:48:21-87
intent#action:name:android.intent.action.INSERT+data:mimeType:vnd.android.cursor.dir/event
ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:52:9-56:18
action#android.intent.action.INSERT
ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:53:13-67
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:53:21-64
intent#action:name:android.intent.action.VIEW+data:scheme:sms
ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:57:9-61:18
intent#action:name:android.intent.action.DIAL+data:path:tel:
ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:62:9-66:18
action#android.intent.action.DIAL
ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:63:13-65
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:63:21-62
activity#com.google.android.gms.ads.AdActivity
ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:73:9-78:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:76:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:78:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:75:13-122
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:77:13-61
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:74:13-65
provider#com.google.android.gms.ads.MobileAdsInitProvider
ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:80:9-85:43
	android:authorities
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:82:13-73
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:83:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:85:13-40
	android:initOrder
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:84:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:81:13-76
service#com.google.android.gms.ads.AdService
ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:87:9-91:43
	android:enabled
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:89:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:90:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:91:13-40
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:88:13-64
activity#com.google.android.gms.ads.OutOfContextTestingActivity
ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:93:9-97:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:96:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:97:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:95:13-122
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:94:13-82
activity#com.google.android.gms.ads.NotificationHandlerActivity
ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:98:9-105:43
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:100:13-46
	android:launchMode
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:102:13-44
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:101:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:105:13-40
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:104:13-72
	android:taskAffinity
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:103:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30ee5621c1b069f1fe90312e9513327f\transformed\jetified-play-services-ads-lite-23.5.0\AndroidManifest.xml:99:13-82
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b8b6f31f4cdd068d3cff6bcf581de0b\transformed\jetified-play-services-measurement-impl-22.1.2\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b8b6f31f4cdd068d3cff6bcf581de0b\transformed\jetified-play-services-measurement-impl-22.1.2\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4dbebc2e7ea9f1947919c184a6d7e86\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:40:13-87
meta-data#com.google.firebase.components:com.google.firebase.remoteconfig.FirebaseRemoteConfigKtxRegistrar
ADDED from [com.google.firebase:firebase-config:22.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc2b22665e024b976aa2bd68e6e79f9\transformed\jetified-firebase-config-22.0.1\AndroidManifest.xml:29:13-31:85
	android:value
		ADDED from [com.google.firebase:firebase-config:22.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc2b22665e024b976aa2bd68e6e79f9\transformed\jetified-firebase-config-22.0.1\AndroidManifest.xml:31:17-82
	android:name
		ADDED from [com.google.firebase:firebase-config:22.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc2b22665e024b976aa2bd68e6e79f9\transformed\jetified-firebase-config-22.0.1\AndroidManifest.xml:30:17-128
meta-data#com.google.firebase.components:com.google.firebase.remoteconfig.RemoteConfigRegistrar
ADDED from [com.google.firebase:firebase-config:22.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc2b22665e024b976aa2bd68e6e79f9\transformed\jetified-firebase-config-22.0.1\AndroidManifest.xml:32:13-34:85
	android:value
		ADDED from [com.google.firebase:firebase-config:22.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc2b22665e024b976aa2bd68e6e79f9\transformed\jetified-firebase-config-22.0.1\AndroidManifest.xml:34:17-82
	android:name
		ADDED from [com.google.firebase:firebase-config:22.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc2b22665e024b976aa2bd68e6e79f9\transformed\jetified-firebase-config-22.0.1\AndroidManifest.xml:33:17-117
meta-data#com.google.firebase.components:com.google.firebase.appcheck.playintegrity.FirebaseAppCheckPlayIntegrityRegistrar
ADDED from [com.google.firebase:firebase-appcheck-playintegrity:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430b373a0231a5ac5a64f84d80e35563\transformed\jetified-firebase-appcheck-playintegrity-18.0.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-appcheck-playintegrity:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430b373a0231a5ac5a64f84d80e35563\transformed\jetified-firebase-appcheck-playintegrity-18.0.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-appcheck-playintegrity:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430b373a0231a5ac5a64f84d80e35563\transformed\jetified-firebase-appcheck-playintegrity-18.0.0\AndroidManifest.xml:26:17-144
meta-data#com.google.firebase.components:com.google.firebase.crashlytics.ktx.FirebaseCrashlyticsKtxRegistrar
ADDED from [com.google.firebase:firebase-crashlytics-ktx:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2af3c9a2590e755876f4d3a775a327e7\transformed\jetified-firebase-crashlytics-ktx-19.2.1\AndroidManifest.xml:24:13-26:85
	android:value
		ADDED from [com.google.firebase:firebase-crashlytics-ktx:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2af3c9a2590e755876f4d3a775a327e7\transformed\jetified-firebase-crashlytics-ktx-19.2.1\AndroidManifest.xml:26:17-82
	android:name
		ADDED from [com.google.firebase:firebase-crashlytics-ktx:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2af3c9a2590e755876f4d3a775a327e7\transformed\jetified-firebase-crashlytics-ktx-19.2.1\AndroidManifest.xml:25:17-130
meta-data#com.google.firebase.components:com.google.firebase.database.ktx.FirebaseDatabaseLegacyRegistrar
ADDED from [com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07a2722339bfa0219c25a815d830727d\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07a2722339bfa0219c25a815d830727d\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07a2722339bfa0219c25a815d830727d\transformed\jetified-firebase-database-ktx-21.0.0\AndroidManifest.xml:13:17-127
meta-data#com.google.firebase.components:com.google.firebase.appcheck.ktx.FirebaseAppcheckLegacyRegistrar
ADDED from [com.google.firebase:firebase-appcheck-ktx:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8aed30e267a3f7957cac475003383c4c\transformed\jetified-firebase-appcheck-ktx-18.0.0\AndroidManifest.xml:24:13-26:85
	android:value
		ADDED from [com.google.firebase:firebase-appcheck-ktx:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8aed30e267a3f7957cac475003383c4c\transformed\jetified-firebase-appcheck-ktx-18.0.0\AndroidManifest.xml:26:17-82
	android:name
		ADDED from [com.google.firebase:firebase-appcheck-ktx:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8aed30e267a3f7957cac475003383c4c\transformed\jetified-firebase-appcheck-ktx-18.0.0\AndroidManifest.xml:25:17-127
meta-data#com.google.firebase.components:com.google.firebase.messaging.ktx.FirebaseMessagingLegacyRegistrar
ADDED from [com.google.firebase:firebase-messaging-ktx:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7d62d36630b8c2669020ef927747254\transformed\jetified-firebase-messaging-ktx-24.1.0\AndroidManifest.xml:26:13-28:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging-ktx:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7d62d36630b8c2669020ef927747254\transformed\jetified-firebase-messaging-ktx-24.1.0\AndroidManifest.xml:28:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging-ktx:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7d62d36630b8c2669020ef927747254\transformed\jetified-firebase-messaging-ktx-24.1.0\AndroidManifest.xml:27:17-129
meta-data#com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar
ADDED from [com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd549eab9cdb581d5cd730ab22aa1b6\transformed\jetified-firebase-crashlytics-19.2.1\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd549eab9cdb581d5cd730ab22aa1b6\transformed\jetified-firebase-crashlytics-19.2.1\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd549eab9cdb581d5cd730ab22aa1b6\transformed\jetified-firebase-crashlytics-19.2.1\AndroidManifest.xml:16:17-126
meta-data#com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar
ADDED from [com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd549eab9cdb581d5cd730ab22aa1b6\transformed\jetified-firebase-crashlytics-19.2.1\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd549eab9cdb581d5cd730ab22aa1b6\transformed\jetified-firebase-crashlytics-19.2.1\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd549eab9cdb581d5cd730ab22aa1b6\transformed\jetified-firebase-crashlytics-19.2.1\AndroidManifest.xml:19:17-115
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aedb6a59da514a3b9c3c71add6ffdec1\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aedb6a59da514a3b9c3c71add6ffdec1\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:30:13-78
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:33:13-35:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:34:17-81
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:34:25-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:47:13-82
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d905e2126ec44f969804dcdc35ede6c0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:61:17-119
service#com.google.firebase.sessions.SessionLifecycleService
ADDED from [com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34162c290514c368f494368d6102f2e8\transformed\jetified-firebase-sessions-2.0.6\AndroidManifest.xml:22:9-25:40
	android:enabled
		ADDED from [com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34162c290514c368f494368d6102f2e8\transformed\jetified-firebase-sessions-2.0.6\AndroidManifest.xml:24:13-35
	android:exported
		ADDED from [com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34162c290514c368f494368d6102f2e8\transformed\jetified-firebase-sessions-2.0.6\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34162c290514c368f494368d6102f2e8\transformed\jetified-firebase-sessions-2.0.6\AndroidManifest.xml:23:13-80
meta-data#com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar
ADDED from [com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34162c290514c368f494368d6102f2e8\transformed\jetified-firebase-sessions-2.0.6\AndroidManifest.xml:29:13-31:85
	android:value
		ADDED from [com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34162c290514c368f494368d6102f2e8\transformed\jetified-firebase-sessions-2.0.6\AndroidManifest.xml:31:17-82
	android:name
		ADDED from [com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34162c290514c368f494368d6102f2e8\transformed\jetified-firebase-sessions-2.0.6\AndroidManifest.xml:30:17-117
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ce1bda87d7244e8b25ef2bc59b4ecef\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ce1bda87d7244e8b25ef2bc59b4ecef\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ce1bda87d7244e8b25ef2bc59b4ecef\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ce1bda87d7244e8b25ef2bc59b4ecef\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ce1bda87d7244e8b25ef2bc59b4ecef\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ce1bda87d7244e8b25ef2bc59b4ecef\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar
ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82e68bdc17783eab343147c24d5a4c00\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
	android:value
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82e68bdc17783eab343147c24d5a4c00\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:31:17-82
	android:name
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82e68bdc17783eab343147c24d5a4c00\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:30:17-120
meta-data#com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar
ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82e68bdc17783eab343147c24d5a4c00\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
	android:value
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82e68bdc17783eab343147c24d5a4c00\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:34:17-82
	android:name
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82e68bdc17783eab343147c24d5a4c00\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:33:17-109
meta-data#com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar
ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e0e1853c5f0749217e440541350556b\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e0e1853c5f0749217e440541350556b\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e0e1853c5f0749217e440541350556b\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
meta-data#com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar
ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e0e1853c5f0749217e440541350556b\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
	android:value
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e0e1853c5f0749217e440541350556b\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
	android:name
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e0e1853c5f0749217e440541350556b\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25a61a6d44aa83fddee0cb806da6dac6\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25a61a6d44aa83fddee0cb806da6dac6\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25a61a6d44aa83fddee0cb806da6dac6\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\786d9723becac0f5ed23bdc06029c6d2\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\071658833db178a8621052ffa0849746\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd4dde74fe4031e7f9743f34c05041e2\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd4dde74fe4031e7f9743f34c05041e2\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1f0a5d4ad88ed29dfa4e8cb817057e6\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1f0a5d4ad88ed29dfa4e8cb817057e6\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\071658833db178a8621052ffa0849746\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\071658833db178a8621052ffa0849746\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\071658833db178a8621052ffa0849746\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\071658833db178a8621052ffa0849746\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\071658833db178a8621052ffa0849746\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\071658833db178a8621052ffa0849746\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\071658833db178a8621052ffa0849746\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
	android:value
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:46:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:53:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4933014c413ddc03301803655825b665\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd4dde74fe4031e7f9743f34c05041e2\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd4dde74fe4031e7f9743f34c05041e2\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd4dde74fe4031e7f9743f34c05041e2\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23c7b3148a83432ca440c27e9e880ef9\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23c7b3148a83432ca440c27e9e880ef9\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23c7b3148a83432ca440c27e9e880ef9\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23c7b3148a83432ca440c27e9e880ef9\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23c7b3148a83432ca440c27e9e880ef9\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23c7b3148a83432ca440c27e9e880ef9\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\903e092fc049e07eb9802b2b1d837d55\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\903e092fc049e07eb9802b2b1d837d55\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\903e092fc049e07eb9802b2b1d837d55\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64d1cc8f3e9c59c96e0297d996aad4c8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64d1cc8f3e9c59c96e0297d996aad4c8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64d1cc8f3e9c59c96e0297d996aad4c8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64d1cc8f3e9c59c96e0297d996aad4c8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
meta-data#com.google.firebase.components:com.google.firebase.abt.component.AbtRegistrar
ADDED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c9f1b0cfb3dce6ae7cf4b0f010a4436\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c9f1b0cfb3dce6ae7cf4b0f010a4436\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c9f1b0cfb3dce6ae7cf4b0f010a4436\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:13:17-109
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c96b0d8b72c615a79b6ffc473f6a8013\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c96b0d8b72c615a79b6ffc473f6a8013\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c96b0d8b72c615a79b6ffc473f6a8013\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d58a419118e34eadf0fdff30391c321\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d58a419118e34eadf0fdff30391c321\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d58a419118e34eadf0fdff30391c321\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.app.videofbdownloadfree.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d58a419118e34eadf0fdff30391c321\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d58a419118e34eadf0fdff30391c321\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d58a419118e34eadf0fdff30391c321\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d58a419118e34eadf0fdff30391c321\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d58a419118e34eadf0fdff30391c321\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.app.videofbdownloadfree.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d58a419118e34eadf0fdff30391c321\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d58a419118e34eadf0fdff30391c321\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf01dc2d83636955506f1575030bb524\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf01dc2d83636955506f1575030bb524\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf01dc2d83636955506f1575030bb524\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85713e3337aa4c8e7156fef507f40140\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdc16e1b8883ecf192e9948d1c13071a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:34:9-36:40
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdc16e1b8883ecf192e9948d1c13071a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:34:9-36:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85713e3337aa4c8e7156fef507f40140\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85713e3337aa4c8e7156fef507f40140\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:27:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85713e3337aa4c8e7156fef507f40140\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:29:13-31:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85713e3337aa4c8e7156fef507f40140\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:31:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85713e3337aa4c8e7156fef507f40140\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:30:17-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62d58cdaaba81e57a544968f24bc0be4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdc16e1b8883ecf192e9948d1c13071a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:24:9-28:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdc16e1b8883ecf192e9948d1c13071a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:26:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdc16e1b8883ecf192e9948d1c13071a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:27:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdc16e1b8883ecf192e9948d1c13071a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:25:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdc16e1b8883ecf192e9948d1c13071a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:30:9-32:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdc16e1b8883ecf192e9948d1c13071a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdc16e1b8883ecf192e9948d1c13071a\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:31:13-132
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23645d451edec15b499b2c0a1186f68f\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
	android:exported
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23645d451edec15b499b2c0a1186f68f\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23645d451edec15b499b2c0a1186f68f\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23645d451edec15b499b2c0a1186f68f\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
