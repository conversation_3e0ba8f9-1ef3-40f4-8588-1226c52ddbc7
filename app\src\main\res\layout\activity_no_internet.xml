<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="48dp"
    android:background="#E5000000">

    <!-- No Internet Image -->
    <ImageView
        android:layout_width="250dp"
        android:layout_height="250dp"
        android:src="@drawable/no_internet_image"
        android:layout_marginBottom="32dp"
        android:scaleType="centerInside" />

    <!-- No Internet Text -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/no_internet"
        android:textSize="28sp"
        android:textStyle="bold"
        android:textColor="#6C757D"
        android:layout_marginBottom="16dp" />

    <!-- Description Text -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/no_internet_description"
        android:textSize="16sp"
        android:textColor="#ADB5BD"
        android:layout_marginBottom="48dp"
        android:gravity="center" />

    <!-- Retry Button -->
    <Button
        android:id="@+id/btnRetry"
        android:layout_width="200dp"
        android:layout_height="56dp"
        android:text="@string/btn_no_internet"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@android:color/white"
        android:background="@drawable/rounded_button" />

</LinearLayout>
