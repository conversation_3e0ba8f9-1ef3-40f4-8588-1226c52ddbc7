<?xml version="1.0" encoding="utf-8"?>
<com.google.android.gms.ads.nativead.NativeAdView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        android:background="@android:color/white"
        android:elevation="4dp">

        <!-- Header with headline -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="12dp">

            <ImageView
                android:id="@+id/ad_app_icon"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginEnd="12dp"
                android:scaleType="centerCrop" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/ad_headline"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@android:color/black"
                    android:maxLines="2"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/ad_advertiser"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textSize="12sp"
                    android:textColor="@android:color/darker_gray"
                    android:maxLines="1"
                    android:ellipsize="end" />

            </LinearLayout>

            <!-- Ad badge -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Ad"
                android:textSize="10sp"
                android:textColor="@android:color/white"
                android:background="#FF9800"
                android:paddingHorizontal="6dp"
                android:paddingVertical="2dp" />

        </LinearLayout>

        <!-- Media content -->
        <com.google.android.gms.ads.nativead.MediaView
            android:id="@+id/ad_media"
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:layout_marginBottom="12dp" />

        <!-- Body text -->
        <TextView
            android:id="@+id/ad_body"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:textColor="@android:color/black"
            android:maxLines="3"
            android:ellipsize="end"
            android:layout_marginBottom="12dp" />

        <!-- Call to Action -->
        <Button
            android:id="@+id/ad_call_to_action"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:textAllCaps="false"
            android:background="@android:color/holo_blue_dark"
            android:textColor="@android:color/white"
            android:padding="12dp" />

    </LinearLayout>

</com.google.android.gms.ads.nativead.NativeAdView>
