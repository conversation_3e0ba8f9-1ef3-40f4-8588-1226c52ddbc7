package com.app.videofbdownloadfree

import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.google.android.material.card.MaterialCardView

class SettingsActivity : BaseActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContentView(R.layout.settings_preferences)



        // Cards
        findViewById<MaterialCardView>(R.id.cardAbout).setOnClickListener {
            openUrl(getString(R.string.about_url))
        }

        findViewById<MaterialCardView>(R.id.cardPrivacy).setOnClickListener {
            openUrl(getString(R.string.privacy_url))
        }

        findViewById<MaterialCardView>(R.id.contactUs).setOnClickListener {
            sendSupportEmail()
        }

        findViewById<MaterialCardView>(R.id.rateApp).setOnClickListener {
            rateApp()
        }
        findViewById<MaterialCardView>(R.id.otherApps).setOnClickListener{
            otherApps(this)
        }
        findViewById<MaterialCardView>(R.id.storagePermissionCard).setOnClickListener{
            checkSelfPermission()
        }
        checkSelfPermissionIcon()
        setupBottomNavigation()
    }

    private fun openUrl(url: String) {
        val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
        startActivity(intent)
    }
    private fun otherApps(context: Context) {
        val uri = Uri.parse("market://dev?id=7947875223539467993")
        val fallbackUri = Uri.parse("https://play.google.com/store/apps/developer?id=7947875223539467993")
        val intent = Intent(Intent.ACTION_VIEW, uri)
        intent.setPackage("com.android.vending")

        try {
            context.startActivity(intent)
        } catch (e: ActivityNotFoundException) {

            val fallbackIntent = Intent(Intent.ACTION_VIEW, fallbackUri)
            context.startActivity(fallbackIntent)
        }
         }

        private fun checkSelfPermissionIcon(){
            val permissionsStatus = PermissionsActivity.arePermissionsGranted(this)
            val permissionIcon = findViewById<ImageView>(R.id.storageStatusIcon)
        if(!permissionsStatus) permissionIcon.setImageResource(R.drawable.minus)
        else permissionIcon.setImageResource(R.drawable.check)
    }
    private fun checkSelfPermission() {
        val permissionsStatus = PermissionsActivity.arePermissionsGranted(this)
        if (!permissionsStatus) {
            startActivity(Intent(this, PermissionsActivity::class.java))
        } else {
            Toast.makeText(this, "Permissions Granted!", Toast.LENGTH_SHORT).show()
        }
    }
    private fun sendSupportEmail() {
        val email = getString(R.string.mail_contact)
        val intent = Intent(Intent.ACTION_SENDTO).apply {
            data = Uri.parse("mailto:$email")
            putExtra(Intent.EXTRA_SUBJECT, "App Support")
        }
        startActivity(intent)
    }

    private fun rateApp() {
        RatingDialog(this).show()
    }
    private fun setupBottomNavigation() {
        val navHome = findViewById<LinearLayout>(R.id.navHome)
        val navDownloads = findViewById<LinearLayout>(R.id.navDownloads)
        val navSettings = findViewById<LinearLayout>(R.id.navSettings)

        // Set Home as selected initially
        setNavItemSelected(navSettings, true)

        navHome.setOnClickListener {
            setNavItemSelected(navHome, true)
            setNavItemSelected(navDownloads, false)
            setNavItemSelected(navSettings, false)
            // Already on home, scroll to top
            AnalyticsManager.logButtonClick("recent_downloads", "MainActivity")
            AnalyticsManager.logFeatureUsage("recent_downloads")

            // Force preload ads for next use
            BackgroundAdService.forcePreload(this)
            startActivity(Intent(this, MainActivity::class.java))


        }

        navDownloads.setOnClickListener {
            setNavItemSelected(navHome, false)
            setNavItemSelected(navDownloads, true)
            setNavItemSelected(navSettings, false)
            startActivity(Intent(this, RecentDownloadsActivity::class.java))

        }

        navSettings.setOnClickListener {
            setNavItemSelected(navHome, false)
            setNavItemSelected(navDownloads, false)
            setNavItemSelected(navSettings, true)

            AnalyticsManager.logButtonClick("settings", "Settings")
            AnalyticsManager.logFeatureUsage("settings")

            // Force preload ads for next use
            BackgroundAdService.forcePreload(this)

        }
    }
    private fun setNavItemSelected(navItem: LinearLayout, isSelected: Boolean) {
        val imageView = navItem.getChildAt(0) as ImageView
        val textView = navItem.getChildAt(1) as TextView

        if (isSelected) {
            imageView.setColorFilter(getColor(R.color.insta_purple))
            textView.setTextColor(getColor(R.color.insta_purple))
            textView.setTypeface(null, android.graphics.Typeface.BOLD)
        } else {
            imageView.setColorFilter(getColor(R.color.gray))
            textView.setTextColor(getColor(R.color.gray))
            textView.setTypeface(null, android.graphics.Typeface.NORMAL)
        }
    }
}
