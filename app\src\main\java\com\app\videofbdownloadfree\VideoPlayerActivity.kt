package com.app.videofbdownloadfree

import android.content.Intent
import android.graphics.SurfaceTexture
import android.media.MediaPlayer
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.Surface
import android.view.TextureView
import android.view.View
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import com.google.android.gms.ads.AdView
import com.google.android.material.card.MaterialCardView
import java.io.File

class VideoPlayerActivity : BaseActivity() {

    private lateinit var textureView: TextureView
    private lateinit var imageView: ImageView
    private lateinit var btnPlayPause: ImageButton
    private lateinit var btnPrevious: ImageButton
    private lateinit var btnNext: ImageButton
    private lateinit var btnClose: ImageButton
    private lateinit var fileInfoText: TextView
    private lateinit var bannerAdView: AdView
    private lateinit var bannerAdContainer: MaterialCardView

    private var mediaPlayer: MediaPlayer? = null
    private var surface: Surface? = null
    private var isPlaying = false

    private var allFiles: List<File> = emptyList()
    private var currentFileIndex = 0
    private var currentFile: File? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.fullscreen_preview)

        initializeViews()
        setupAdBanner()
        handleIntent()
        setupClickListeners()
    }

    private fun initializeViews() {
        textureView = findViewById(R.id.fullscreenVideo)
        imageView = findViewById(R.id.fullscreenImage)
        btnPlayPause = findViewById(R.id.btnPlayPause)
        btnPrevious = findViewById(R.id.btnPrevious)
        btnNext = findViewById(R.id.btnNext)
        btnClose = findViewById(R.id.btnCloseFullscreen)
        fileInfoText = findViewById(R.id.fileInfoText)
        bannerAdView = findViewById(R.id.bannerAdView)
        bannerAdContainer = findViewById(R.id.bannerAdContainer)
    }

    private fun setupAdBanner() {
        if (AdConfig.adsEnabled) {
            loadBannerAd()
        } else {
            bannerAdContainer.visibility = View.GONE
        }
    }

    private fun loadBannerAd() {
        try {
            // Set the correct ad unit ID for video player
            bannerAdView.adUnitId = AdConfig.bannerPlayerAdUnitId
            Log.d("VideoPlayerActivity", "Loading banner ad with unit ID: ${AdConfig.bannerPlayerAdUnitId}")

            // Configure AdRequest for Facebook Bidding
            val adRequestBuilder = com.google.android.gms.ads.AdRequest.Builder()
            val adRequest = FacebookBiddingHelper.configureAdRequestForBidding(adRequestBuilder).build()
            bannerAdView.loadAd(adRequest)
            bannerAdView.adListener = object : com.google.android.gms.ads.AdListener() {
                override fun onAdLoaded() {
                    bannerAdContainer.visibility = View.VISIBLE
                }
                override fun onAdFailedToLoad(error: com.google.android.gms.ads.LoadAdError) {
                    bannerAdContainer.visibility = View.GONE
                }
            }
        } catch (e: Exception) {
            Log.e("VideoPlayerActivity", "Error loading banner ad: ${e.message}")
            bannerAdContainer.visibility = View.GONE
        }
    }

    private fun handleIntent() {
        val videoPath = intent.getStringExtra("video_path")
        val filePath = intent.getStringExtra("file_path")

        val targetPath = videoPath ?: filePath

        if (!targetPath.isNullOrEmpty()) {
            val file = File(targetPath)
            if (file.exists()) {
                currentFile = file
                loadAllFilesFromDirectory(file)
                loadCurrentFile(file)
            } else {
                Toast.makeText(this, "File not found", Toast.LENGTH_SHORT).show()
                finish()
            }
        } else {
            Toast.makeText(this, "No file path provided", Toast.LENGTH_SHORT).show()
            finish()
        }
    }

    private fun loadAllFilesFromDirectory(currentFile: File) {
        val parentDir = currentFile.parentFile
        if (parentDir != null && parentDir.exists()) {
            allFiles = parentDir.listFiles { file ->
                file.isFile && isMediaFile(file)
            }?.sortedByDescending { it.lastModified() } ?: emptyList()

            currentFileIndex = allFiles.indexOf(currentFile).takeIf { it >= 0 } ?: 0
        } else {
            allFiles = listOf(currentFile)
            currentFileIndex = 0
        }
        updateFileInfo()
    }

    private fun isMediaFile(file: File): Boolean {
        val extension = file.extension.lowercase()
        return extension in listOf("mp4", "mov", "avi", "mkv", "jpg", "jpeg", "png", "webp", "gif")
    }

    private fun updateFileInfo() {
        if (allFiles.isNotEmpty()) {
            fileInfoText.text = "${currentFile?.name} (${currentFileIndex + 1}/${allFiles.size})"
        } else {
            fileInfoText.text = currentFile?.name ?: "Unknown file"
        }
    }

    private fun setupClickListeners() {
        btnClose.setOnClickListener {
            releaseMediaPlayer()
            finish()
        }

        btnPlayPause.setOnClickListener {
            if (isPlaying) {
                pauseVideo()
            } else {
                resumeVideo()
            }
        }

        btnPrevious.setOnClickListener {
            navigateToPrevious()
        }

        btnNext.setOnClickListener {
            navigateToNext()
        }

        // Click on video/image to toggle play/pause
        textureView.setOnClickListener {
            if (btnPlayPause.visibility == View.VISIBLE) {
                btnPlayPause.performClick()
            }
        }

        imageView.setOnClickListener {
            btnNext.performClick()
        }
    }

    private fun loadCurrentFile(file: File) {
        currentFile = file
        updateFileInfo()

        when (file.extension.lowercase()) {
            "jpg", "jpeg", "png", "webp", "gif" -> {
                loadImage(file)
            }
            "mp4", "mov", "avi", "mkv" -> {
                loadVideo(file)
            }
            else -> {
                Toast.makeText(this, "Unsupported file format", Toast.LENGTH_SHORT).show()
                finish()
            }
        }
    }

    private fun loadImage(file: File) {
        releaseMediaPlayer()

        btnPlayPause.visibility = View.GONE
        imageView.visibility = View.VISIBLE
        textureView.visibility = View.GONE

        imageView.setImageURI(Uri.fromFile(file))
        imageView.scaleType = ImageView.ScaleType.FIT_CENTER
    }

    private fun loadVideo(file: File) {
        releaseMediaPlayer()

        btnPlayPause.visibility = View.VISIBLE
        imageView.visibility = View.GONE
        textureView.visibility = View.VISIBLE

        btnPlayPause.setImageResource(android.R.drawable.ic_media_play)
        isPlaying = false

        setupVideoPlayer(file)
    }

    private fun setupVideoPlayer(file: File) {
        mediaPlayer = MediaPlayer()

        textureView.surfaceTextureListener = object : TextureView.SurfaceTextureListener {
            override fun onSurfaceTextureAvailable(surfaceTexture: SurfaceTexture, width: Int, height: Int) {
                surface?.release()
                surface = Surface(surfaceTexture)
                mediaPlayer?.setSurface(surface)

                try {
                    mediaPlayer?.setDataSource(file.absolutePath)
                    mediaPlayer?.prepareAsync()
                    mediaPlayer?.setOnPreparedListener {
                        mediaPlayer?.setVideoScalingMode(MediaPlayer.VIDEO_SCALING_MODE_SCALE_TO_FIT_WITH_CROPPING)
                        mediaPlayer?.start()
                        isPlaying = true
                        btnPlayPause.setImageResource(android.R.drawable.ic_media_pause)
                    }
                    mediaPlayer?.setOnCompletionListener {
                        isPlaying = false
                        btnPlayPause.setImageResource(android.R.drawable.ic_media_play)
                    }
                    mediaPlayer?.setOnErrorListener { _, what, extra ->
                        Log.e("VideoPlayerActivity", "MediaPlayer error: what=$what, extra=$extra")
                        Toast.makeText(this@VideoPlayerActivity, "Error playing video", Toast.LENGTH_SHORT).show()
                        false
                    }
                } catch (e: Exception) {
                    Log.e("VideoPlayerActivity", "Error setting up video: ${e.message}")
                    Toast.makeText(this@VideoPlayerActivity, "Error loading video", Toast.LENGTH_SHORT).show()
                }
            }

            override fun onSurfaceTextureSizeChanged(surfaceTexture: SurfaceTexture, width: Int, height: Int) {}

            override fun onSurfaceTextureDestroyed(surfaceTexture: SurfaceTexture): Boolean {
                releaseMediaPlayer()
                return true
            }

            override fun onSurfaceTextureUpdated(surfaceTexture: SurfaceTexture) {}
        }
    }

    private fun pauseVideo() {
        mediaPlayer?.pause()
        isPlaying = false
        btnPlayPause.setImageResource(android.R.drawable.ic_media_play)
    }

    private fun resumeVideo() {
        mediaPlayer?.start()
        isPlaying = true
        btnPlayPause.setImageResource(android.R.drawable.ic_media_pause)
    }

    private fun navigateToPrevious() {
        if (allFiles.isNotEmpty() && currentFileIndex > 0) {
            currentFileIndex--
            loadCurrentFile(allFiles[currentFileIndex])
        }
    }

    private fun navigateToNext() {
        if (allFiles.isNotEmpty() && currentFileIndex < allFiles.size - 1) {
            currentFileIndex++
            loadCurrentFile(allFiles[currentFileIndex])
        }
    }

    private fun releaseMediaPlayer() {
        try {
            mediaPlayer?.stop()
            mediaPlayer?.release()
            mediaPlayer = null
            surface?.release()
            surface = null
            isPlaying = false
        } catch (e: Exception) {
            Log.e("VideoPlayerActivity", "Error releasing media player: ${e.message}")
        }
    }

    override fun onPause() {
        super.onPause()
        if (isPlaying) {
            pauseVideo()
        }
        bannerAdView.pause()
    }

    override fun onResume() {
        super.onResume()
        bannerAdView.resume()
    }

    override fun onDestroy() {
        super.onDestroy()
        releaseMediaPlayer()
        bannerAdView.destroy()
    }

    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        releaseMediaPlayer()
        super.onBackPressed()
    }
}
