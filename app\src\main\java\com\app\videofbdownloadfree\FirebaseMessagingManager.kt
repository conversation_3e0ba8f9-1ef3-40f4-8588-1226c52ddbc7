package com.app.videofbdownloadfree

import android.content.Context
import android.os.Build
import android.util.Log
import com.google.firebase.messaging.FirebaseMessaging

/**
 * Manager for Firebase Cloud Messaging operations
 * Handles token management and topic subscriptions
 */
object FirebaseMessagingManager {
    private const val TAG = "FCMManager"

    /**
     * Initialize Firebase Cloud Messaging
     */
    fun initialize(context: Context) {
        try {
            Log.d(TAG, "Initializing Firebase Cloud Messaging...")
            
            // Get FCM token
            getToken { token ->
                if (token != null) {
                    Log.d(TAG, "FCM token retrieved: $token")
                    saveToken(context, token)
                } else {
                    Log.w(TAG, "Failed to retrieve FCM token")
                }
            }
            
            // Subscribe to default topics
            subscribeToDefaultTopics()
            
            Log.d(TAG, "Firebase Cloud Messaging initialized successfully")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing Firebase Cloud Messaging: ${e.message}")
            CrashlyticsManager.logException(e, "FCM initialization failed")
        }
    }

    /**
     * Get FCM registration token
     */
    fun getToken(callback: (String?) -> Unit) {
        try {
            FirebaseMessaging.getInstance().token
                .addOnCompleteListener { task ->
                    if (!task.isSuccessful) {
                        Log.w(TAG, "Fetching FCM registration token failed", task.exception)
                        callback(null)
                        return@addOnCompleteListener
                    }

                    // Get new FCM registration token
                    val token = task.result
                    Log.d(TAG, "FCM registration token: $token")
                    callback(token)
                }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting FCM token: ${e.message}")
            callback(null)
        }
    }

    /**
     * Subscribe to a topic
     */
    fun subscribeToTopic(topic: String, callback: ((Boolean) -> Unit)? = null) {
        try {
            FirebaseMessaging.getInstance().subscribeToTopic(topic)
                .addOnCompleteListener { task ->
                    val success = task.isSuccessful
                    if (success) {
                        Log.d(TAG, "Subscribed to topic: $topic")
                    } else {
                        Log.w(TAG, "Failed to subscribe to topic: $topic", task.exception)
                    }
                    
                    // Log analytics
                    AnalyticsManager.logEvent("fcm_topic_subscription", android.os.Bundle().apply {
                        putString("topic", topic)
                        putString("success", success.toString())
                    })
                    
                    callback?.invoke(success)
                }
        } catch (e: Exception) {
            Log.e(TAG, "Error subscribing to topic $topic: ${e.message}")
            callback?.invoke(false)
        }
    }

    /**
     * Unsubscribe from a topic
     */
    fun unsubscribeFromTopic(topic: String, callback: ((Boolean) -> Unit)? = null) {
        try {
            FirebaseMessaging.getInstance().unsubscribeFromTopic(topic)
                .addOnCompleteListener { task ->
                    val success = task.isSuccessful
                    if (success) {
                        Log.d(TAG, "Unsubscribed from topic: $topic")
                    } else {
                        Log.w(TAG, "Failed to unsubscribe from topic: $topic", task.exception)
                    }
                    
                    // Log analytics
                    AnalyticsManager.logEvent("fcm_topic_unsubscription", android.os.Bundle().apply {
                        putString("topic", topic)
                        putString("success", success.toString())
                    })
                    
                    callback?.invoke(success)
                }
        } catch (e: Exception) {
            Log.e(TAG, "Error unsubscribing from topic $topic: ${e.message}")
            callback?.invoke(false)
        }
    }

    /**
     * Subscribe to default topics
     */
    private fun subscribeToDefaultTopics() {
        val defaultTopics = listOf(
            "all_users",           // General announcements
            "app_updates",         // App update notifications
            "feature_announcements", // New feature announcements
            "maintenance"          // Maintenance notifications
        )

        defaultTopics.forEach { topic ->
            subscribeToTopic(topic) { success ->
                if (success) {
                    Log.d(TAG, "Successfully subscribed to default topic: $topic")
                } else {
                    Log.w(TAG, "Failed to subscribe to default topic: $topic")
                }
            }
        }
    }

    /**
     * Save FCM token locally
     */
    private fun saveToken(context: Context, token: String) {
        try {
            val sharedPrefs = context.getSharedPreferences("fcm_prefs", Context.MODE_PRIVATE)
            val previousToken = sharedPrefs.getString("fcm_token", null)
            
            if (previousToken != token) {
                sharedPrefs.edit()
                    .putString("fcm_token", token)
                    .putLong("token_timestamp", System.currentTimeMillis())
                    .apply()
                    
                Log.d(TAG, "New FCM token saved locally")
                
                // Log analytics for token change
                AnalyticsManager.logEvent("fcm_token_updated", android.os.Bundle().apply {
                    putString("token_changed", (previousToken != null).toString())
                })
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error saving FCM token: ${e.message}")
        }
    }

    /**
     * Get saved FCM token
     */
    fun getSavedToken(context: Context): String? {
        return try {
            val sharedPrefs = context.getSharedPreferences("fcm_prefs", Context.MODE_PRIVATE)
            sharedPrefs.getString("fcm_token", null)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting saved FCM token: ${e.message}")
            null
        }
    }

    /**
     * Check if notifications are enabled
     */
    fun areNotificationsEnabled(context: Context): Boolean {
        return try {
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as android.app.NotificationManager
            notificationManager.areNotificationsEnabled()
        } catch (e: Exception) {
            Log.e(TAG, "Error checking notification status: ${e.message}")
            false
        }
    }

    /**
     * Get FCM status information
     */
    fun getFCMStatus(context: Context): String {
        return try {
            val status = StringBuilder()
            status.append("Firebase Cloud Messaging Status:\n")
            status.append("- Notifications Enabled: ${areNotificationsEnabled(context)}\n")
            status.append("- Saved Token: ${getSavedToken(context)?.take(20)}...\n")
            status.append("- Default Topics: all_users, app_updates, feature_announcements, maintenance\n")
            status.toString()
        } catch (e: Exception) {
            "Error getting FCM status: ${e.message}"
        }
    }

    /**
     * Handle FCM errors
     */
    fun handleFCMError(error: String, operation: String) {
        try {
            Log.e(TAG, "FCM error for $operation: $error")
            
            // Log to crashlytics
            CrashlyticsManager.logException(
                Exception("FCM error: $error"), 
                "FCM failed for $operation"
            )
            
            // Log to analytics
            AnalyticsManager.logError("fcm_error", error, "FirebaseMessagingManager")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error handling FCM error: ${e.message}")
        }
    }

    /**
     * Send a test notification locally
     */
    fun sendTestNotification(context: Context) {
        try {
            Log.d(TAG, "Sending test notification...")

            val intent = android.content.Intent(context, SaveItFirebaseMessagingService::class.java)
            val service = SaveItFirebaseMessagingService()
            service.showTestNotification()

            Log.d(TAG, "Test notification sent")

        } catch (e: Exception) {
            Log.e(TAG, "Error sending test notification: ${e.message}")
        }
    }

    /**
     * Show notification directly (for testing)
     */
    fun showDirectNotification(context: Context, title: String, body: String) {
        try {
            Log.d(TAG, "Showing direct notification: $title")

            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as android.app.NotificationManager

            // Create notification channel if needed
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val channel = android.app.NotificationChannel(
                    "saveit_notifications",
                    "SaveIt Notifications",
                    android.app.NotificationManager.IMPORTANCE_HIGH
                )
                notificationManager.createNotificationChannel(channel)
            }

            val intent = android.content.Intent(context, MainActivity::class.java)
            val pendingIntent = android.app.PendingIntent.getActivity(
                context, 0, intent, android.app.PendingIntent.FLAG_IMMUTABLE
            )

            val notification = androidx.core.app.NotificationCompat.Builder(context, "saveit_notifications")
                .setSmallIcon(android.R.drawable.ic_dialog_info)
                .setContentTitle(title)
                .setContentText(body)
                .setPriority(androidx.core.app.NotificationCompat.PRIORITY_HIGH)
                .setContentIntent(pendingIntent)
                .setAutoCancel(true)
                .build()

            notificationManager.notify(System.currentTimeMillis().toInt(), notification)
            Log.d(TAG, "Direct notification shown successfully")

        } catch (e: Exception) {
            Log.e(TAG, "Error showing direct notification: ${e.message}")
        }
    }
}
