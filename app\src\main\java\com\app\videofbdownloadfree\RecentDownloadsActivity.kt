package com.app.videofbdownloadfree

import android.content.Context
import android.content.Intent
import android.graphics.SurfaceTexture
import android.media.AudioManager
import android.media.MediaPlayer
import android.net.Uri
import android.os.Bundle

import android.util.Log
import android.view.Surface
import android.view.TextureView
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.VideoView
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import kotlinx.coroutines.*
import java.io.File
import java.io.IOException

class RecentDownloadsActivity : BaseActivity() {

    private lateinit var downloadsRecyclerView: RecyclerView
    private lateinit var emptyTextView: TextView
    private lateinit var btnPosts: Button
    private lateinit var btnReels: Button
    private lateinit var scrollView: androidx.core.widget.NestedScrollView
    private val baseDir by lazy { File(getExternalFilesDir(null),"SaveIt") }
    private var currentFilter = "posts"
    private var currentAdapter: MediaAdapterWithBannerAds? = null
    private var loadingJob: Job? = null
    private lateinit var interstitialAdManager: InterstitialAdManager
    private lateinit var adRefreshManager: AdRefreshManager
    private var mediaPlayer: MediaPlayer? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_recent_downloads)

        // Log screen view
        AnalyticsManager.logScreenView("Recent Downloads", "RecentDownloadsActivity")

        downloadsRecyclerView = findViewById(R.id.downloadsRecyclerView)
        emptyTextView = findViewById(R.id.emptyTextView)
        btnPosts = findViewById(R.id.btnPosts)
        btnReels = findViewById(R.id.btnReels)

        // Initialize InterstitialAdManager
        interstitialAdManager = InterstitialAdManager(this)
        interstitialAdManager.preloadAd()

        // Initialize AdRefreshManager
        adRefreshManager = AdRefreshManager(this)
        adRefreshManager.initialize(
            interstitialManager = interstitialAdManager,
            bannerManager = null, // No banner ads in this activity - ads are handled by adapter
            appOpenManager = null
        )

        // Add lifecycle observer and start auto refresh
        lifecycle.addObserver(adRefreshManager)
        adRefreshManager.startAutoRefresh()


        // Setup GridLayoutManager with 3 columns
        setupRecyclerView()

        btnPosts.setOnClickListener {
            currentFilter = "posts"
            btnPosts.isSelected = true
            btnReels.isSelected = false
            updateTabButtons()
            loadAndDisplayDownloads()
        }

        btnReels.setOnClickListener {
            currentFilter = "reels"
            btnPosts.isSelected = false
            btnReels.isSelected = true
            updateTabButtons()
            loadAndDisplayDownloads()
        }

        updateTabButtons()
        setupRecyclerView()
        loadAndDisplayDownloads()
        setupBottomNavigation()
    }

    private fun updateTabButtons() {
        // Use selected state for automatic styling via selectors
        if (currentFilter == "posts") {
            btnPosts.isSelected = true
            btnReels.isSelected = false
        } else {
            btnPosts.isSelected = false
            btnReels.isSelected = true
        }
    }

    private fun setupRecyclerView() {
        // Use LinearLayoutManager for single column layout (one item per row)
        val linearLayoutManager = androidx.recyclerview.widget.LinearLayoutManager(this)
        downloadsRecyclerView.layoutManager = linearLayoutManager

        // Note: setHasFixedSize(true) removed to avoid lint error with wrap_content in NestedScrollView
        // This is the correct approach for RecyclerView inside NestedScrollView with wrap_content

        Log.d("RecentDownloadsActivity", "RecyclerView setup with LinearLayoutManager - one item per row")
    }

    private fun loadAndDisplayDownloads() {
        // Cancel previous loading job
        loadingJob?.cancel()

        // Show loading state
        findViewById<LinearLayout>(R.id.emptyStateLayout).visibility = View.GONE
        downloadsRecyclerView.visibility = View.VISIBLE

        android.util.Log.d("RecentDownloadsActivity", "=== Loading downloads for filter: $currentFilter ===")

        loadingJob = CoroutineScope(Dispatchers.Main).launch {
            try {
                val mediaFiles = withContext(Dispatchers.IO) {
                    loadMediaFilesInBackground()
                }

                displayMediaFiles(mediaFiles)

            } catch (e: CancellationException) {
                android.util.Log.d("RecentDownloadsActivity", "Loading cancelled")
            } catch (e: Exception) {
                android.util.Log.e("RecentDownloadsActivity", "Error loading files: ${e.message}")
                showEmptyState("Error load file downloads")
            }
        }
    }

    private suspend fun loadMediaFilesInBackground(): List<File> {
        android.util.Log.d("RecentDownloadsActivity", "Base directory: ${baseDir.absolutePath}")
        android.util.Log.d("RecentDownloadsActivity", "Base directory exists: ${baseDir.exists()}")

        val allFiles = mutableListOf<File>()

        try {
            // Check Videos and Images folders specifically
            val videosDir = File(baseDir, "Videos")
            val imagesDir = File(baseDir, "Images")

            listOf(videosDir, imagesDir).forEach { dir ->
                if (dir.exists()) {
                    android.util.Log.d("RecentDownloadsActivity", "Scanning directory: ${dir.name}")
                    dir.listFiles()?.forEach { file ->
                        if (file.isFile && isValidMediaFile(file)) {
                            allFiles.add(file)
                            android.util.Log.d("RecentDownloadsActivity", "Found file: ${file.name}")
                        }
                    }
                }
            }

            // Also check base directory for any remaining files
            baseDir.listFiles()?.forEach { file ->
                if (file.isFile && isValidMediaFile(file)) {
                    allFiles.add(file)
                    android.util.Log.d("RecentDownloadsActivity", "Found base file: ${file.name}")
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("RecentDownloadsActivity", "Error scanning directories: ${e.message}")
        }

        android.util.Log.d("RecentDownloadsActivity", "Total files found: ${allFiles.size}")

        // Filter files based on current filter
        val filteredFiles = allFiles.filter { file ->
            isFileMatchingFilter(file, currentFilter)
        }

        android.util.Log.d("RecentDownloadsActivity", "Filtered files for '$currentFilter': ${filteredFiles.size}")

        // Sort by last modified time (newest first)
        val sortedFiles = filteredFiles.sortedByDescending { it.lastModified() }

        // Log the first few files to verify sorting
        sortedFiles.take(3).forEachIndexed { index, file ->
            val date = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault())
                .format(java.util.Date(file.lastModified()))
            android.util.Log.d("RecentDownloadsActivity", "File ${index + 1}: ${file.name} - Modified: $date")
        }

        return sortedFiles
    }

    private fun displayMediaFiles(mediaFiles: List<File>) {
        if (mediaFiles.isEmpty()) {
            showEmptyState()
            return
        }

        // Clean up previous adapter
        currentAdapter?.destroy()

        // Use MediaAdapterWithBannerAds
        val adapter = MediaAdapterWithBannerAds(this, mediaFiles) { file ->
            openFileInVideoPlayer(file)
        }
        currentAdapter = adapter
        downloadsRecyclerView.adapter = adapter

        // Update AdRefreshManager with new adapter
        adRefreshManager.initialize(
            interstitialManager = interstitialAdManager,
            bannerManager = null, // No banner ads in this activity - ads are handled by adapter
            appOpenManager = null,
            mediaAdapterWithBanners = adapter
        )

        android.util.Log.d("RecentDownloadsActivity", "Native ads adapter created with ${mediaFiles.size} files")
    }

    /**
     * Check if file matches current filter
     */
    private fun isFileMatchingFilter(file: File, filter: String): Boolean {
        if (!file.isFile) return false

        val extension = file.extension.lowercase()

        return when (filter) {
            "posts" -> {
                // Posts are typically images, check if file is in Images folder or has image extension
                (file.parent?.endsWith("Images") == true || extension in listOf("jpg", "jpeg", "png", "webp", "gif")) &&
                extension in listOf("jpg", "jpeg", "png", "webp", "gif")
            }
            "reels" -> {
                // Reels are videos, check if file is in Videos folder or has video extension
                (file.parent?.endsWith("Videos") == true || extension in listOf("mp4", "mov", "avi", "mkv")) &&
                extension in listOf("mp4", "mov", "avi", "mkv")
            }
            else -> {
                // Show all media files
                extension in listOf("jpg", "jpeg", "png", "webp", "gif", "mp4", "mov", "avi", "mkv")
            }
        }
    }

    /**
     * Get file type description for logging/debugging
     */
    private fun getFileTypeDescription(filter: String): String {
        return when (filter) {
            "posts" -> "images (from Images folder)"
            "reels" -> "videos (from Videos folder)"
            else -> "media files (from Videos and Images folders)"
        }
    }

    private fun getSavedDownloadFolders(): List<String> {
        val prefs = getSharedPreferences("downloads", MODE_PRIVATE)
        val json = prefs.getString("download_list_ordered", "[]") ?: "[]"
        return try {
            val jsonArray = org.json.JSONArray(json)
            val list = mutableListOf<String>()
            for (i in 0 until jsonArray.length()) {
                list.add(jsonArray.getString(i))
            }
            // Sort by timestamp in folder name (newest first)
            list.sortedByDescending { folderName ->
                // Extract timestamp from folder name if it exists
                val timestampMatch = Regex("_(\\d{8}_\\d{6})$").find(folderName)
                timestampMatch?.groupValues?.get(1) ?: "00000000_000000"
            }
        } catch (e: Exception) {
            e.printStackTrace()
            emptyList()
        }
    }

    private fun openFileInVideoPlayer(file: File) {
        val intent = Intent(this, VideoPlayerActivity::class.java).apply {
            putExtra("file_path", file.absolutePath)
        }
        startActivity(intent)
    }
    private fun setupBottomNavigation() {
        val navHome = findViewById<LinearLayout>(R.id.navHome)
        val navDownloads = findViewById<LinearLayout>(R.id.navDownloads)
        val navSettings = findViewById<LinearLayout>(R.id.navSettings)

        // Set Home as selected initially
        setNavItemSelected(navDownloads, true)

        navHome.setOnClickListener {
            setNavItemSelected(navHome, true)
            setNavItemSelected(navDownloads, false)
            setNavItemSelected(navSettings, false)
            // Already on home, scroll to top
            AnalyticsManager.logButtonClick("recent_downloads", "MainActivity")
            AnalyticsManager.logFeatureUsage("recent_downloads")

            // Force preload ads for next use
            BackgroundAdService.forcePreload(this)

            // إزالة الإعلان من التنقل - الانتقال مباشرة
            startActivity(Intent(this, MainActivity::class.java))
        }

        navDownloads.setOnClickListener {
            setNavItemSelected(navHome, false)
            setNavItemSelected(navDownloads, true)
            setNavItemSelected(navSettings, false)
            scrollView.smoothScrollTo(0, 0)

        }

        navSettings.setOnClickListener {
            setNavItemSelected(navHome, false)
            setNavItemSelected(navDownloads, false)
            setNavItemSelected(navSettings, true)

            AnalyticsManager.logButtonClick("settings", "MainActivity")
            AnalyticsManager.logFeatureUsage("settings")

            // Force preload ads for next use
            BackgroundAdService.forcePreload(this)

            // إزالة الإعلان من التنقل - الانتقال مباشرة
            startActivity(Intent(this, SettingsActivity::class.java))
        }
    }
    private fun setNavItemSelected(navItem: LinearLayout, isSelected: Boolean) {
        val imageView = navItem.getChildAt(0) as ImageView
        val textView = navItem.getChildAt(1) as TextView

        if (isSelected) {
            imageView.setColorFilter(getColor(R.color.insta_purple))
            textView.setTextColor(getColor(R.color.insta_purple))
            textView.setTypeface(null, android.graphics.Typeface.BOLD)
        } else {
            imageView.setColorFilter(getColor(R.color.gray))
            textView.setTextColor(getColor(R.color.gray))
            textView.setTypeface(null, android.graphics.Typeface.NORMAL)
        }
    }

    private fun getAllMediaFiles(): List<File> {
        val allFiles = mutableListOf<File>()

        if (!baseDir.exists()) return emptyList()

        try {
            // Check Videos and Images folders specifically
            val videosDir = File(baseDir, "Videos")
            val imagesDir = File(baseDir, "Images")

            listOf(videosDir, imagesDir).forEach { dir ->
                if (dir.exists()) {
                    dir.listFiles()?.forEach { file ->
                        if (file.isFile && isValidMediaFile(file)) {
                            allFiles.add(file)
                        }
                    }
                }
            }

            // Also check base directory for any remaining files
            baseDir.listFiles()?.forEach { file ->
                if (file.isFile && isValidMediaFile(file)) {
                    allFiles.add(file)
                }
            }
        } catch (e: Exception) {
            Log.e("RecentDownloads", "Error scanning directories: ${e.message}")
        }

        // Sort by last modified time (newest first) - same as loadMediaFilesInBackground
        val sortedFiles = allFiles.sortedByDescending { it.lastModified() }

        Log.d("RecentDownloads", "getAllMediaFiles: Total ${sortedFiles.size} files sorted by newest first")

        return sortedFiles
    }


    private fun updateFileInfo(textView: TextView, file: File, position: Int, total: Int) {
        val fileType = when (file.extension.lowercase()) {
            "jpg", "jpeg", "png", "webp", "gif" -> "🖼️ Image"
            "mp4", "mov", "avi", "mkv" -> "🎬 Video"
            else -> "📄 File"
        }
        textView.text = "$fileType ($position/$total)"
    }



    private fun isValidMediaFile(file: File): Boolean {
        val validExtensions = listOf("jpg", "jpeg", "png", "webp", "gif", "mp4", "mov", "avi", "mkv")
        return file.extension.lowercase() in validExtensions && file.length() > 0
    }

    private fun loadBannerAd(bannerAdView: com.google.android.gms.ads.AdView, container: com.google.android.material.card.MaterialCardView) {
        if (!AdConfig.adsEnabled) return

        try {
            // Set ad unit ID from AdConfig
            bannerAdView.adUnitId = AdConfig.bannerAdUnitId

            bannerAdView.adListener = object : com.google.android.gms.ads.AdListener() {
                override fun onAdLoaded() {
                    container.visibility = View.VISIBLE
                    Log.d("RecentDownloads", "Banner ad loaded in player")
                }

                override fun onAdFailedToLoad(error: com.google.android.gms.ads.LoadAdError) {
                    container.visibility = View.GONE
                    Log.e("RecentDownloads", "Banner ad failed to load: ${error.message}")
                }
            }

            bannerAdView.loadAd(com.google.android.gms.ads.AdRequest.Builder().build())

        } catch (e: Exception) {
            Log.e("RecentDownloads", "Error loading banner ad: ${e.message}")
            container.visibility = View.GONE
        }
    }

    override fun onOptionsItemSelected(item: android.view.MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                showInterstitialAdBeforeExit()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    override fun onBackPressed() {
        super.onBackPressed()
        showInterstitialAdBeforeExit()
    }

    private fun showInterstitialAdBeforeExit() {
        // Show interstitial ad before exiting
        if (interstitialAdManager.isAdReady()) {
            interstitialAdManager.forceShowAd(
                onAdShown = {
                    Log.d("RecentDownloadsActivity", "Interstitial ad shown before exit")
                },
                onAdDismissed = {
                    Log.d("RecentDownloadsActivity", "Interstitial ad dismissed before exit")
                    super.onBackPressed() // Exit activity after ad
                },
                onAdFailedToShow = { error ->
                    Log.e("RecentDownloadsActivity", "Failed to show interstitial ad before exit: $error")
                    super.onBackPressed() // Exit activity even if ad fails
                }
            )
        } else {
            // No ad available, exit normally
            super.onBackPressed()
        }
    }

    private fun showEmptyState(message: String? = null) {
        findViewById<LinearLayout>(R.id.emptyStateLayout).visibility = View.VISIBLE
        downloadsRecyclerView.visibility = View.GONE

        val displayMessage = message ?: run {
            val filterDescription = getFileTypeDescription(currentFilter)
            "No $filterDescription found in $currentFilter folder.\nDownload some $currentFilter to get started!"
        }
        emptyTextView.text = displayMessage
    }

    override fun onDestroy() {
        // Cancel loading job
        loadingJob?.cancel()

        // Clean up banner ads
        currentAdapter?.destroy()

        // Clean up ads
        interstitialAdManager.destroy()

        super.onDestroy()
    }

    override fun onPause() {
        super.onPause()
        // No banner ads to pause - ads are handled by adapter
    }

    override fun onResume() {
        super.onResume()
        // No banner ads to resume - ads are handled by adapter
    }
}
