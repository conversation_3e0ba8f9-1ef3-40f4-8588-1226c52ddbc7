<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="MissingLeanbackLauncher">

    <application
        android:name=".MyApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@drawable/icon"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:theme="@style/Theme.InstaLoader"
        android:requestLegacyExternalStorage="true"
        android:preserveLegacyExternalStorage="true"
        tools:targetApi="35">
        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="@string/admob_id"/>

        <!-- Facebook Audience Network for Bidding -->
        <meta-data
            android:name="com.facebook.sdk.ApplicationId"
            android:value="@string/facebook_app_id" />
        <meta-data
            android:name="com.facebook.sdk.ClientToken"
            android:value="@string/facebook_client_token" />

        <!-- Fix for AD_SERVICES_CONFIG conflict -->
        <property
            android:name="android.adservices.AD_SERVICES_CONFIG"
            android:resource="@xml/gma_ad_services_config"
            tools:replace="android:resource" />

        <!-- Splash Activity -->
        <activity
            android:name=".SplashActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <!-- No Internet Activity -->
        <activity
            android:name=".NoInternetActivity"
            android:exported="false" />
        <!-- Main Activity -->
        <activity
            android:name=".MainActivity"
            android:exported="false"
            android:windowSoftInputMode="stateHidden|adjustResize" />

        <!-- Recent Downloads Activity -->
        <activity
            android:name=".RecentDownloadsActivity"
            android:exported="false" />

        <!-- Permissions Activity -->
        <activity
            android:name=".PermissionsActivity"
            android:exported="false"
            android:theme="@style/Theme.InstaLoader" />

        <!-- Ads Consent Activity -->
        <activity
            android:name=".AdsConsentActivity"
            android:exported="false"
            android:theme="@style/Theme.InstaLoader" />

        <activity android:name=".SettingsActivity" />
        <activity android:name=".VideoPlayerActivity" />


        <!-- File Provider for sharing files -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

    </application>
    <!-- Make features optional to increase device compatibility -->
    <uses-feature android:name="android.hardware.camera" android:required="false" />
    <uses-feature android:name="android.hardware.telephony" android:required="false" />
    <uses-feature android:name="android.hardware.location.gps" android:required="false" />
    <uses-feature android:name="android.hardware.sensor.accelerometer" android:required="false" />

    <!-- Support additional form factors -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />
    <!-- For Android 11+ manage all files permission -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />
    <!-- For Android 13+ (API 33+) media permissions -->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <!-- For Android 14+ (API 34+) partial media access -->
    <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED" />
    <uses-feature android:name="android.hardware.camera" android:required="false" />
    <uses-feature android:name="android.hardware.telephony" android:required="false" />
    <uses-feature android:name="android.hardware.location.gps" android:required="false" />
    <uses-feature android:name="android.hardware.sensor.accelerometer" android:required="false" />
    <!-- Support Android TV -->
    <uses-feature android:name="android.software.leanback" android:required="false" />
    <!-- Support Android Auto / Automotive -->
    <uses-feature android:name="android.hardware.type.automotive" android:required="false" />
    <!-- Support Chromebook -->
    <uses-feature android:name="org.chromium.arc" android:required="false" />
    <uses-feature
        android:name="android.hardware.touchscreen"
        android:required="false" />
</manifest>

