<?xml version="1.0" encoding="utf-8"?>
<!-- Floating Native Ad Layout for Fullscreen Preview -->
<com.google.android.gms.ads.nativead.NativeAdView 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="200dp"
    android:layout_height="150dp"
    android:background="@android:color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="8dp">

        <!-- Header with icon and headline -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="6dp">

            <ImageView
                android:id="@+id/ad_app_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="6dp"
                android:scaleType="centerCrop" />

            <TextView
                android:id="@+id/ad_headline"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="@android:color/black"
                android:maxLines="1"
                android:ellipsize="end" />

            <!-- Ad badge -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Ad"
                android:textSize="8sp"
                android:textColor="@android:color/white"
                android:background="@drawable/ad_badge_background"
                android:paddingLeft="4dp"
                android:paddingRight="4dp"
                android:paddingTop="1dp"
                android:paddingBottom="1dp" />

        </LinearLayout>

        <!-- Body text -->
        <TextView
            android:id="@+id/ad_body"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:textSize="10sp"
            android:textColor="@android:color/black"
            android:maxLines="3"
            android:ellipsize="end"
            android:layout_marginBottom="6dp" />

        <!-- Call to Action Button -->
        <Button
            android:id="@+id/ad_call_to_action"
            android:layout_width="match_parent"
            android:layout_height="32dp"
            android:textSize="10sp"
            android:textAllCaps="false"
            android:background="@color/insta_purple"
            android:textColor="@android:color/white"
            android:text="Install" />

    </LinearLayout>

</com.google.android.gms.ads.nativead.NativeAdView>
