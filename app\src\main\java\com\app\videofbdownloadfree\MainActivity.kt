package com.app.videofbdownloadfree

import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.graphics.SurfaceTexture
import android.media.AudioManager
import android.media.MediaPlayer
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.Surface
import android.view.TextureView
import android.view.View

import android.widget.*
import androidx.annotation.RequiresApi
import androidx.cardview.widget.CardView
import com.chaquo.python.Python
import com.chaquo.python.android.AndroidPlatform
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.AdSize
import com.google.android.gms.ads.AdView
import com.google.android.gms.ads.MobileAds
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.firebase.database.DataSnapshot
import com.google.firebase.database.DatabaseError
import com.google.firebase.database.FirebaseDatabase
import com.google.firebase.database.MutableData
import com.google.firebase.database.Transaction
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File
import java.io.IOException

class MainActivity : BaseActivity() {

    private lateinit var inputBox: EditText
    private lateinit var statusText: TextView
    private lateinit var btnDownload: Button
    private lateinit var downloadCounter: android.widget.TextView
    private lateinit var btnRecent: Button
    private lateinit var btnHelp: Button
    private lateinit var scrollView: androidx.core.widget.NestedScrollView
    private lateinit var progressCard: androidx.cardview.widget.CardView
    private lateinit var btnPaste: Button
    private lateinit var adView: AdView
    private lateinit var nativeAdContainer: android.widget.FrameLayout
    private lateinit var nativeAdManager: NativeAdManager
    private lateinit var downloadLimitManager: DownloadLimitManager
    private lateinit var rewardedAdManager: RewardedAdManager
    private lateinit var interstitialAdManager: InterstitialAdManager
    private lateinit var adRefreshManager: AdRefreshManager
    private lateinit var networkManager: NetworkManager
    private lateinit var recentDownloadsRecyclerView: androidx.recyclerview.widget.RecyclerView
    private lateinit var recentDownloadsAdapter: RecentDownloadsAdapter
    private lateinit var seeAllButton: TextView
    private lateinit var recentDownloadsSection: LinearLayout
    private lateinit var sectionTitle: TextView
    private var mediaPlayer: MediaPlayer? = null





    private fun incrementDownloadsCount(){
        val ref = FirebaseDatabase.getInstance().getReference("downloadsCount")
        ref.runTransaction(object : Transaction.Handler{
            override fun doTransaction(currentData: MutableData): Transaction.Result {
                val current = currentData.getValue(Int::class.java) ?:0
                currentData.value = current + 1
                return Transaction.success(currentData)
            }
            override  fun onComplete(error:DatabaseError?,committed:Boolean,currentData:DataSnapshot?){
                if(error != null){
                    FirebaseCrashlytics.getInstance().recordException(
                        Exception("Failed to increment downloadsCount: ${error.message}")
                    )
                }else{
                    Log.d("Firebase","Add to database")
                }
            }
        })
    }
    private fun getTimeAgo(timestamp: Long): String {
        val now = System.currentTimeMillis()
        val diff = now - timestamp

        return when {
            diff < 60000 -> "Just now"
            diff < 3600000 -> "${diff / 60000}m ago"
            diff < 86400000 -> "${diff / 3600000}h ago"
            diff < 604800000 -> "${diff / 86400000}d ago"
            else -> "${diff / 604800000}w ago"
        }
    }





    @RequiresApi(Build.VERSION_CODES.M)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Enable edge-to-edge display
        PerformanceManager.startTrace("MainActivity")
        enableEdgeToEdge()

        setContentView(R.layout.activity_main)

        // Handle window insets for navigation bar
        setupWindowInsets()
        // Initialize AdMob first
        MobileAds.initialize(this) { initializationStatus ->
            Log.d("MainActivity", "AdMob initialized: ${initializationStatus.adapterStatusMap}")
            // Start background ad service after AdMob is initialized
            Handler(Looper.getMainLooper()).postDelayed({
                BackgroundAdService.start(this)
                // Initial preload for immediate use
                AdPreloader.preloadAds(this, forcePreload = true)
            }, 1000) // 1 second delay to ensure everything is ready
        }

        // Setup banner ad
        val adContainer = findViewById<LinearLayout>(R.id.adContainer)
        adView = AdView(this)
        adView.setAdSize(AdSize.getCurrentOrientationAnchoredAdaptiveBannerAdSize(this,360))
        adView.adUnitId = AdConfig.bannerAdUnitId
        adContainer.addView(adView)
        // Configure AdRequest for Facebook Bidding
        val adRequestBuilder = AdRequest.Builder()
        val adReq = FacebookBiddingHelper.configureAdRequestForBidding(adRequestBuilder).build()
        adView.loadAd(adReq)


        // Initially hide action bar title
        supportActionBar?.setDisplayShowTitleEnabled(false)
        inputBox = findViewById(R.id.inputBox)
        statusText = findViewById(R.id.StatusText)
        btnDownload = findViewById(R.id.button)
        downloadCounter = findViewById(R.id.downloadCounter)
        btnRecent = findViewById(R.id.btnRecent)
        btnHelp = findViewById(R.id.btnHelp)
        scrollView = findViewById(R.id.main_scroll_view)
        btnPaste = findViewById(R.id.btnPaste)
        nativeAdContainer = findViewById(R.id.nativeAdContainer)
        progressCard = findViewById(R.id.progressCard)
        recentDownloadsRecyclerView = findViewById(R.id.recentDownloadsRecyclerView)
        seeAllButton = findViewById(R.id.seeAllButton)
        recentDownloadsSection = findViewById(R.id.recentDownloadsSection)
        sectionTitle = findViewById(R.id.sectionTitle)

        // Initialize managers
        nativeAdManager = NativeAdManager(this)
        downloadLimitManager = DownloadLimitManager(this)
        rewardedAdManager = RewardedAdManager(this)
        interstitialAdManager = InterstitialAdManager(this)
        networkManager = NetworkManager.getInstance(this)

        // Get App Open Ad Manager from Application
        val appOpenAdManager = (application as? MyApplication)?.getAppOpenAdManager()

        // Initialize AdRefreshManager
        adRefreshManager = AdRefreshManager(this)
        adRefreshManager.initialize(
            interstitialManager = interstitialAdManager,
            bannerManager = null, // Will be set in activities that have banners
            appOpenManager = appOpenAdManager
        )

        // Add lifecycle observer
        lifecycle.addObserver(adRefreshManager)


        // Load Native Ad
        loadNativeAd()

        // Ads are now preloaded in AdMob initialization callback above

        // Start auto refresh for ads
        adRefreshManager.startAutoRefresh()

        // Initial button state will be set in setupInputValidation()

        // Update download status
        updateDownloadStatus()

        // Log MainActivity start
        CrashlyticsManager.log("MainActivity started")
        AnalyticsManager.logScreenView("Main", "MainActivity")
        AnalyticsManager.logAppOpen()

        // Start continuous internet monitoring
        startInternetMonitoring()

        // Ensure SaveIt directory exists
        ensureSaveItDirectoryExists()

        // Check if permissions are granted, if not redirect to PermissionsActivity
        if (!PermissionsActivity.arePermissionsGranted(this)) {
            Toast.makeText(this, "⚠️ Storage permissions required for full functionality", Toast.LENGTH_LONG).show()
        }



        if (!Python.isStarted()) {
            Python.start(AndroidPlatform(this))
        }


        btnDownload.setOnClickListener {
            AnalyticsManager.logButtonClick("download", "MainActivity")
            val input = inputBox.text.toString().trim()

            // Validate input first
            if (input.isEmpty()) {
                Toast.makeText(this, "Please enter Instagram link or username", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }

            AnalyticsManager.logSearchEvent(input)

            if (!input.contains("instagram")) {
                Toast.makeText(this, "Please Enter Valid Instagram Link!", Toast.LENGTH_LONG).show()
                return@setOnClickListener
            }

            // Check internet connection first
            if (!networkManager.isNetworkAvailable()) {
                goToNoInternetActivity()
                return@setOnClickListener
            }

            // Check storage permissions first
            if (!PermissionsActivity.arePermissionsGranted(this)) {
                android.app.AlertDialog.Builder(this)
                    .setTitle("Storage Permission Required")
                    .setMessage("Storage access is required to download content. Would you like to enable it now?")
                    .setPositiveButton("Enable") { _, _ ->
                        startActivity(Intent(this, PermissionsActivity::class.java))
                    }
                    .setNegativeButton("Cancel", null)
                    .show()
                return@setOnClickListener
            }

            // Check download limit
            if (downloadLimitManager.canDownload()) {
                // User has downloads left - start download and update UI immediately
                downloadInstagramContent(input)
                // Update counter immediately after starting download
                updateDownloadStatus()
            } else {
                // Show rewarded ad to get more downloads
                showRewardedAdDialog()
            }
        }

        btnRecent.setOnClickListener {
            AnalyticsManager.logButtonClick("recent_downloads", "MainActivity")
            AnalyticsManager.logFeatureUsage("recent_downloads")

            // Force preload ads for next use
            BackgroundAdService.forcePreload(this)

            // Show interstitial ad with smart loading
            interstitialAdManager.showAdWithLoading(
                onAdShown = {
                    Log.d("MainActivity", "Interstitial ad shown before Recent Downloads")
                },
                onAdDismissed = {
                    Log.d("MainActivity", "Interstitial ad dismissed, opening Recent Downloads")
                    startActivity(Intent(this, RecentDownloadsActivity::class.java))
                },
                onAdFailedToShow = { error ->
                    Log.e("MainActivity", "Failed to show interstitial ad: $error")
                    // Open Recent Downloads even if ad fails
                    startActivity(Intent(this, RecentDownloadsActivity::class.java))
                }
            )
        }

        btnHelp.setOnClickListener {
            // Show how to use guide
            showHowToUseDialog()
        }



        btnPaste.setOnClickListener {
            handleClipboard()
        }

        // Setup input validation for download button
        setupInputValidation()

        // Setup recent downloads
        setupRecentDownloads()

        // Setup bottom navigation
        setupBottomNavigation()

        // Clear focus from input field to prevent keyboard from showing
        inputBox.clearFocus()

        //Stop Performance
        PerformanceManager.stopTrace("MainActivity")
    }


    private fun handleClipboard() {
        val clipboard = getSystemService(CLIPBOARD_SERVICE) as android.content.ClipboardManager
        val clipData = clipboard.primaryClip

        if (clipData != null && clipData.itemCount > 0) {
            val item = clipData.getItemAt(0)
            val pastedText = item.text

            if (!pastedText.isNullOrBlank()) {
                inputBox.setText(pastedText.toString())
                // Validate the pasted text
                validateInput(pastedText.toString().trim())
                Toast.makeText(this, "Pasted from clipboard", Toast.LENGTH_SHORT).show()
            } else {
                Toast.makeText(this, "Content Not Valid", Toast.LENGTH_SHORT).show()
            }
        } else {
            Toast.makeText(this, "Clipboard is empty", Toast.LENGTH_SHORT).show()
        }
    }

    private fun setupBottomNavigation() {
        val navHome = findViewById<LinearLayout>(R.id.navHome)
        val navDownloads = findViewById<LinearLayout>(R.id.navDownloads)
        val navSettings = findViewById<LinearLayout>(R.id.navSettings)

        // Set Home as selected initially
        setNavItemSelected(navHome, true)

        navHome.setOnClickListener {
            setNavItemSelected(navHome, true)
            setNavItemSelected(navDownloads, false)
            setNavItemSelected(navSettings, false)
            // Already on home, scroll to top
            scrollView.smoothScrollTo(0, 0)
        }

        navDownloads.setOnClickListener {
            setNavItemSelected(navHome, false)
            setNavItemSelected(navDownloads, true)
            setNavItemSelected(navSettings, false)

            AnalyticsManager.logButtonClick("recent_downloads", "MainActivity")
            AnalyticsManager.logFeatureUsage("recent_downloads")

            // Force preload ads for next use
            BackgroundAdService.forcePreload(this)

            // إزالة الإعلان من التنقل - الانتقال مباشرة
            startActivity(Intent(this, RecentDownloadsActivity::class.java))
        }

        navSettings.setOnClickListener {
            setNavItemSelected(navHome, false)
            setNavItemSelected(navDownloads, false)
            setNavItemSelected(navSettings, true)

            AnalyticsManager.logButtonClick("settings", "MainActivity")
            AnalyticsManager.logFeatureUsage("settings")

            // Force preload ads for next use
            BackgroundAdService.forcePreload(this)

            // إزالة الإعلان من التنقل - الانتقال مباشرة
            startActivity(Intent(this, SettingsActivity::class.java))
        }
    }

    private fun setNavItemSelected(navItem: LinearLayout, isSelected: Boolean) {
        val imageView = navItem.getChildAt(0) as ImageView
        val textView = navItem.getChildAt(1) as TextView

        if (isSelected) {
            imageView.setColorFilter(getColor(R.color.insta_purple))
            textView.setTextColor(getColor(R.color.insta_purple))
            textView.setTypeface(null, android.graphics.Typeface.BOLD)
        } else {
            imageView.setColorFilter(getColor(R.color.gray))
            textView.setTextColor(getColor(R.color.gray))
            textView.setTypeface(null, android.graphics.Typeface.NORMAL)
        }
    }

    private fun showHowToUseDialog() {
        // Create custom dialog with detailed instructions
        val dialog = android.app.Dialog(this)
        dialog.setContentView(R.layout.dialog_how_to_use)
        dialog.window?.setLayout(
            android.view.ViewGroup.LayoutParams.MATCH_PARENT,
            android.view.ViewGroup.LayoutParams.WRAP_CONTENT
        )

        // Setup close buttons
        val btnClose = dialog.findViewById<android.widget.Button>(R.id.btnCloseDialog)
        val btnCloseBottom = dialog.findViewById<android.widget.Button>(R.id.btnCloseDialogBottom)

        btnClose?.setOnClickListener {
            dialog.dismiss()
        }

        btnCloseBottom?.setOnClickListener {
            dialog.dismiss()
        }

        dialog.show()
    }

    private fun setupPlayerControls(
        videoView: TextureView,
        imageView: ImageView,
        btnPlayPause: ImageButton,
        btnClose: ImageButton,
        btnPrevious: ImageButton,
        btnNext: ImageButton,
        fileInfoText: TextView,
        allFiles: List<File>,
        initialIndex: Int,
        onFileChanged: (File) -> Unit
    ) {
        val audioManager = getSystemService(Context.AUDIO_SERVICE) as AudioManager
        var currentIndex = initialIndex
        var isPlaying = false

        // Play/Pause button
        btnPlayPause.setOnClickListener {
            if (mediaPlayer?.isPlaying === true) {
                mediaPlayer?.pause()
                btnPlayPause.setImageResource(android.R.drawable.ic_media_play)
                isPlaying = false
            } else {
                mediaPlayer?.start()
                btnPlayPause.setImageResource(android.R.drawable.ic_media_pause)
                isPlaying = true
            }
        }

        // Previous button
        btnPrevious.setOnClickListener {
            if (allFiles.isNotEmpty() && currentIndex > 0) {
                currentIndex--
                val newFile = allFiles[currentIndex]
                onFileChanged(newFile)
                updateFileInfo(fileInfoText, newFile, currentIndex + 1, allFiles.size)
            }
        }

        // Next button
        btnNext.setOnClickListener {
            if (allFiles.isNotEmpty() && currentIndex < allFiles.size - 1) {
                currentIndex++
                val newFile = allFiles[currentIndex]
                onFileChanged(newFile)
                updateFileInfo(fileInfoText, newFile, currentIndex + 1, allFiles.size)
            }
        }





        // Close button
        btnClose.setOnClickListener {
            if (mediaPlayer?.isPlaying == true) {
                mediaPlayer?.release()
                mediaPlayer = null
                // اذهب مباشرةً إلى MainActivity مع إنهاء النشاط الحالي
                val intent = Intent(this, MainActivity::class.java)
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK)
                startActivity(intent)
                finish()  // تأكد من إغلاق هذا النشاط لتجنب تراكب الأنشطة
            } else {
                // إذا لم يكن الفيديو شغّال، فقط ارجع للنشاط السابق
                onBackPressed()
            }
        }

        // Video/Image click to toggle play/pause
        videoView.setOnClickListener {
            if (btnPlayPause.visibility == View.VISIBLE) {
                btnPlayPause.performClick()
            }
        }

        imageView.setOnClickListener {
            // For images, just show next/previous
            btnNext.performClick()
        }

        // Update initial file info
        if (allFiles.isNotEmpty() && currentIndex >= 0) {
            updateFileInfo(fileInfoText, allFiles[currentIndex], currentIndex + 1, allFiles.size)
        }
    }

    private fun updateFileInfo(textView: TextView, file: File, position: Int, total: Int) {
        val fileType = when (file.extension.lowercase()) {
            "jpg", "jpeg", "png", "webp", "gif" -> "🖼️ Image"
            "mp4", "mov", "avi", "mkv" -> "🎬 Video"
            else -> "📄 File"
        }
        textView.text = "$fileType ($position/$total)"
    }



    private fun showRewardedAdDialog() {
        val remaining = downloadLimitManager.getRemainingDownloads()

        android.app.AlertDialog.Builder(this)
            .setTitle("🎁 Get Free Downloads")
            .setMessage("""
                You've used all your free downloads ($remaining/5)

                Watch a short ad to get 5 more downloads for free!

                📺 Ad Duration: ~30 seconds
                🎁 Reward: 5 free downloads
            """.trimIndent())
            .setPositiveButton("🎬 Watch Ad") { _, _ ->
                showRewardedAd()
            }
            .setNegativeButton("Cancel", null)
            .setIcon(android.R.drawable.ic_dialog_info)
            .show()
    }

    private fun showRewardedAd() {
        // Check internet connection first
        if (!isNetworkAvailable()) {
            Toast.makeText(this, "No internet connection. Please check your network.", Toast.LENGTH_LONG).show()
            return
        }

        // Force preload for next time
        BackgroundAdService.forcePreload(this)

        if (rewardedAdManager.isAdReady()) {
            // Ad is ready, show immediately
            rewardedAdManager.showRewardedAd(
                onUserEarnedReward = {
                    // User watched the ad, give them downloads
                    downloadLimitManager.addDownloadsFromReward()

                    // Update UI immediately
                    updateDownloadStatus()

                    android.app.AlertDialog.Builder(this)
                        .setTitle("🎉 Congratulations!")
                        .setMessage("You've earned 5 additional free downloads!\n\nYou can now continue with your download.")
                        .setPositiveButton("Awesome!") { _, _ ->
                            // Ensure UI is updated again when dialog is dismissed
                            updateDownloadStatus()
                        }
                        .setIcon(android.R.drawable.ic_dialog_info)
                        .show()
                },
                onAdFailedToShow = { error ->
                    Toast.makeText(this, "Failed to show ad: $error", Toast.LENGTH_LONG).show()
                    // Preload new ad in background
                    BackgroundAdService.forcePreload(this)
                }
            )
        } else {
            // Ad not ready, show loading and load ad
            val loadingDialog = AdLoadingDialog(this)
            loadingDialog.show("Loading rewarded ad...")

            rewardedAdManager.loadRewardedAd(
                onAdLoaded = {
                    loadingDialog.dismiss()
                    // Show the ad immediately after loading
                    showRewardedAd()
                },
                onAdFailedToLoad = { error ->
                    loadingDialog.dismiss()
                    Toast.makeText(this, "Failed to load ad: $error", Toast.LENGTH_LONG).show()
                }
            )
        }
    }

    private fun updateDownloadStatus() {
        val remaining = downloadLimitManager.getRemainingDownloads()
        downloadCounter.text = "Free Downloads: $remaining/5"

        // Re-validate input to update button state correctly
        val currentInput = inputBox.text.toString().trim()
        validateInput(currentInput)

        Log.d("MainActivity", "Download status updated, remaining: $remaining")
    }

    private fun showInterstitialAdAfterDownload() {
        // Increment download counter for ad frequency
        interstitialAdManager.incrementDownloadCounter()

        // Force preload for next time
        BackgroundAdService.forcePreload(this)

        // Show interstitial ad if conditions are met
        interstitialAdManager.showInterstitialAd(
            onAdShown = {
                Log.d("MainActivity", "Interstitial ad shown after download")
            },
            onAdDismissed = {
                Log.d("MainActivity", "Interstitial ad dismissed after download")
                // Preload again for next use
                BackgroundAdService.forcePreload(this)
            },
            onAdFailedToShow = { error ->
                Log.e("MainActivity", "Failed to show interstitial ad after download: $error")
                // Preload again on failure
                BackgroundAdService.forcePreload(this)
            }
        )
    }



    private fun startInternetMonitoring() {
        networkManager.isConnected.observe(this) { isConnected ->
            if (!isConnected) {
                // Internet lost, go to NoInternetActivity immediately
                goToNoInternetActivity()
            }
        }
    }

    private fun goToNoInternetActivity() {
        val intent = Intent(this, NoInternetActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish()
    }

    private fun isNetworkAvailable(): Boolean {
        return networkManager.isNetworkAvailable()
    }
    // Events Ads Admob
    override fun onPause() {
        adView.pause()
        super.onPause()
    }
    override fun onResume() {
        super.onResume()
        adView.resume()
        // Update download status when returning to activity
        updateDownloadStatus()

        // Immediate refresh of recent downloads
        loadRecentDownloads()

        // Additional refresh with small delay to ensure everything is loaded
        Handler(Looper.getMainLooper()).postDelayed({
            loadRecentDownloads()
        }, 300) // Reduced delay for faster response

        enableImmersiveMode()
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        enableImmersiveMode()
    }
    private fun openFileInVideoPlayer(file: File) {
        val intent = Intent(this, VideoPlayerActivity::class.java).apply {
            putExtra("file_path", file.absolutePath)
        }
        startActivity(intent)
    }

    /**
     * Setup input validation for download button
     */
    private fun setupInputValidation() {
        // Initially disable the download button
        updateDownloadButtonState(false, "Enter Instagram Link")

        // Add text watcher to input field
        inputBox.addTextChangedListener(object : android.text.TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

            override fun afterTextChanged(s: android.text.Editable?) {
                val input = s?.toString()?.trim() ?: ""
                validateInput(input)
            }
        })
    }

    /**
     * Validate input and update button state
     */
    private fun validateInput(input: String) {
        when {
            input.isEmpty() -> {
                updateDownloadButtonState(false, "Enter Instagram Link")
            }
            !input.contains("instagram", ignoreCase = true) -> {
                updateDownloadButtonState(false, "Enter Valid Instagram Link")
            }
            else -> {
                // Check download limit and update accordingly
                val remaining = downloadLimitManager.getRemainingDownloads()
                if (remaining > 0) {
                    updateDownloadButtonState(true, getString(R.string.download_now))
                } else {
                    updateDownloadButtonState(true, "🎬 Watch Ad to Download")
                }
            }
        }
    }

    /**
     * Update download button state and text
     */
    private fun updateDownloadButtonState(enabled: Boolean, text: String) {
        btnDownload.isEnabled = enabled
        btnDownload.text = text

        // The selector drawable will handle the visual state automatically
        // Set alpha for additional visual feedback
        btnDownload.alpha = if (enabled) 1.0f else 0.6f

        Log.d("MainActivity", "Button state updated - Enabled: $enabled, Text: $text")
    }

    /**
     * Clear input field and reset button state
     */
    private fun clearInputField() {
        // Add a subtle animation effect
        inputBox.animate()
            .alpha(0.3f)
            .setDuration(150)
            .withEndAction {
                inputBox.setText("")
                inputBox.animate()
                    .alpha(1.0f)
                    .setDuration(150)
                    .start()
            }
            .start()

        // This will trigger the TextWatcher and automatically update button state
        Log.d("MainActivity", "Input field cleared with animation")

        // Update recent downloads after clearing input (in case new files were added)
        loadRecentDownloads()
    }

    /**
     * Setup last 5 downloads RecyclerView
     */
    private fun setupRecentDownloads() {
        // Initialize adapter
        recentDownloadsAdapter = RecentDownloadsAdapter(
            context = this,
            onItemClick = { item ->
                // Open file in video player
                openFileInVideoPlayer(item.file)
            },
            onActionClick = { item ->
                // Show action menu (share, delete, etc.)
                showFileActionMenu(item.file)
            }
        )

        // Setup RecyclerView
        recentDownloadsRecyclerView.apply {
            adapter = recentDownloadsAdapter
            layoutManager = androidx.recyclerview.widget.LinearLayoutManager(this@MainActivity)
            isNestedScrollingEnabled = false
        }

        // Setup "See All" button
        seeAllButton.setOnClickListener {
            // Navigate to RecentDownloadsActivity
            startActivity(Intent(this, RecentDownloadsActivity::class.java))
        }

        // Load recent downloads
        loadRecentDownloads()
    }

    /**
     * Load last 5 downloads from storage
     */
    private fun loadRecentDownloads() {
        try {
            val saveItDir = File(getExternalFilesDir(null), "SaveIt")
            if (!saveItDir.exists()) {
                Log.d("MainActivity", "SaveIt directory does not exist")
                showEmptyState()
                return
            }

            val allFiles = mutableListOf<File>()

            // Check Videos folder
            val videosDir = File(saveItDir, "Videos")
            if (videosDir.exists()) {
                videosDir.listFiles()?.forEach { file ->
                    if (file.isFile && isVideoFile(file)) {
                        allFiles.add(file)
                        Log.d("MainActivity", "Found video: ${file.name}")
                    }
                }
            }

            // Check Images folder
            val imagesDir = File(saveItDir, "Images")
            if (imagesDir.exists()) {
                imagesDir.listFiles()?.forEach { file ->
                    if (file.isFile && isImageFile(file)) {
                        allFiles.add(file)
                        Log.d("MainActivity", "Found image: ${file.name}")
                    }
                }
            }

            // Also check base SaveIt directory for any remaining files
            saveItDir.listFiles()?.forEach { file ->
                if (file.isFile && (isImageFile(file) || isVideoFile(file))) {
                    allFiles.add(file)
                    Log.d("MainActivity", "Found file in base: ${file.name}")
                }
            }

            // Sort by last modified and take only the first 5 files
            val recentFiles = allFiles.sortedByDescending { it.lastModified() }.take(5)

            Log.d("MainActivity", "Total files found: ${allFiles.size}, showing recent: ${recentFiles.size}")

            if (recentFiles.isEmpty()) {
                Log.d("MainActivity", "No recent files found, showing empty state")
                showEmptyState()
                return
            }

            val recentItems = recentFiles.map { file ->
                RecentDownloadItem(
                    file = file,
                    userName = extractUsernameFromFile(file),
                    description = generateDescription(file),
                    downloadTime = file.lastModified()
                )
            }

            recentDownloadsAdapter.updateItems(recentItems)
            showDownloadsList(recentItems.size)

            Log.d("MainActivity", "Loaded ${recentItems.size} recent downloads successfully")
        } catch (e: Exception) {
            Log.e("MainActivity", "Error loading recent downloads", e)
            showEmptyState()
        }
    }

    /**
     * Show empty state when no downloads available
     */
    private fun showEmptyState() {
        // Hide the entire section when no downloads
        recentDownloadsSection.visibility = View.GONE
        Log.d("MainActivity", "No downloads found - hiding recent downloads section")
    }

    /**
     * Show downloads list when downloads are available
     */
    private fun showDownloadsList(itemCount: Int) {
        // Show the entire section
        recentDownloadsSection.visibility = View.VISIBLE
        recentDownloadsRecyclerView.visibility = View.VISIBLE

        // Update title dynamically based on count
        val title = when (itemCount) {
            1 -> "Last Download"
            2 -> "Last 2 Downloads"
            3 -> "Last 3 Downloads"
            4 -> "Last 4 Downloads"
            5 -> "Last 5 Downloads"
            else -> "Recent Downloads"
        }

        sectionTitle.text = title
        Log.d("MainActivity", "Showing $itemCount downloads with title: $title")
    }

    private fun isImageFile(file: File): Boolean {
        val imageExtensions = listOf("jpg", "jpeg", "png", "gif", "webp")
        return imageExtensions.contains(file.extension.lowercase())
    }

    private fun isVideoFile(file: File): Boolean {
        val videoExtensions = listOf("mp4", "mov", "avi", "mkv", "webm", "3gp")
        return videoExtensions.contains(file.extension.lowercase())
    }

    private fun extractUsernameFromFile(file: File): String {
        // Generate random usernames for demo
        val usernames = listOf(
            "Ray Potter", "Alvin Newton", "Jackson Townsend",
            "Emma Wilson", "Lucas Brown", "Sophia Davis",
            "Oliver Johnson", "Isabella Garcia", "Mason Miller"
        )
        return usernames.random()
    }

    private fun generateDescription(file: File): String {
        val descriptions = listOf(
            "Behind the scene from the last...",
            "woman standing on focus photography",
            "woman in brown hat",
            "Amazing sunset view from today",
            "Coffee time with friends",
            "New adventure begins here",
            "Beautiful moments captured",
            "Life is beautiful when shared"
        )
        return descriptions.random()
    }

    private fun showFileActionMenu(file: File) {
        // Simple action menu - you can expand this
        val options = arrayOf("Open", "Share", "Delete")

        android.app.AlertDialog.Builder(this)
            .setTitle(file.name)
            .setItems(options) { _, which ->
                when (which) {
                    0 -> openFileInVideoPlayer(file) // Open
                    1 -> shareFile(file) // Share
                    2 -> deleteFile(file) // Delete
                }
            }
            .show()
    }

    private fun shareFile(file: File) {
        try {
            val uri = androidx.core.content.FileProvider.getUriForFile(
                this,
                "${packageName}.provider",
                file
            )

            val shareIntent = Intent().apply {
                action = Intent.ACTION_SEND
                putExtra(Intent.EXTRA_STREAM, uri)
                type = if (isVideoFile(file)) "video/*" else "image/*"
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            }

            startActivity(Intent.createChooser(shareIntent, "Share file"))
        } catch (e: Exception) {
            Toast.makeText(this, "Error sharing file", Toast.LENGTH_SHORT).show()
        }
    }

    private fun deleteFile(file: File) {
        android.app.AlertDialog.Builder(this)
            .setTitle("Delete File")
            .setMessage("Are you sure you want to delete ${file.name}?")
            .setPositiveButton("Delete") { _, _ ->
                if (file.delete()) {
                    Toast.makeText(this, "File deleted", Toast.LENGTH_SHORT).show()
                    Log.d("MainActivity", "File deleted: ${file.name}, updating recent downloads")

                    // Immediate refresh after deletion
                    loadRecentDownloads()

                    // Additional refresh to ensure UI is updated
                    Handler(Looper.getMainLooper()).postDelayed({
                        loadRecentDownloads()
                    }, 500)
                } else {
                    Toast.makeText(this, "Failed to delete file", Toast.LENGTH_SHORT).show()
                }
            }
            .setNegativeButton("Cancel", null)
            .show()
    }






    private fun loadBannerAd(bannerAdView: com.google.android.gms.ads.AdView, container: com.google.android.material.card.MaterialCardView) {
        if (!AdConfig.adsEnabled) return

        try {
            // Set ad unit ID from AdConfig
            bannerAdView.adUnitId = AdConfig.bannerAdUnitId

            bannerAdView.adListener = object : com.google.android.gms.ads.AdListener() {
                override fun onAdLoaded() {
                    container.visibility = View.VISIBLE
                    Log.d("MainActivity", "Banner ad loaded in player")
                }

                override fun onAdFailedToLoad(error: com.google.android.gms.ads.LoadAdError) {
                    container.visibility = View.GONE
                    Log.e("MainActivity", "Banner ad failed to load: ${error.message}")
                }
            }

            // Create ad request with consent consideration and Facebook Bidding
            val adRequestBuilder = com.google.android.gms.ads.AdRequest.Builder()

            // If user hasn't given consent for personalized ads, request non-personalized ads
            if (!AdConfig.isPersonalizedAdsEnabled(this)) {
                val extras = android.os.Bundle()
                extras.putString("npa", "1") // Non-personalized ads
                adRequestBuilder.addNetworkExtrasBundle(com.google.ads.mediation.admob.AdMobAdapter::class.java, extras)
            }

            // Configure for Facebook Bidding
            val finalAdRequest = FacebookBiddingHelper.configureAdRequestForBidding(adRequestBuilder).build()
            bannerAdView.loadAd(finalAdRequest)

        } catch (e: Exception) {
            Log.e("MainActivity", "Error loading banner ad: ${e.message}")
            container.visibility = View.GONE
        }
    }




    private fun downloadInstagramContent(input: String) {
        val startTime = System.currentTimeMillis()
        PerformanceManager.stopTrace("download_content")

        // Clear the input field immediately after starting download
        clearInputField()

        // Use one download
        if (!downloadLimitManager.useDownload()) {
            Toast.makeText(this, "No downloads remaining", Toast.LENGTH_SHORT).show()
            return
        }

        val py = Python.getInstance()
        val module = py.getModule("script")
         // transfer folder python to app
        val appFilesDir = getExternalFilesDir(null)?.absolutePath ?: ""
        module.callAttr("set_app_dir",appFilesDir)

        statusText.text = ""
        progressCard.visibility = View.VISIBLE

        // Show remaining downloads
        val remaining = downloadLimitManager.getRemainingDownloads()
        Toast.makeText(this, "Download started - $remaining left", Toast.LENGTH_SHORT).show()

        CoroutineScope(Dispatchers.IO).launch {
            try {
                // Ensure Python module is properly loaded
                if (!Python.isStarted()) {
                    runOnUiThread {
                        Toast.makeText(this@MainActivity, "Python not initialized. Please restart the app.", Toast.LENGTH_LONG).show()
                        statusText.text = "Error: Python not initialized"
                        progressCard.visibility = View.GONE
                    }
                    return@launch
                }

                if (input.startsWith("https://www.instagram.com/")) {
                    val shortcode = when {
                        input.contains("/p/") -> input.substringAfter("/p/").substringBefore("/").substringBefore("?")
                        input.contains("/reel/") -> input.substringAfter("/reel/").substringBefore("/").substringBefore("?")
                        input.contains("/reels/") -> input.substringAfter("/reels/").substringBefore("/").substringBefore("?")
                        else -> ""
                    }.trim()

                    if (shortcode.isEmpty() || shortcode.length < 5) {
                        runOnUiThread {
                            Toast.makeText(this@MainActivity, "Invalid Instagram link. Please check the URL.", Toast.LENGTH_LONG).show()
                            statusText.text = "Invalid Instagram link"
                            progressCard.visibility = View.GONE
                        }
                        return@launch
                    }

                    // Determine content type based on URL
                    val contentType = when {
                        input.contains("/p/") -> "posts"
                        input.contains("/reel/") -> "reels"
                        else -> "posts" // default fallback
                    }

                    runOnUiThread {
                        statusText.text = "Downloading ${if (contentType == "reels") "reel" else "post"}..."
                    }

                    // Pass content type to Python script
                    try {
                        module.callAttr("download_post_from_link", shortcode, contentType)
                    } catch (e: Exception) {
                        Log.e("ErrorDownload", "Python function call failed: ${e.message}")
                        throw e // Re-throw to be caught by outer try-catch
                    }
                    // Save with timestamp format
                    val timestamp = java.text.SimpleDateFormat("yyyyMMdd_HHmmss", java.util.Locale.getDefault()).format(java.util.Date())
                    saveDownloadRecord("${timestamp}_${contentType}")
                }
                else {
                    try {
                        val count = module.callAttr("post_count", input).toString()
                        runOnUiThread {
                            statusText.text = "Found $count posts, Downloading..."
                        }
                        module.callAttr("download", input)
                    } catch (e: Exception) {
                        Log.e("ErrorDownload", "Python function call failed for profile: ${e.message}")
                        throw e // Re-throw to be caught by outer try-catch
                    }
                    // Save with timestamp format
                    val timestamp = java.text.SimpleDateFormat("yyyyMMdd_HHmmss", java.util.Locale.getDefault()).format(java.util.Date())
                    saveDownloadRecord("${timestamp}_profile")
                }

                runOnUiThread {
                    Toast.makeText(this@MainActivity, "Download Finished", Toast.LENGTH_LONG).show()
                    statusText.text = "✅ Download completed successfully!"
                    progressCard.visibility = View.GONE
                    incrementDownloadsCount()
                    // Update download status after completion
                    updateDownloadStatus()

                    Log.d("MainActivity", "Download completed, updating recent downloads...")

                    // Immediate update
                    loadRecentDownloads()

                    // Additional updates with delays to ensure file system sync
                    Handler(Looper.getMainLooper()).postDelayed({
                        Log.d("MainActivity", "First delayed update after download")
                        loadRecentDownloads()
                    }, 1000) // 1 second delay

                    Handler(Looper.getMainLooper()).postDelayed({
                        Log.d("MainActivity", "Second delayed update after download")
                        loadRecentDownloads()
                    }, 3000) // 3 seconds delay for final sync

                    // Log successful download
                    CrashlyticsManager.logDownloadEvent(input, true)
                    AnalyticsManager.logDownloadEvent(input, "instagram_content", true)
                    //End Start

                    // Show interstitial ad after successful download
                    showInterstitialAdAfterDownload()

                    // Increment rating dialog counter and show if needed
                    RatingDialog.incrementShowCount(this@MainActivity)
                    if (RatingDialog.shouldShowRating(this@MainActivity)) {
                        Handler(Looper.getMainLooper()).postDelayed({
                            RatingDialog(this@MainActivity).show()
                        }, 2000) // Show after 2 seconds
                    }
                    val endTime = System.currentTimeMillis() - startTime
                    val downloadFile = getCurrentPreviewFile()
                    val fileSize = downloadFile?.length() ?: 0L
                    if(downloadFile != null){
                        PerformanceManager.recordDownloadPerformance(
                            contentType = "instagram_content",
                            success = true,
                            downloadTimeMs = endTime,
                            fileSize= fileSize
                        )
                    }
                }
            } catch (e: Exception) {
                // Log failed download
                CrashlyticsManager.logDownloadEvent(input, false)
                CrashlyticsManager.logException(e, "Download failed for: $input")
                AnalyticsManager.logDownloadEvent(input, "instagram_content", false)
                AnalyticsManager.logError("download_error", e.message ?: "Unknown error", "MainActivity")
                Log.e("ErrorDownload","error: ${e}")
                runOnUiThread {
                    Toast.makeText(this@MainActivity, "Error: ${e.message}", Toast.LENGTH_LONG).show()
                    statusText.text = "Error: Please Try Again or contact Support"
                    progressCard.visibility = View.GONE

                    // Update download status in case of error
                    updateDownloadStatus()
                }
            }
        }
    }

    private fun saveDownloadRecord(folderName: String) {
        val downloads = getDownloadsList()
        if (!downloads.contains(folderName)) {
            downloads.add(folderName)
        } else {
            downloads.remove(folderName)
            downloads.add(folderName)
        }
        saveDownloadsList(downloads)

        // Trigger recent downloads update after saving download record
        runOnUiThread {
            Log.d("MainActivity", "Download record saved: $folderName, updating recent downloads immediately")

            // Force immediate update
            loadRecentDownloads()

            // Additional delayed update to ensure files are properly organized
            Handler(Looper.getMainLooper()).postDelayed({
                Log.d("MainActivity", "Delayed update after saving download record")
                loadRecentDownloads()
            }, 2000) // 2 seconds delay
        }
    }



    private fun saveDownloadsList(list: List<String>) {
        val prefs = getSharedPreferences("downloads", MODE_PRIVATE)
        val jsonArray = org.json.JSONArray()
        list.forEach { jsonArray.put(it) }
        prefs.edit().putString("download_list_ordered", jsonArray.toString()).apply()
    }

    private fun getDownloadsList(): MutableList<String> {
        val prefs = getSharedPreferences("downloads", MODE_PRIVATE)
        val json = prefs.getString("download_list_ordered", "[]") ?: "[]"
        return try {
            val jsonArray = org.json.JSONArray(json)
            val list = mutableListOf<String>()
            for (i in 0 until jsonArray.length()) {
                list.add(jsonArray.getString(i))
            }
            list
        } catch (e: Exception) {
            mutableListOf()
        }
    }

    private fun getCurrentPreviewFile(): File? {
        return try {
            // Use the same path as the Python script - app's external files directory
            val baseDir = getExternalFilesDir(null)?.let { File(it, "SaveIt") }
            if (baseDir == null || !baseDir.exists()) {
                Log.d("MainActivity", "SaveIt directory does not exist: ${baseDir?.absolutePath}")
                return null
            }

            val allFiles = mutableListOf<File>()

            // Check Videos and Images folders specifically
            val videosDir = File(baseDir, "Videos")
            val imagesDir = File(baseDir, "Images")

            Log.d("MainActivity", "Checking directories: Videos=${videosDir.exists()}, Images=${imagesDir.exists()}")

            listOf(videosDir, imagesDir).forEach { dir ->
                if (dir.exists()) {
                    dir.listFiles()?.forEach { file ->
                        if (file.isFile && isValidMediaFile(file)) {
                            allFiles.add(file)
                            Log.d("MainActivity", "Found file: ${file.name} (${file.lastModified()})")
                        }
                    }
                }
            }

            // Also check base directory for any remaining files
            baseDir.listFiles()?.forEach { file ->
                if (file.isFile && isValidMediaFile(file)) {
                    allFiles.add(file)
                    Log.d("MainActivity", "Found file in base: ${file.name} (${file.lastModified()})")
                }
            }

            val latestFile = allFiles.sortedByDescending { it.lastModified() }.firstOrNull()
            Log.d("MainActivity", "Latest file: ${latestFile?.name ?: "none"}")

            latestFile

        } catch (e: Exception) {
            Log.e("MainActivity", "Error getting preview file: ${e.message}")
            null
        }
    }

    private fun isValidMediaFile(file: File): Boolean {
        val validExtensions = listOf("jpg", "jpeg", "png", "webp", "mp4", "mov", "avi", "mkv", "gif")
        return file.extension.lowercase() in validExtensions
    }

    private fun getAllMediaFiles(): List<File> {
        val allFiles = mutableListOf<File>()
        val baseDir = getExternalFilesDir(null)?.let { File(it,"SaveIt") }

        if (baseDir == null || !baseDir.exists()) return emptyList()

        try {
            // Check Videos and Images folders specifically
            val videosDir = File(baseDir, "Videos")
            val imagesDir = File(baseDir, "Images")

            listOf(videosDir, imagesDir).forEach { dir ->
                if (dir.exists()) {
                    dir.listFiles()?.forEach { file ->
                        if (file.isFile && isValidMediaFile(file)) {
                            allFiles.add(file)
                        }
                    }
                }
            }

            // Also check base directory for any remaining files
            baseDir.listFiles()?.forEach { file ->
                if (file.isFile && isValidMediaFile(file)) {
                    allFiles.add(file)
                }
            }
        } catch (e: Exception) {
            Log.e("MainActivity", "Error scanning directories: ${e.message}")
        }

        return allFiles.sortedByDescending { it.lastModified() }
    }

    private fun ensureSaveItDirectoryExists() {
        try {
            // Use the same path as the Python script - app's external files directory
            val saveItDir = getExternalFilesDir(null)?.let { File(it, "SaveIt") }
            if (saveItDir == null) {
                Log.e("MainActivity", "Cannot get external files directory")
                return
            }

            if (!saveItDir.exists()) {
                val created = saveItDir.mkdirs()
                Log.d("MainActivity", "SaveIt directory created: $created at ${saveItDir.absolutePath}")
            } else {
                Log.d("MainActivity", "SaveIt directory already exists at ${saveItDir.absolutePath}")
            }

            // Create Videos and Images subdirectories
            val videosDir = File(saveItDir, "Videos")
            val imagesDir = File(saveItDir, "Images")

            if (!videosDir.exists()) {
                videosDir.mkdirs()
                Log.d("MainActivity", "Videos directory created at ${videosDir.absolutePath}")
            }

            if (!imagesDir.exists()) {
                imagesDir.mkdirs()
                Log.d("MainActivity", "Images directory created at ${imagesDir.absolutePath}")
            }
        } catch (e: Exception) {
            Log.e("MainActivity", "Error creating SaveIt directory: ${e.message}")
        }
    }



    private fun loadNativeAd() {
        nativeAdManager.loadNativeAd(
            container = nativeAdContainer,
            onAdLoaded = {
                Log.d("MainActivity", "Native ad loaded")
            },
            onAdFailedToLoad = { error ->
                Log.e("MainActivity", "Native ad failed: ${error.message}")
                nativeAdContainer.visibility = View.GONE
            }
        )
    }

    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        // Show interstitial ad with loading before exiting app
        interstitialAdManager.showAdWithLoading(
            onAdShown = {
                Log.d("MainActivity", "Interstitial ad shown on back press")
            },
            onAdDismissed = {
                Log.d("MainActivity", "Interstitial ad dismissed on back press")
                super.onBackPressed() // Exit app after ad
            },
            onAdFailedToShow = { error ->
                Log.e("MainActivity", "Failed to show interstitial ad on back press: $error")
                super.onBackPressed() // Exit app even if ad fails
            }
        )
    }

    // Bottom navigation replaces the old options menu

    private fun enableEdgeToEdge() {
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.R) {
            window.setDecorFitsSystemWindows(false)
        } else {
            @Suppress("DEPRECATION")
            window.decorView.systemUiVisibility = (
                android.view.View.SYSTEM_UI_FLAG_LAYOUT_STABLE or
                android.view.View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION or
                android.view.View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
            )
        }
    }

    private fun setupWindowInsets() {
        val rootView = findViewById<View>(android.R.id.content)
        androidx.core.view.ViewCompat.setOnApplyWindowInsetsListener(rootView) { view, insets ->
            val systemBars = insets.getInsets(androidx.core.view.WindowInsetsCompat.Type.systemBars())
            val navigationBars = insets.getInsets(androidx.core.view.WindowInsetsCompat.Type.navigationBars())

            // Apply padding to avoid overlap with system bars
            view.setPadding(
                systemBars.left,
                systemBars.top,
                systemBars.right,
                0 // Don't add bottom padding here, handle it per component
            )

            // Handle bottom navigation bar for ads and dialogs
            handleNavigationBarInsets(navigationBars.bottom)

            insets
        }
    }

    private fun handleNavigationBarInsets(bottomInset: Int) {
        // Add bottom margin to banner ad to avoid navigation bar
        if (::adView.isInitialized) {
            val adLayoutParams = adView.layoutParams as android.view.ViewGroup.MarginLayoutParams
            adLayoutParams.bottomMargin = bottomInset
            adView.layoutParams = adLayoutParams
        }

        // Add bottom margin to bottom navigation
        val bottomNav = findViewById<LinearLayout>(R.id.bottomNavigation)
        if (bottomNav != null) {
            val navLayoutParams = bottomNav.layoutParams as android.view.ViewGroup.MarginLayoutParams
            navLayoutParams.bottomMargin = bottomInset
            bottomNav.layoutParams = navLayoutParams
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        // Stop background ad service
        BackgroundAdService.stop()

        // Cleanup ads and managers
        try {
            adView.destroy()
            nativeAdManager.destroy()
            rewardedAdManager.destroy()
            interstitialAdManager.destroy()
            adRefreshManager.destroy()
        } catch (e: Exception) {
            Log.e("MainActivity", "Error during cleanup: ${e.message}")
        }

        // Clear preloaded ads
        AdPreloader.clearAds()
    }
}
