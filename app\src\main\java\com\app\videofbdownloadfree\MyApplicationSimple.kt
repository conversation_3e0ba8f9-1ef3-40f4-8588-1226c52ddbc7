package com.app.videofbdownloadfree

import android.app.Activity
import android.app.Application
import android.os.Bundle
import android.util.Log
import com.google.android.gms.ads.MobileAds

/**
 * Alternative Application class that doesn't use ProcessLifecycleOwner.
 * This version uses only ActivityLifecycleCallbacks for simpler dependency management.
 */
class MyApplication : Application(), Application.ActivityLifecycleCallbacks {
    
    private lateinit var appOpenAdManager: AppOpenAdManager
    private var currentActivity: Activity? = null
    private var isAppInBackground = false
    private var activityCount = 0
    
    companion object {
        const val TAG = "MyApplication"
    }
    
    override fun onCreate() {
        super.onCreate()
        PerformanceManager.initialize()
        // Register activity lifecycle callbacks
        registerActivityLifecycleCallbacks(this)
        
        // Initialize the Mobile Ads SDK
        MobileAds.initialize(this) { initializationStatus ->
            Log.d(TAG, "MobileAds initialization complete: $initializationStatus")
            // Initialize app open ad manager after SDK initialization
            appOpenAdManager = AppOpenAdManager(this)
            Log.d(TAG, "AppOpenAdManager created, loading first ad...")
            appOpenAdManager.loadAd()
        }

        // Initialize Facebook Audience Network for Bidding
        if (AdConfig.facebookBiddingEnabled) {
            FacebookAudienceNetworkManager.initialize(this)
        }
    }
    
    /**
     * ActivityLifecycleCallbacks methods to track app foreground/background state.
     */
    override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {}
    
    override fun onActivityStarted(activity: Activity) {
        activityCount++
        Log.d(TAG, "Activity started: ${activity.javaClass.simpleName}, count: $activityCount")

        // App is coming to foreground
        if (isAppInBackground && activityCount == 1) {
            isAppInBackground = false
            Log.d(TAG, "App moved to foreground - will show ad in onActivityResumed")
        }

        currentActivity = activity
    }
    
    override fun onActivityResumed(activity: Activity) {
        Log.d(TAG, "Activity resumed: ${activity.javaClass.simpleName}")
        currentActivity = activity

        // Show app open ad when activity is fully resumed and app came from background
        if (!isAppInBackground && activityCount == 1 && ::appOpenAdManager.isInitialized) {
            Log.d(TAG, "App fully resumed - attempting to show app open ad")
            Log.d(TAG, "AppOpenAdManager status: ${appOpenAdManager.getAdStatus()}")

            // Use a short delay to ensure activity is completely ready
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                if (currentActivity == activity && !activity.isFinishing && !activity.isDestroyed) {
                    appOpenAdManager.showAdIfAvailable(activity, object : AppOpenAdManager.OnShowAdCompleteListener {
                        override fun onShowAdComplete() {
                            Log.d(TAG, "App open ad show complete")
                        }
                    })
                } else {
                    Log.w(TAG, "Activity not ready for showing ad")
                }
            }, 300) // 300ms delay - balance between speed and reliability
        }
    }
    
    override fun onActivityPaused(activity: Activity) {}
    
    override fun onActivityStopped(activity: Activity) {
        activityCount--
        Log.d(TAG, "Activity stopped: ${activity.javaClass.simpleName}, count: $activityCount")

        // App is going to background
        if (activityCount == 0) {
            isAppInBackground = true
            Log.d(TAG, "App moved to background")
        }
    }
    
    override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {}
    
    override fun onActivityDestroyed(activity: Activity) {
        if (currentActivity == activity) {
            currentActivity = null
        }
    }
    
    /**
     * Get the app open ad manager instance.
     */
    fun getAppOpenAdManager(): AppOpenAdManager? {
        return if (::appOpenAdManager.isInitialized) appOpenAdManager else null
    }
}
