/ Header Record For PersistentHashMapValueStorage, +androidx.lifecycle.DefaultLifecycleObserver) (com.app.videofbdownloadfree.BaseActivity) (androidx.appcompat.app.AppCompatActivity) (com.app.videofbdownloadfree.BaseActivity2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder5 4androidx.recyclerview.widget.RecyclerView.ViewHolder2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder5 4androidx.recyclerview.widget.RecyclerView.ViewHolderK android.app.Application2android.app.Application.ActivityLifecycleCallbacks) (com.app.videofbdownloadfree.BaseActivity) (com.app.videofbdownloadfree.BaseActivity) (com.app.videofbdownloadfree.BaseActivity2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder) (com.app.videofbdownloadfree.BaseActivity) (androidx.appcompat.app.AppCompatActivity) (com.app.videofbdownloadfree.BaseActivity