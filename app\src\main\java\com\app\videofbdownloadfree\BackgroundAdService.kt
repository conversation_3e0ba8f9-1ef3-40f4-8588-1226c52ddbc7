package com.app.videofbdownloadfree

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Handler
import android.os.Looper
import android.util.Log
import kotlinx.coroutines.*

/**
 * Background service for preloading ads without blocking UI
 */
object BackgroundAdService {
    private const val TAG = "BackgroundAdService"
    private const val PRELOAD_INTERVAL = 60000L // 1 minute
    private const val INITIAL_DELAY = 5000L // 5 seconds after app start

    private var isRunning = false
    private var preloadJob: Job? = null
    private var context: Context? = null

    // أسماء traces للأداء
    private const val TRACE_SERVICE_LIFECYCLE = "background_ad_service_lifecycle"
    private const val TRACE_PRELOAD_ADS = "background_ad_preload"
    private const val TRACE_FORCE_PRELOAD = "force_ad_preload"

    /**
     * Start background ad preloading
     */
    fun start(context: Context) {
        if (isRunning) {
            Log.d(TAG, "Background ad service already running")
            return
        }

        // بدء قياس دورة حياة الخدمة
        PerformanceManager.startTrace(TRACE_SERVICE_LIFECYCLE)

        this.context = context.applicationContext
        isRunning = true

        Log.d(TAG, "Starting background ad service")

        // إضافة معلومات إضافية للقياس
        PerformanceManager.putAttribute(TRACE_SERVICE_LIFECYCLE, "device_model", android.os.Build.MODEL)
        PerformanceManager.putAttribute(TRACE_SERVICE_LIFECYCLE, "ads_enabled", AdConfig.adsEnabled.toString())

        preloadJob = CoroutineScope(Dispatchers.Main).launch {
            // Initial delay to let app settle
            delay(INITIAL_DELAY)

            var preloadCount = 0

            while (isRunning && AdConfig.adsEnabled) {
                try {
                    preloadAdsInBackground()
                    preloadCount++

                    // تسجيل عدد عمليات التحميل المسبق
                    PerformanceManager.incrementCounter(TRACE_SERVICE_LIFECYCLE, "preload_count")

                    delay(PRELOAD_INTERVAL)
                } catch (e: Exception) {
                    Log.e(TAG, "Error in background ad preloading: ${e.message}")

                    // تسجيل الأخطاء
                    PerformanceManager.incrementCounter(TRACE_SERVICE_LIFECYCLE, "error_count")
                    PerformanceManager.putAttribute(TRACE_SERVICE_LIFECYCLE, "last_error", e.message ?: "Unknown error")

                    delay(PRELOAD_INTERVAL * 2) // Wait longer on error
                }
            }
        }
    }

    /**
     * Stop background ad preloading
     */
    fun stop() {
        Log.d(TAG, "Stopping background ad service")
        isRunning = false
        preloadJob?.cancel()
        preloadJob = null
        context = null

        // إنهاء قياس دورة حياة الخدمة
        PerformanceManager.stopTrace(TRACE_SERVICE_LIFECYCLE)
    }

    /**
     * Preload ads in background without blocking UI
     */
    private suspend fun preloadAdsInBackground() {
        val ctx = context ?: return

        // بدء قياس عملية التحميل المسبق
        PerformanceManager.startTrace(TRACE_PRELOAD_ADS)
        val startTime = System.currentTimeMillis()

        try {
            withContext(Dispatchers.Main) {
                // Only preload if we don't have ads ready
                if (!AdPreloader.isInterstitialReady()) {
                    Log.d(TAG, "Preloading interstitial ad in background")
                    PerformanceManager.putAttribute(TRACE_PRELOAD_ADS, "interstitial_preload", "true")
                    AdPreloader.preloadAds(ctx, forcePreload = false)
                } else {
                    PerformanceManager.putAttribute(TRACE_PRELOAD_ADS, "interstitial_preload", "false")
                }

                if (!AdPreloader.isRewardedReady()) {
                    Log.d(TAG, "Preloading rewarded ad in background")
                    PerformanceManager.putAttribute(TRACE_PRELOAD_ADS, "rewarded_preload", "true")
                    AdPreloader.preloadAds(ctx, forcePreload = false)
                } else {
                    PerformanceManager.putAttribute(TRACE_PRELOAD_ADS, "rewarded_preload", "false")
                }
            }

            // تسجيل نجاح التحميل المسبق
            PerformanceManager.putAttribute(TRACE_PRELOAD_ADS, "success", "true")
        } catch (e: Exception) {
            // تسجيل فشل التحميل المسبق
            PerformanceManager.putAttribute(TRACE_PRELOAD_ADS, "success", "false")
            PerformanceManager.putAttribute(TRACE_PRELOAD_ADS, "error", e.message ?: "Unknown error")
            throw e // إعادة رمي الاستثناء للتعامل معه في الدالة الأم
        } finally {
            // حساب وقت التحميل المسبق
            val loadTime = System.currentTimeMillis() - startTime
            PerformanceManager.putAttribute(TRACE_PRELOAD_ADS, "load_time_ms", loadTime.toString())

            // إنهاء قياس عملية التحميل المسبق
            PerformanceManager.stopTrace(TRACE_PRELOAD_ADS)

            // تسجيل أداء تحميل الإعلان
            PerformanceManager.recordAdLoadPerformance(
                adType = "background_preload",
                success = AdPreloader.isInterstitialReady() || AdPreloader.isRewardedReady(),
                loadTimeMs = loadTime
            )
        }
    }

    /**
     * Force immediate preload (for user actions)
     */
    fun forcePreload(context: Context) {
        if (!AdConfig.adsEnabled) return

        // بدء قياس عملية التحميل المسبق الفوري
        PerformanceManager.startTrace(TRACE_FORCE_PRELOAD)
        val startTime = System.currentTimeMillis()

        CoroutineScope(Dispatchers.Main).launch {
            try {
                Log.d(TAG, "Force preloading ads")
                AdPreloader.preloadAds(context, forcePreload = true)

                // تسجيل نجاح التحميل المسبق الفوري
                PerformanceManager.putAttribute(TRACE_FORCE_PRELOAD, "success", "true")
            } catch (e: Exception) {
                // تسجيل فشل التحميل المسبق الفوري
                PerformanceManager.putAttribute(TRACE_FORCE_PRELOAD, "success", "false")
                PerformanceManager.putAttribute(TRACE_FORCE_PRELOAD, "error", e.message ?: "Unknown error")
                Log.e(TAG, "Error in force preloading ads: ${e.message}")
            } finally {
                // حساب وقت التحميل المسبق الفوري
                val loadTime = System.currentTimeMillis() - startTime
                PerformanceManager.putAttribute(TRACE_FORCE_PRELOAD, "load_time_ms", loadTime.toString())

                // إنهاء قياس عملية التحميل المسبق الفوري
                PerformanceManager.stopTrace(TRACE_FORCE_PRELOAD)

                // تسجيل أداء تحميل الإعلان
                PerformanceManager.recordAdLoadPerformance(
                    adType = "force_preload",
                    success = AdPreloader.isInterstitialReady() || AdPreloader.isRewardedReady(),
                    loadTimeMs = loadTime
                )
            }
        }
    }

    /**
     * Check if service is running
     */
    fun isRunning(): Boolean = isRunning

    /**
     * Get ads readiness status
     */
    fun getAdsStatus(): String {
        return "Interstitial: ${if (AdPreloader.isInterstitialReady()) "Ready" else "Loading"}, " +
                "Rewarded: ${if (AdPreloader.isRewardedReady()) "Ready" else "Loading"}"
    }

    /**
     * إزالة دالة onStartCommand غير المستخدمة
     * هذه الدالة لا تنتمي إلى object، بل إلى Service
     */
}