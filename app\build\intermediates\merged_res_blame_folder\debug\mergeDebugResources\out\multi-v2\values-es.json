{"logs": [{"outputFile": "com.app.videofbdownloadfree.app-mergeDebugResources-61:/values-es/values-es.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\19f3705e3499cc83b2f3aa3cd543b099\\transformed\\preference-1.2.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,271,352,499,668,756", "endColumns": "69,95,80,146,168,87,81", "endOffsets": "170,266,347,494,663,751,833"}, "to": {"startLines": "66,70,138,151,164,165,166", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6879,7183,12759,13967,14953,15122,15210", "endColumns": "69,95,80,146,168,87,81", "endOffsets": "6944,7274,12835,14109,15117,15205,15287"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c96b0d8b72c615a79b6ffc473f6a8013\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-es\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "155", "endOffsets": "350"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5578", "endColumns": "159", "endOffsets": "5733"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\01e1e5e969c3bf2e9c6e929b66b5ab2c\\transformed\\browser-1.8.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,263,378", "endColumns": "106,100,114,104", "endOffsets": "157,258,373,478"}, "to": {"startLines": "67,72,73,74", "startColumns": "4,4,4,4", "startOffsets": "6949,7374,7475,7590", "endColumns": "106,100,114,104", "endOffsets": "7051,7470,7585,7690"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1d0e40e3188866457cee9d0d9de29220\\transformed\\jetified-play-services-ads-23.5.0\\res\\values-es\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,242,289,362,428,497,605,668,795,902,1022,1077,1136,1266,1362,1404,1501,1536,1572,1626,1708,1753", "endColumns": "42,46,72,65,68,107,62,126,106,119,54,58,129,95,41,96,34,35,53,81,44,55", "endOffsets": "241,288,361,427,496,604,667,794,901,1021,1076,1135,1265,1361,1403,1500,1535,1571,1625,1707,1752,1808"}, "to": {"startLines": "135,136,137,139,140,141,142,143,144,145,146,147,148,149,152,153,154,155,156,157,158,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12584,12631,12682,12840,12910,12983,13095,13162,13293,13404,13528,13587,13650,13784,14114,14160,14261,14300,14340,14398,14484,15292", "endColumns": "46,50,76,69,72,111,66,130,110,123,58,62,133,99,45,100,38,39,57,85,48,59", "endOffsets": "12626,12677,12754,12905,12978,13090,13157,13288,13399,13523,13582,13645,13779,13879,14155,14256,14295,14335,14393,14479,14528,15347"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2d58a419118e34eadf0fdff30391c321\\transformed\\core-1.16.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "38,39,40,41,42,43,44,163", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3503,3602,3704,3804,3902,4009,4115,14852", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "3597,3699,3799,3897,4004,4110,4230,14948"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0b04ae20513d4bbf33fbc1e5158096c1\\transformed\\material-1.12.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,278,359,438,525,626,722,826,948,1029,1091,1156,1251,1332,1395,1484,1548,1617,1680,1754,1818,1875,1993,2051,2113,2170,2250,2389,2478,2554,2649,2730,2812,2953,3034,3114,3265,3355,3435,3491,3547,3613,3692,3774,3845,3934,4008,4085,4155,4234,4334,4418,4502,4594,4694,4768,4849,4951,5004,5089,5156,5249,5338,5400,5464,5527,5595,5708,5815,5919,6020,6080,6140,6223,6306,6382", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,78,86,100,95,103,121,80,61,64,94,80,62,88,63,68,62,73,63,56,117,57,61,56,79,138,88,75,94,80,81,140,80,79,150,89,79,55,55,65,78,81,70,88,73,76,69,78,99,83,83,91,99,73,80,101,52,84,66,92,88,61,63,62,67,112,106,103,100,59,59,82,82,75,76", "endOffsets": "273,354,433,520,621,717,821,943,1024,1086,1151,1246,1327,1390,1479,1543,1612,1675,1749,1813,1870,1988,2046,2108,2165,2245,2384,2473,2549,2644,2725,2807,2948,3029,3109,3260,3350,3430,3486,3542,3608,3687,3769,3840,3929,4003,4080,4150,4229,4329,4413,4497,4589,4689,4763,4844,4946,4999,5084,5151,5244,5333,5395,5459,5522,5590,5703,5810,5914,6015,6075,6135,6218,6301,6377,6454"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,68,69,71,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,150,160,161,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3059,3140,3219,3306,3407,4235,4339,4461,7056,7118,7279,7695,7776,7839,7928,7992,8061,8124,8198,8262,8319,8437,8495,8557,8614,8694,8833,8922,8998,9093,9174,9256,9397,9478,9558,9709,9799,9879,9935,9991,10057,10136,10218,10289,10378,10452,10529,10599,10678,10778,10862,10946,11038,11138,11212,11293,11395,11448,11533,11600,11693,11782,11844,11908,11971,12039,12152,12259,12363,12464,12524,13884,14616,14699,14775", "endLines": "5,33,34,35,36,37,45,46,47,68,69,71,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,150,160,161,162", "endColumns": "12,80,78,86,100,95,103,121,80,61,64,94,80,62,88,63,68,62,73,63,56,117,57,61,56,79,138,88,75,94,80,81,140,80,79,150,89,79,55,55,65,78,81,70,88,73,76,69,78,99,83,83,91,99,73,80,101,52,84,66,92,88,61,63,62,67,112,106,103,100,59,59,82,82,75,76", "endOffsets": "323,3135,3214,3301,3402,3498,4334,4456,4537,7113,7178,7369,7771,7834,7923,7987,8056,8119,8193,8257,8314,8432,8490,8552,8609,8689,8828,8917,8993,9088,9169,9251,9392,9473,9553,9704,9794,9874,9930,9986,10052,10131,10213,10284,10373,10447,10524,10594,10673,10773,10857,10941,11033,11133,11207,11288,11390,11443,11528,11595,11688,11777,11839,11903,11966,12034,12147,12254,12358,12459,12519,12579,13962,14694,14770,14847"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\64d1cc8f3e9c59c96e0297d996aad4c8\\transformed\\jetified-play-services-base-18.5.0\\res\\values-es\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,456,583,687,844,973,1091,1197,1385,1490,1651,1779,1940,2093,2156,2221", "endColumns": "103,158,126,103,156,128,117,105,187,104,160,127,160,152,62,64,80", "endOffsets": "296,455,582,686,843,972,1090,1196,1384,1489,1650,1778,1939,2092,2155,2220,2301"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4542,4650,4813,4944,5052,5213,5346,5468,5738,5930,6039,6204,6336,6501,6658,6725,6794", "endColumns": "107,162,130,107,160,132,121,109,191,108,164,131,164,156,66,68,84", "endOffsets": "4645,4808,4939,5047,5208,5341,5463,5573,5925,6034,6199,6331,6496,6653,6720,6789,6874"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9197c9cddad2e7ee60ceb2271f7affe0\\transformed\\appcompat-1.7.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,320,428,513,614,742,828,909,1001,1095,1192,1286,1386,1480,1576,1672,1764,1856,1938,2045,2156,2255,2363,2471,2578,2737,2836", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "202,315,423,508,609,737,823,904,996,1090,1187,1281,1381,1475,1571,1667,1759,1851,1933,2040,2151,2250,2358,2466,2573,2732,2831,2914"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "328,430,543,651,736,837,965,1051,1132,1224,1318,1415,1509,1609,1703,1799,1895,1987,2079,2161,2268,2379,2478,2586,2694,2801,2960,14533", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "425,538,646,731,832,960,1046,1127,1219,1313,1410,1504,1604,1698,1794,1890,1982,2074,2156,2263,2374,2473,2581,2689,2796,2955,3054,14611"}}]}]}