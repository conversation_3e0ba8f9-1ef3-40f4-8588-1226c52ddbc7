<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Disabled state -->
    <item android:state_enabled="false">
        <shape android:shape="rectangle">
            <solid android:color="@color/gray" />
            <corners android:radius="28dp" />
        </shape>
    </item>
    
    <!-- Pressed state -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/insta_deep_purple" />
            <corners android:radius="28dp" />
        </shape>
    </item>
    
    <!-- Normal enabled state -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/insta_purple" />
            <corners android:radius="28dp" />
        </shape>
    </item>
    
</selector>
