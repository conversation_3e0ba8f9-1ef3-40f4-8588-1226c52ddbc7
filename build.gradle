// Top-level build file where you can add configuration options common to all sub-projects/modules.
plugins {
    id 'com.android.application' version '8.10.1' apply false
    id 'com.android.library' version '8.10.1' apply false
    id 'org.jetbrains.kotlin.android' version '1.9.10' apply false
    id 'com.chaquo.python' version '16.1.0' apply false
    id 'com.google.gms.google-services' version '4.4.2' apply false
    id 'com.google.firebase.crashlytics' version '3.0.2' apply false
    id 'com.google.firebase.firebase-perf' version '1.4.2' apply false
}

allprojects {
    configurations.all {
        resolutionStrategy {
            force 'org.jetbrains.kotlin:kotlin-stdlib:1.9.10'
            force 'org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10'
            force 'org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10'
            force 'org.jetbrains.kotlin:kotlin-stdlib-common:1.9.10'
        }
    }
}