import instaloader
import os
from datetime import datetime

def download(profile, progress_callback=None):
    try:
        print(f"Starting profile download for: {profile}")

        profile = profile.strip()
        user = instaloader.Instaloader()
        user.save_metadata = False
        user.post_metadata_txt_pattern = ""

        # Create timestamp for filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Set base directory to SaveIt
        app_dir = os.environ.get('APP_FILES_DIR','/sdcard/Android/data/com.app.videofbdownloadfree/files')
        save_dir = os.path.join(app_dir,"SaveIt")

        # Create folder if not exist
        os.makedirs(save_dir, exist_ok=True)

        # Set Folder save
        user.dirname_pattern = save_dir

        print(f"Getting profile: {profile}")
        profile_obj = instaloader.Profile.from_username(user.context, profile)
        posts = list(profile_obj.get_posts())
        total = len(posts)

        print(f"Found {total} posts to download")

        for i, post in enumerate(posts, 1):
            print(f"Downloading post {i}/{total}")
            # Download to base SaveIt folder, files will be organized by type
            user.download_post(post, target="")
            if progress_callback:
                progress = int(i / total * 100)
                progress_callback(progress)

        print(f"Organizing files...")
        # Organize files after all downloads complete
        organize_downloaded_files(timestamp, "profile")

        print(f"Profile download completed successfully for: {profile}")

    except Exception as e:
        print(f"Error in download: {e}")
        raise e

def download_post_from_link(shortcode, content_type="posts", progress_callback=None):
    try:
        print(f"Starting download for shortcode: {shortcode}, content_type: {content_type}")

        L = instaloader.Instaloader()
        L.save_metadata = False
        L.download_video_thumbnails = False
        L.post_metadata_txt_pattern = ""

        # Create timestamp for filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Create Videos and Images folders
        app_dir = os.environ.get("APP_FILES_DIR","/sdcard/Android/data/com.app.videofbdownloadfree/files")
        save_dir = os.path.join(app_dir,"SaveIt")
        videos_dir = os.path.join(save_dir,"Videos")
        images_dir = os.path.join(save_dir,"Images")

        for directory in [save_dir, videos_dir, images_dir]:
            os.makedirs(directory, exist_ok=True)

        # Set base directory to SaveIt
        L.dirname_pattern = save_dir

        print(f"Getting post from shortcode: {shortcode}")
        post = instaloader.Post.from_shortcode(L.context, shortcode)

        print(f"Downloading post...")
        # Download the post
        L.download_post(post, target="")

        print(f"Organizing files...")
        # Move files to appropriate folders with timestamp names
        organize_downloaded_files(timestamp, content_type)

        if progress_callback:
            progress_callback(100)

        print(f"Download completed successfully for shortcode: {shortcode}")

    except Exception as e:
        print(f"Error in download_post_from_link: {e}")
        raise e

def organize_downloaded_files(timestamp, content_type):
    """Organize downloaded files into Videos and Images folders with timestamp names"""
    import shutil
    import glob

    app_dir = os.environ.get('APP_FILES_DIR','/sdcard/Android/data/com.app.videofbdownloadfree/files')
    base_dir = os.path.join(app_dir,"SaveIt")
    videos_dir = os.path.join(base_dir,"Videos")
    images_dir = os.path.join(base_dir,"Images")

    # File extensions
    video_extensions = ['.mp4', '.mov', '.avi', '.mkv']
    image_extensions = ['.jpg', '.jpeg', '.png', '.webp', '.gif']

    # Find all files in base directory (excluding subdirectories)
    all_files = []
    for file_path in glob.glob(f"{base_dir}/*"):
        if os.path.isfile(file_path):
            all_files.append(file_path)

    file_counter = 1

    for file_path in all_files:
        file_name = os.path.basename(file_path)
        file_ext = os.path.splitext(file_name)[1].lower()

        try:
            if file_ext in video_extensions:
                # Move to Videos folder with timestamp name
                new_name = f"{timestamp}_video_{file_counter:02d}{file_ext}"
                new_path = os.path.join(videos_dir, new_name)
                shutil.move(file_path, new_path)
                print(f"Moved video: {file_name} -> {new_name}")
                file_counter += 1

            elif file_ext in image_extensions:
                # Move to Images folder with timestamp name
                new_name = f"{timestamp}_image_{file_counter:02d}{file_ext}"
                new_path = os.path.join(images_dir, new_name)
                shutil.move(file_path, new_path)
                print(f"Moved image: {file_name} -> {new_name}")
                file_counter += 1

        except Exception as e:
            print(f"Error moving file {file_name}: {e}")

    # Clean up any empty folders
    try:
        for item in os.listdir(base_dir):
            item_path = os.path.join(base_dir, item)
            if os.path.isdir(item_path) and item not in ['Videos', 'Images']:
                # Check if folder is empty
                if not os.listdir(item_path):
                    os.rmdir(item_path)
                    print(f"Removed empty folder: {item}")
    except Exception as e:
        print(f"Error cleaning up folders: {e}")
def set_app_dir(dir_path):
    """Set the APP_FILES_DIR environment variable"""
    os.environ["APP_FILES_DIR"] = dir_path
    return True
def post_count(profile):
    try:
        print(f"Getting post count for profile: {profile}")

        profile = profile.strip()
        user = instaloader.Instaloader()
        profile_obj = instaloader.Profile.from_username(user.context, profile)
        count = len(list(profile_obj.get_posts()))

        print(f"Profile {profile} has {count} posts")
        return count

    except Exception as e:
        print(f"Error in post_count: {e}")
        raise e

