<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="com.facebook.android:audience-network-sdk:6.15.0" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\assets"><file name="audience_network.dex" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d457c9a3d578de247f52c7f5d7be24e6\transformed\jetified-audience-network-sdk-6.15.0\assets\audience_network.dex"/></source></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\assets"><file name="loader_bar.json" path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\main\assets\loader_bar.json"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\SaveIt google play\app\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>