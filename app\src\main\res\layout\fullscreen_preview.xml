<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/media_player_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <!-- Banner Ad في الأعلى -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/bannerAdContainer"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:cardCornerRadius="0dp"
        app:cardElevation="4dp"
        android:visibility="visible"
        app:cardBackgroundColor="@android:color/transparent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <com.google.android.gms.ads.AdView
            android:id="@+id/bannerAdView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            app:adSize="BANNER" />
    </com.google.android.material.card.MaterialCardView>

    <!-- الحاوية الرئيسية التي تضم الفيديو والعناصر الطافية -->
    <FrameLayout
        android:id="@+id/mediaContainer"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/bannerAdContainer"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="8dp">

        <!-- الفيديو خلف كل العناصر -->
        <TextureView
            android:id="@+id/fullscreenVideo"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone" />

        <!-- الصورة fullscreen (بديل الفيديو) -->
        <ImageView
            android:id="@+id/fullscreenImage"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:adjustViewBounds="true"
            android:scaleType="fitCenter"
            android:visibility="gone" />

        <!-- العناصر الطافية فوق الفيديو -->

        <!-- شريط علوي طافي (معلومات الملف وزر الإغلاق) -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/topOverlay"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:background="#66000000"
            android:paddingStart="16dp"
            android:paddingEnd="16dp">

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/fileInfoCard"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                app:cardCornerRadius="20dp"
                android:backgroundTint="@android:color/transparent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent">

                <TextView
                    android:id="@+id/fileInfoText"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="File (1/1)"
                    android:fontFamily="@font/poppins_bold"
                    android:textColor="@android:color/white"
                    android:textSize="16sp"
                    android:gravity="center_vertical"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp" />
            </com.google.android.material.card.MaterialCardView>

            <ImageButton
                android:id="@+id/btnCloseFullscreen"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="@drawable/rounded_button"
                android:src="@drawable/close"
                android:contentDescription="Close"
                app:tint="@android:color/white"
                android:padding="8dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- شريط التحكم السفلي (أزرار التشغيل) -->
        <LinearLayout
            android:id="@+id/centerControlsContainer"
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:orientation="vertical"
            android:padding="16dp"
            android:background="#66000000"
            android:layout_gravity="bottom"
            android:gravity="center">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center"
                android:layout_marginBottom="12dp">

                <ImageButton
                    android:id="@+id/btnPrevious"
                    android:layout_width="35dp"
                    android:layout_height="35dp"
                    android:layout_marginEnd="24dp"
                    android:background="@drawable/rounded_button"
                    android:src="@drawable/previous"
                    android:contentDescription="Previous"
                    app:tint="@android:color/white" />

                <ImageButton
                    android:id="@+id/btnPlayPause"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:background="@drawable/rounded_button"
                    android:src="@android:drawable/ic_media_play"
                    android:contentDescription="Play/Pause"
                    app:tint="@android:color/white" />

                <ImageButton
                    android:id="@+id/btnNext"
                    android:layout_width="35dp"
                    android:layout_height="35dp"
                    android:layout_marginStart="24dp"
                    android:background="@drawable/rounded_button"
                    android:src="@drawable/skip_next"
                    android:contentDescription="Next"
                    app:tint="@android:color/white" />
            </LinearLayout>

        </LinearLayout>

    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
