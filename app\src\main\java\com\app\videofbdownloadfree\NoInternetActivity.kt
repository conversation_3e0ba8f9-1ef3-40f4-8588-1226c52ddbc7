package com.app.videofbdownloadfree

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.widget.Button
import androidx.appcompat.app.AppCompatActivity

/**
 * Activity that blocks app usage when no internet
 */
class NoInternetActivity : BaseActivity() {
    
    private lateinit var networkManager: NetworkManager
    private lateinit var btnRetry: Button
    private val handler = Handler(Looper.getMainLooper())
    private var checkRunnable: Runnable? = null
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_no_internet)
        
        // Initialize NetworkManager
        networkManager = NetworkManager.getInstance(this)
        
        // Initialize views
        btnRetry = findViewById(R.id.btnRetry)
        
        // Setup retry button
        btnRetry.setOnClickListener {
            checkInternetAndProceed()
        }

        // Start continuous monitoring
        startContinuousMonitoring()
    }
    
    private fun startContinuousMonitoring() {
        // Use LiveData for real-time monitoring
        networkManager.isConnected.observe(this) { isConnected ->
            if (isConnected) {
                // Internet is back, go to MainActivity immediately
                goToMainActivity()
            }
        }

        // Also keep periodic check as backup
        startPeriodicCheck()
    }

    private fun startPeriodicCheck() {
        checkRunnable = object : Runnable {
            override fun run() {
                if (networkManager.isNetworkAvailable()) {
                    // Internet is back, go to MainActivity
                    goToMainActivity()
                } else {
                    // Check again after 3 seconds
                    handler.postDelayed(this, 3000)
                }
            }
        }
        handler.post(checkRunnable!!)
    }
    
    private fun checkInternetAndProceed() {
        if (networkManager.isNetworkAvailable()) {
            goToMainActivity()
        } else {
            // Show brief message
            android.widget.Toast.makeText(this, "No internet connection", android.widget.Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun goToMainActivity() {
        val intent = Intent(this, MainActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish()
    }
    
    override fun onDestroy() {
        super.onDestroy()
        checkRunnable?.let { handler.removeCallbacks(it) }
    }
    
    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        // Prevent going back, user must have internet to use the app
        android.widget.Toast.makeText(this, "Internet connection required", android.widget.Toast.LENGTH_LONG).show()
    }
}
