package com.app.videofbdownloadfree

import android.content.Context
import android.graphics.SurfaceTexture
import android.media.MediaPlayer
import android.net.Uri
import android.util.Log
import android.view.Surface
import android.view.TextureView

class VideoPlayerManager(private val context: Context) {

    private var mediaPlayer: MediaPlayer? = null
    private var surface: Surface? = null
    private var textureView: TextureView? = null

    fun setTextureView(textureView: TextureView) {
        this.textureView = textureView
        // استمع لحدث تهيئة SurfaceTexture
        if (textureView.isAvailable) {
            textureView.surfaceTexture?.let { setupSurface(it) }
        } else {
            textureView.surfaceTextureListener = object : TextureView.SurfaceTextureListener {
                override fun onSurfaceTextureAvailable(surfaceTexture: SurfaceTexture, width: Int, height: Int) {
                    setupSurface(surfaceTexture)
                }

                override fun onSurfaceTextureSizeChanged(surfaceTexture: SurfaceTexture, width: Int, height: Int) {}
                override fun onSurfaceTextureDestroyed(surfaceTexture: SurfaceTexture): Boolean {
                    release()
                    return true
                }
                override fun onSurfaceTextureUpdated(surfaceTexture: SurfaceTexture) {}
            }
        }
    }

    private fun setupSurface(surfaceTexture: SurfaceTexture) {
        Log.d("VideoPlayerManager", "Setting up surface")
        surface?.release()
        surface = Surface(surfaceTexture)

    }

    fun playVideo(videoUri: Uri) {
        Log.d("VideoPlayerManager", "Trying to play video: $videoUri")

        if (surface == null) return

        release()

        mediaPlayer = MediaPlayer().apply {
            setSurface(surface)
            setDataSource(context, videoUri)
            setOnPreparedListener {
                start()
            }
            setOnCompletionListener {
            }
            prepareAsync()
        }
    }

    fun pause() {
        mediaPlayer?.pause()
    }

    fun resume() {
        mediaPlayer?.start()
    }

    fun stop() {
        mediaPlayer?.stop()
        release()
    }

    fun isPlaying(): Boolean = mediaPlayer?.isPlaying == true

    fun release() {
        mediaPlayer?.release()
        mediaPlayer = null
    }
}
