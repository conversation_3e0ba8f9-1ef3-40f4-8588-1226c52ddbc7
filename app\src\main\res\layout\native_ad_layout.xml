<?xml version="1.0" encoding="utf-8"?>
<com.google.android.gms.ads.nativead.NativeAdView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp">

    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        android:layout_margin="4dp"
        app:cardBackgroundColor="@android:color/white">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Header with icon and headline -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="12dp">

                <ImageView
                    android:id="@+id/ad_app_icon"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_marginEnd="12dp"
                    android:scaleType="centerCrop"
                    android:background="@drawable/rounded_corner_background" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/ad_headline"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@android:color/black"
                        android:maxLines="2"
                        android:ellipsize="end" />

                    <TextView
                        android:id="@+id/ad_advertiser"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="12sp"
                        android:textColor="#000"
                        android:maxLines="1"
                        android:ellipsize="end" />

                </LinearLayout>

                <!-- Ad badge -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Ad"
                    android:textSize="10sp"
                    android:textColor="@android:color/white"
                    android:background="@drawable/ad_badge_background"
                    android:paddingLeft="6dp"
                    android:paddingRight="6dp"
                    android:paddingTop="2dp"
                    android:paddingBottom="2dp" />

            </LinearLayout>

            <!-- Media content -->
            <com.google.android.gms.ads.nativead.MediaView
                android:id="@+id/ad_media"
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:layout_marginBottom="12dp"
                android:background="@drawable/rounded_corner_background" />

            <!-- Body text -->
            <TextView
                android:id="@+id/ad_body"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:textColor="@android:color/black"
                android:maxLines="3"
                android:ellipsize="end"
                android:layout_marginBottom="12dp" />

            <!-- Star rating and price -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="12dp">

                <RatingBar
                    android:id="@+id/ad_stars"
                    style="?android:attr/ratingBarStyleSmall"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:isIndicator="true"
                    android:numStars="5"
                    android:stepSize="0.5" />

                <TextView
                    android:id="@+id/ad_price"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="@android:color/holo_blue_dark"
                    android:gravity="end"
                    android:maxLines="1" />

            </LinearLayout>

            <!-- Store and Call to Action -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/ad_store"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textSize="12sp"
                    android:textColor="#000"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/ad_call_to_action"
                    android:layout_width="wrap_content"
                    android:layout_height="36dp"
                    android:textSize="12sp"
                    android:textAllCaps="false"
                    android:minWidth="80dp"
                    style="@style/Widget.MaterialComponents.Button" />

            </LinearLayout>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

</com.google.android.gms.ads.nativead.NativeAdView>
