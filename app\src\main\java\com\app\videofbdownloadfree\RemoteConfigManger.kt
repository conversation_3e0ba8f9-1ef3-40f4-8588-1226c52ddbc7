package com.app.videofbdownloadfree

import android.util.Log
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.firebase.ktx.Firebase
import com.google.firebase.remoteconfig.ktx.remoteConfig
import com.google.firebase.remoteconfig.ktx.remoteConfigSettings
import kotlinx.coroutines.tasks.await
object RemoteConfigManager {

    private val remoteConfig = Firebase.remoteConfig

    suspend fun init(): Boolean {
        val configSettings = remoteConfigSettings {
            minimumFetchIntervalInSeconds = 0
        }

        remoteConfig.setConfigSettingsAsync(configSettings)

        remoteConfig.setDefaultsAsync(
            mapOf(
                "banner_ad_unit_id" to "ca-app-pub-1773354726031886/4647951701",
                "banner_player" to "ca-app-pub-1773354726031886/7787376089",
                "interstitial_ad_unit_id" to "ca-app-pub-1773354726031886/3143298346",
                "admob_native_id" to "ca-app-pub-1773354726031886/5976511090",
                "admob_app_open_id" to "ca-app-pub-1773354726031886/6159595494",
                "admob_rewarded_id" to "ca-app-pub-1773354726031886/8442784533",
                "admob_app_id" to "ca-app-pub-1773354726031886~4208237154",
                "banner_recent_big" to "ca-app-pub-1773354726031886/7903744398",
                "facebook_bidding_enabled" to true
            )
        )

        return try {
            remoteConfig.fetchAndActivate().await()

            AdConfig.bannerAdUnitId = remoteConfig.getString("banner_ad_unit_id")
                .ifBlank { "ca-app-pub-1773354726031886/4647951701" }

            AdConfig.bannerPlayerAdUnitId = remoteConfig.getString("banner_player")
                .ifBlank { "ca-app-pub-1773354726031886/7787376089" }

            AdConfig.bannerRecentBig = remoteConfig.getString("banner_recent_big")
                .ifBlank { "ca-app-pub-1773354726031886/7903744398" }
            AdConfig.interstitialAdUnitId = remoteConfig.getString("interstitial_ad_unit_id")
                .ifBlank { "ca-app-pub-1773354726031886/3143298346" }

            AdConfig.nativeAdUnitId = remoteConfig.getString("admob_native_id")
                .ifBlank { "ca-app-pub-1773354726031886/5976511090" }

            AdConfig.openAppAdUnitId = remoteConfig.getString("admob_app_open_id")
                .ifBlank { "ca-app-pub-1773354726031886/6159595494" }

            AdConfig.rewardedAdUnitId = remoteConfig.getString("admob_rewarded_id")
                .ifBlank { "ca-app-pub-1773354726031886/8442784533" }

            AdConfig.admobId = remoteConfig.getString("admob_app_id")
                .ifBlank { "ca-app-pub-1773354726031886~4208237154" }

            // Facebook Audience Network settings
            AdConfig.facebookBiddingEnabled = remoteConfig.getBoolean("facebook_bidding_enabled")

            Log.d("RemoteConfig", "Config fetched successfully with Facebook settings.")
            Log.d("RemoteConfig", "Banner Player Ad Unit ID: ${AdConfig.bannerPlayerAdUnitId}")
            Log.d("RemoteConfig", "Banner Ad Unit ID: ${AdConfig.bannerAdUnitId}")
            Log.d("RemoteConfig", "Facebook Bidding Enabled: ${AdConfig.facebookBiddingEnabled}")
            true
        } catch (e: Exception) {
            Log.e("RemoteConfig", "Error fetching config", e)
    FirebaseCrashlytics.getInstance().recordException(Exception("Error get Unit ad from firebase ${e}"))

            false
        }
    }
}

