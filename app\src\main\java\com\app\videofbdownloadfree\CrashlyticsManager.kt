package com.app.videofbdownloadfree

import android.content.Context
import android.content.pm.PackageManager
import android.util.Log
import com.google.firebase.crashlytics.FirebaseCrashlytics

/**
 * Manager for Firebase Crashlytics
 */
object CrashlyticsManager {

    private const val TAG = "CrashlyticsManager"
    private val crashlytics = FirebaseCrashlytics.getInstance()

    /**
     * Initialize Crashlytics
     */
    fun initialize(context: Context? = null) {
        try {
            // Enable Crashlytics data collection
            crashlytics.setCrashlyticsCollectionEnabled(true)

            // Set user identifier (optional)
            crashlytics.setUserId("user_${System.currentTimeMillis()}")

            // Set custom keys for debugging
            context?.let { ctx ->
                try {
                    val packageInfo = ctx.packageManager.getPackageInfo(ctx.packageName, 0)
                    crashlytics.setCustomKey("app_version", packageInfo.versionName ?: "1.0")
                    crashlytics.setCustomKey("app_version_code", packageInfo.versionCode)
                } catch (e: PackageManager.NameNotFoundException) {
                    crashlytics.setCustomKey("app_version", "1.0")
                    crashlytics.setCustomKey("app_version_code", 1)
                }
            } ?: run {
                crashlytics.setCustomKey("app_version", "1.0")
                crashlytics.setCustomKey("app_version_code", 1)
            }
            
            Log.d(TAG, "Crashlytics initialized successfully")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize Crashlytics: ${e.message}")
        }
    }
    
    /**
     * Log a non-fatal exception
     */
    fun logException(exception: Throwable, message: String? = null) {
        try {
            message?.let {
                crashlytics.log(it)
            }
            crashlytics.recordException(exception)
            Log.w(TAG, "Exception logged to Crashlytics: ${exception.message}")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to log exception to Crashlytics: ${e.message}")
        }
    }
    
    /**
     * Log a custom message
     */
    fun log(message: String) {
        try {
            crashlytics.log(message)
            Log.d(TAG, "Message logged to Crashlytics: $message")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to log message to Crashlytics: ${e.message}")
        }
    }
    
    /**
     * Set custom key-value pair
     */
    fun setCustomKey(key: String, value: String) {
        try {
            crashlytics.setCustomKey(key, value)
            Log.d(TAG, "Custom key set: $key = $value")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to set custom key: ${e.message}")
        }
    }
    
    /**
     * Set custom key-value pair (boolean)
     */
    fun setCustomKey(key: String, value: Boolean) {
        try {
            crashlytics.setCustomKey(key, value)
            Log.d(TAG, "Custom key set: $key = $value")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to set custom key: ${e.message}")
        }
    }
    
    /**
     * Set custom key-value pair (int)
     */
    fun setCustomKey(key: String, value: Int) {
        try {
            crashlytics.setCustomKey(key, value)
            Log.d(TAG, "Custom key set: $key = $value")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to set custom key: ${e.message}")
        }
    }
    
    /**
     * Set user identifier
     */
    fun setUserId(userId: String) {
        try {
            crashlytics.setUserId(userId)
            Log.d(TAG, "User ID set: $userId")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to set user ID: ${e.message}")
        }
    }
    
    /**
     * Force a crash for testing (DEBUG only)
     */
    fun forceCrash() {
        Log.w(TAG, "Forcing crash for testing...")
        throw RuntimeException("Test crash from CrashlyticsManager")
    }
    
    /**
     * Log download event
     */
    fun logDownloadEvent(url: String, success: Boolean) {
        try {
            val message = "Download ${if (success) "successful" else "failed"}: $url"
            log(message)
            setCustomKey("last_download_url", url)
            setCustomKey("last_download_success", success)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to log download event: ${e.message}")
        }
    }
    
    /**
     * Log ad event
     */
    fun logAdEvent(adType: String, event: String, success: Boolean) {
        try {
            val message = "Ad $adType $event: ${if (success) "success" else "failed"}"
            log(message)
            setCustomKey("last_ad_type", adType)
            setCustomKey("last_ad_event", event)
            setCustomKey("last_ad_success", success)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to log ad event: ${e.message}")
        }
    }
    
    /**
     * Log network event
     */
    fun logNetworkEvent(connected: Boolean) {
        try {
            val message = "Network ${if (connected) "connected" else "disconnected"}"
            log(message)
            setCustomKey("network_connected", connected)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to log network event: ${e.message}")
        }
    }
}
