plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'com.chaquo.python'
    id 'com.google.gms.google-services'
    id 'com.google.firebase.crashlytics'
    id 'com.google.firebase.firebase-perf'
}

android {
    namespace 'com.app.videofbdownloadfree'
    compileSdk 35

    defaultConfig {
        applicationId "com.app.videofbdownloadfree"
        minSdk 24
        targetSdk 35
        versionCode 19
        versionName "4.5.1"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        // NDK settings for native code

        ndk {
            abiFilters "armeabi-v7a", "arm64-v8a", "x86", "x86_64"
        }

        // Support for modern Android versions
        multiDexEnabled true
        chaquopy {
            python {
                buildPython "C:/Users/<USER>/AppData/Local/Programs/Python/Python38/python.exe"
                version "3.8"
                pip {
                    // A requirement specifier, with or without a version number:
                    install "instaloader"
                }
            }
        }

    }
    signingConfigs {
        release {
            storeFile file("C:/Users/<USER>/Downloads/keystore.jks")
            storePassword "123456aze"
            keyAlias "mouad"
            keyPassword "123456aze"
        }
    }
    buildTypes {
        release {
            // Enable code shrinking, obfuscation, and optimization
            minifyEnabled true
            // ProGuard files: default and your custom rules
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'

            // Enable debug symbols for native libraries
            ndk {
                debugSymbolLevel 'FULL'
            }
            signingConfig signingConfigs.release
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = "17"
        apiVersion = "1.9"
        languageVersion = "1.9"
    }

    lint {
        checkReleaseBuilds false
        abortOnError false
    }

    buildFeatures {
        buildConfig true
    }
    sourceSets {
        main {
            python.srcDir "src/main/python"
        }
    }
}

configurations.all {
    resolutionStrategy {
        force 'org.jetbrains.kotlin:kotlin-stdlib:1.9.10'
        force 'org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10'
        force 'org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10'
        force 'org.jetbrains.kotlin:kotlin-stdlib-common:1.9.10'

        // Force compatible versions for Chaquo
        eachDependency { details ->
            if (details.requested.group == 'org.jetbrains.kotlin') {
                details.useVersion '1.9.10'
            }
            // Force compatible Google Play Services versions
            if (details.requested.group == 'com.google.android.gms') {
                if (details.requested.name.startsWith('play-services-measurement')) {
                    details.useVersion '22.1.2'
                }
            }
        }
    }
}

dependencies {

    implementation 'androidx.core:core-ktx:1.16.0'
    implementation 'org.jetbrains.kotlin:kotlin-stdlib:1.9.10'
    implementation 'androidx.appcompat:appcompat:1.7.1'
    implementation 'androidx.multidex:multidex:2.0.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.2.0'
    implementation 'androidx.preference:preference-ktx:1.2.1'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.2.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.1'
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:1.9.0"
    implementation 'com.google.android.material:material:1.12.0'
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    implementation 'androidx.recyclerview:recyclerview:1.3.2'
    implementation("com.google.android.gms:play-services-ads:23.5.0")

    // Facebook Audience Network SDK for Bidding
    implementation 'com.facebook.android:audience-network-sdk:6.15.0'

    // AndroidX Lifecycle dependencies for ProcessLifecycleOwner
    implementation 'androidx.lifecycle:lifecycle-process:2.8.7'
    implementation 'androidx.lifecycle:lifecycle-common-java8:2.8.7'

    // Firebase BOM - manages all Firebase library versions
    implementation platform('com.google.firebase:firebase-bom:33.6.0')

    // Firebase Crashlytics and Analytics
    implementation 'com.google.firebase:firebase-crashlytics-ktx'
    implementation 'com.google.firebase:firebase-analytics-ktx'
    implementation 'com.google.firebase:firebase-config:21.6.1'
    implementation 'com.google.firebase:firebase-database-ktx'
    implementation 'com.google.firebase:firebase-perf-ktx'

    // Firebase App Check for security
    implementation 'com.google.firebase:firebase-appcheck-ktx'
    implementation 'com.google.firebase:firebase-appcheck-playintegrity'

    // Firebase Cloud Messaging for push notifications
    implementation 'com.google.firebase:firebase-messaging-ktx'

    // Force compatible versions to avoid manifest conflicts
    implementation 'com.google.android.gms:play-services-measurement-api:22.1.2'
    implementation 'com.airbnb.android:lottie:6.4.0'



}