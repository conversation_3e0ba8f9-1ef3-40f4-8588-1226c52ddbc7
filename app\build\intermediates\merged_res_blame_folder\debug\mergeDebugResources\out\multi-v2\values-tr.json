{"logs": [{"outputFile": "com.app.videofbdownloadfree.app-mergeDebugResources-61:/values-tr/values-tr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2d58a419118e34eadf0fdff30391c321\\transformed\\core-1.16.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "38,39,40,41,42,43,44,163", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3424,3521,3623,3721,3818,3920,4026,14405", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "3516,3618,3716,3813,3915,4021,4132,14501"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\01e1e5e969c3bf2e9c6e929b66b5ab2c\\transformed\\browser-1.8.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,261,368", "endColumns": "99,105,106,105", "endOffsets": "150,256,363,469"}, "to": {"startLines": "67,72,73,74", "startColumns": "4,4,4,4", "startOffsets": "6749,7151,7257,7364", "endColumns": "99,105,106,105", "endOffsets": "6844,7252,7359,7465"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9197c9cddad2e7ee60ceb2271f7affe0\\transformed\\appcompat-1.7.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,2797", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,429,528,640,725,831,951,1031,1106,1197,1290,1382,1476,1576,1669,1771,1866,1957,2048,2127,2234,2338,2434,2541,2644,2753,2909,14092", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "424,523,635,720,826,946,1026,1101,1192,1285,1377,1471,1571,1664,1766,1861,1952,2043,2122,2229,2333,2429,2536,2639,2748,2904,3002,14167"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c96b0d8b72c615a79b6ffc473f6a8013\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-tr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "142", "endOffsets": "337"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5443", "endColumns": "146", "endOffsets": "5585"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\64d1cc8f3e9c59c96e0297d996aad4c8\\transformed\\jetified-play-services-base-18.5.0\\res\\values-tr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,450,582,683,826,952,1075,1177,1345,1448,1601,1731,1872,2035,2093,2153", "endColumns": "105,150,131,100,142,125,122,101,167,102,152,129,140,162,57,59,75", "endOffsets": "298,449,581,682,825,951,1074,1176,1344,1447,1600,1730,1871,2034,2092,2152,2228"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4427,4537,4692,4828,4933,5080,5210,5337,5590,5762,5869,6026,6160,6305,6472,6534,6598", "endColumns": "109,154,135,104,146,129,126,105,171,106,156,133,144,166,61,63,79", "endOffsets": "4532,4687,4823,4928,5075,5205,5332,5438,5757,5864,6021,6155,6300,6467,6529,6593,6673"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0b04ae20513d4bbf33fbc1e5158096c1\\transformed\\material-1.12.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,340,415,492,591,682,778,890,972,1032,1096,1187,1264,1325,1416,1479,1542,1601,1670,1733,1787,1895,1953,2015,2069,2142,2263,2347,2427,2526,2610,2701,2841,2918,2994,3125,3212,3288,3341,3395,3461,3531,3608,3679,3759,3830,3905,3983,4054,4155,4240,4329,4424,4517,4589,4661,4757,4809,4895,4962,5046,5136,5198,5262,5325,5395,5489,5591,5680,5780,5837,5895,5974,6058,6133", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,74,74,76,98,90,95,111,81,59,63,90,76,60,90,62,62,58,68,62,53,107,57,61,53,72,120,83,79,98,83,90,139,76,75,130,86,75,52,53,65,69,76,70,79,70,74,77,70,100,84,88,94,92,71,71,95,51,85,66,83,89,61,63,62,69,93,101,88,99,56,57,78,83,74,73", "endOffsets": "260,335,410,487,586,677,773,885,967,1027,1091,1182,1259,1320,1411,1474,1537,1596,1665,1728,1782,1890,1948,2010,2064,2137,2258,2342,2422,2521,2605,2696,2836,2913,2989,3120,3207,3283,3336,3390,3456,3526,3603,3674,3754,3825,3900,3978,4049,4150,4235,4324,4419,4512,4584,4656,4752,4804,4890,4957,5041,5131,5193,5257,5320,5390,5484,5586,5675,5775,5832,5890,5969,6053,6128,6202"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,68,69,71,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,150,160,161,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3007,3082,3157,3234,3333,4137,4233,4345,6849,6909,7060,7470,7547,7608,7699,7762,7825,7884,7953,8016,8070,8178,8236,8298,8352,8425,8546,8630,8710,8809,8893,8984,9124,9201,9277,9408,9495,9571,9624,9678,9744,9814,9891,9962,10042,10113,10188,10266,10337,10438,10523,10612,10707,10800,10872,10944,11040,11092,11178,11245,11329,11419,11481,11545,11608,11678,11772,11874,11963,12063,12120,13474,14172,14256,14331", "endLines": "5,33,34,35,36,37,45,46,47,68,69,71,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,150,160,161,162", "endColumns": "12,74,74,76,98,90,95,111,81,59,63,90,76,60,90,62,62,58,68,62,53,107,57,61,53,72,120,83,79,98,83,90,139,76,75,130,86,75,52,53,65,69,76,70,79,70,74,77,70,100,84,88,94,92,71,71,95,51,85,66,83,89,61,63,62,69,93,101,88,99,56,57,78,83,74,73", "endOffsets": "310,3077,3152,3229,3328,3419,4228,4340,4422,6904,6968,7146,7542,7603,7694,7757,7820,7879,7948,8011,8065,8173,8231,8293,8347,8420,8541,8625,8705,8804,8888,8979,9119,9196,9272,9403,9490,9566,9619,9673,9739,9809,9886,9957,10037,10108,10183,10261,10332,10433,10518,10607,10702,10795,10867,10939,11035,11087,11173,11240,11324,11414,11476,11540,11603,11673,11767,11869,11958,12058,12115,12173,13548,14251,14326,14400"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\19f3705e3499cc83b2f3aa3cd543b099\\transformed\\preference-1.2.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,263,341,473,642,725", "endColumns": "70,86,77,131,168,82,77", "endOffsets": "171,258,336,468,637,720,798"}, "to": {"startLines": "66,70,138,151,164,165,166", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6678,6973,12336,13553,14506,14675,14758", "endColumns": "70,86,77,131,168,82,77", "endOffsets": "6744,7055,12409,13680,14670,14753,14831"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1d0e40e3188866457cee9d0d9de29220\\transformed\\jetified-play-services-ads-23.5.0\\res\\values-tr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,241,288,345,411,479,591,654,793,907,1042,1095,1148,1269,1361,1401,1492,1528,1562,1614,1700,1740", "endColumns": "41,46,56,65,67,111,62,138,113,134,52,52,120,91,39,90,35,33,51,85,39,55", "endOffsets": "240,287,344,410,478,590,653,792,906,1041,1094,1147,1268,1360,1400,1491,1527,1561,1613,1699,1739,1795"}, "to": {"startLines": "135,136,137,139,140,141,142,143,144,145,146,147,148,149,152,153,154,155,156,157,158,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12178,12224,12275,12414,12484,12556,12672,12739,12882,13000,13139,13196,13253,13378,13685,13729,13824,13864,13902,13958,14048,14836", "endColumns": "45,50,60,69,71,115,66,142,117,138,56,56,124,95,43,94,39,37,55,89,43,59", "endOffsets": "12219,12270,12331,12479,12551,12667,12734,12877,12995,13134,13191,13248,13373,13469,13724,13819,13859,13897,13953,14043,14087,14891"}}]}]}