{"logs": [{"outputFile": "com.app.videofbdownloadfree.app-mergeDebugResources-60:/values-cs/values-cs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1d0e40e3188866457cee9d0d9de29220\\transformed\\jetified-play-services-ads-23.5.0\\res\\values-cs\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,240,287,358,423,492,591,654,763,874,1001,1051,1107,1203,1291,1334,1416,1452,1489,1545,1622,1666", "endColumns": "40,46,70,64,68,98,62,108,110,126,49,55,95,87,42,81,35,36,55,76,43,55", "endOffsets": "239,286,357,422,491,590,653,762,873,1000,1050,1106,1202,1290,1333,1415,1451,1488,1544,1621,1665,1721"}, "to": {"startLines": "137,138,139,141,142,143,144,145,146,147,148,149,150,151,154,155,156,157,158,159,160,169", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12379,12424,12475,12628,12697,12770,12873,12940,13053,13168,13299,13353,13413,13513,13831,13878,13964,14004,14045,14105,14186,14989", "endColumns": "44,50,74,68,72,102,66,112,114,130,53,59,99,91,46,85,39,40,59,80,47,59", "endOffsets": "12419,12470,12545,12692,12765,12868,12935,13048,13163,13294,13348,13408,13508,13600,13873,13959,13999,14040,14100,14181,14229,15044"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9197c9cddad2e7ee60ceb2271f7affe0\\transformed\\appcompat-1.7.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,424,510,615,732,810,886,977,1070,1165,1259,1353,1446,1541,1638,1729,1820,1904,2008,2120,2219,2325,2436,2538,2701,2799", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "207,309,419,505,610,727,805,881,972,1065,1160,1254,1348,1441,1536,1633,1724,1815,1899,2003,2115,2214,2320,2431,2533,2696,2794,2877"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,161", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "418,525,627,737,823,928,1045,1123,1199,1290,1383,1478,1572,1666,1759,1854,1951,2042,2133,2217,2321,2433,2532,2638,2749,2851,3014,14234", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "520,622,732,818,923,1040,1118,1194,1285,1378,1473,1567,1661,1754,1849,1946,2037,2128,2212,2316,2428,2527,2633,2744,2846,3009,3107,14312"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\01e1e5e969c3bf2e9c6e929b66b5ab2c\\transformed\\browser-1.8.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,157,260,374", "endColumns": "101,102,113,100", "endOffsets": "152,255,369,470"}, "to": {"startLines": "69,74,75,76", "startColumns": "4,4,4,4", "startOffsets": "6895,7320,7423,7537", "endColumns": "101,102,113,100", "endOffsets": "6992,7418,7532,7633"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2d58a419118e34eadf0fdff30391c321\\transformed\\core-1.16.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,455,560,667,786", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "148,250,351,450,555,662,781,882"}, "to": {"startLines": "40,41,42,43,44,45,46,165", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3540,3638,3740,3841,3940,4045,4152,14554", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "3633,3735,3836,3935,4040,4147,4266,14650"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0b04ae20513d4bbf33fbc1e5158096c1\\transformed\\material-1.12.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,368,446,524,601,704,796,888,1014,1095,1156,1221,1320,1396,1457,1546,1610,1677,1731,1799,1859,1913,2030,2090,2152,2206,2278,2400,2484,2563,2657,2740,2832,2969,3047,3129,3256,3344,3424,3478,3529,3595,3667,3744,3815,3896,3968,4045,4119,4190,4295,4383,4454,4547,4642,4716,4790,4886,4938,5021,5088,5174,5262,5324,5388,5451,5519,5629,5735,5834,5948,6006,6061,6140,6223,6298", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,77,77,76,102,91,91,125,80,60,64,98,75,60,88,63,66,53,67,59,53,116,59,61,53,71,121,83,78,93,82,91,136,77,81,126,87,79,53,50,65,71,76,70,80,71,76,73,70,104,87,70,92,94,73,73,95,51,82,66,85,87,61,63,62,67,109,105,98,113,57,54,78,82,74,78", "endOffsets": "363,441,519,596,699,791,883,1009,1090,1151,1216,1315,1391,1452,1541,1605,1672,1726,1794,1854,1908,2025,2085,2147,2201,2273,2395,2479,2558,2652,2735,2827,2964,3042,3124,3251,3339,3419,3473,3524,3590,3662,3739,3810,3891,3963,4040,4114,4185,4290,4378,4449,4542,4637,4711,4785,4881,4933,5016,5083,5169,5257,5319,5383,5446,5514,5624,5730,5829,5943,6001,6056,6135,6218,6293,6372"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,70,71,73,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,152,162,163,164", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3112,3190,3268,3345,3448,4271,4363,4489,6997,7058,7221,7638,7714,7775,7864,7928,7995,8049,8117,8177,8231,8348,8408,8470,8524,8596,8718,8802,8881,8975,9058,9150,9287,9365,9447,9574,9662,9742,9796,9847,9913,9985,10062,10133,10214,10286,10363,10437,10508,10613,10701,10772,10865,10960,11034,11108,11204,11256,11339,11406,11492,11580,11642,11706,11769,11837,11947,12053,12152,12266,12324,13605,14317,14400,14475", "endLines": "7,35,36,37,38,39,47,48,49,70,71,73,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,152,162,163,164", "endColumns": "12,77,77,76,102,91,91,125,80,60,64,98,75,60,88,63,66,53,67,59,53,116,59,61,53,71,121,83,78,93,82,91,136,77,81,126,87,79,53,50,65,71,76,70,80,71,76,73,70,104,87,70,92,94,73,73,95,51,82,66,85,87,61,63,62,67,109,105,98,113,57,54,78,82,74,78", "endOffsets": "413,3185,3263,3340,3443,3535,4358,4484,4565,7053,7118,7315,7709,7770,7859,7923,7990,8044,8112,8172,8226,8343,8403,8465,8519,8591,8713,8797,8876,8970,9053,9145,9282,9360,9442,9569,9657,9737,9791,9842,9908,9980,10057,10128,10209,10281,10358,10432,10503,10608,10696,10767,10860,10955,11029,11103,11199,11251,11334,11401,11487,11575,11637,11701,11764,11832,11942,12048,12147,12261,12319,12374,13679,14395,14470,14549"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\64d1cc8f3e9c59c96e0297d996aad4c8\\transformed\\jetified-play-services-base-18.5.0\\res\\values-cs\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,572,678,827,950,1058,1155,1326,1433,1593,1717,1874,2025,2089,2152", "endColumns": "101,155,120,105,148,122,107,96,170,106,159,123,156,150,63,62,81", "endOffsets": "294,450,571,677,826,949,1057,1154,1325,1432,1592,1716,1873,2024,2088,2151,2233"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4570,4676,4836,4961,5071,5224,5351,5463,5707,5882,5993,6157,6285,6446,6601,6669,6736", "endColumns": "105,159,124,109,152,126,111,100,174,110,163,127,160,154,67,66,85", "endOffsets": "4671,4831,4956,5066,5219,5346,5458,5559,5877,5988,6152,6280,6441,6596,6664,6731,6817"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c96b0d8b72c615a79b6ffc473f6a8013\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-cs\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5564", "endColumns": "142", "endOffsets": "5702"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\19f3705e3499cc83b2f3aa3cd543b099\\transformed\\preference-1.2.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,276,354,501,670,754", "endColumns": "72,97,77,146,168,83,80", "endOffsets": "173,271,349,496,665,749,830"}, "to": {"startLines": "68,72,140,153,166,167,168", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6822,7123,12550,13684,14655,14824,14908", "endColumns": "72,97,77,146,168,83,80", "endOffsets": "6890,7216,12623,13826,14819,14903,14984"}}]}]}