package com.app.videofbdownloadfree

import android.app.Activity
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import com.google.android.gms.ads.AdListener
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.AdSize
import com.google.android.gms.ads.AdView
import com.google.android.gms.ads.LoadAdError

/**
 * Enhanced Banner Ad Manager with auto-refresh
 */
class BannerAdManager(private val activity: Activity) {
    
    companion object {
        private const val TAG = "BannerAdManager"
        private val REFRESH_INTERVAL_MS = AdConfig.BANNER_REFRESH_INTERVAL
        private const val MAX_REFRESH_COUNT = 15 // Increased for better monetization
    }
    
    private var adView: AdView? = null
    private var refreshHandler: Handler? = null
    private var refreshRunnable: Runnable? = null
    private var refreshCount = 0
    private var isDestroyed = false
    
    /**
     * Initialize banner ad with auto-refresh
     */
    fun initializeBannerAd(adViewContainer: AdView, adSize: AdSize = AdSize.BANNER) {
        if (!AdConfig.adsEnabled) {
            adViewContainer.visibility = View.GONE
            return
        }

        try {
            adView = adViewContainer
            
            // Configure AdView
            adView?.apply {
                setAdSize(adSize)
                adUnitId = AdConfig.bannerAdUnitId
                
                adListener = object : AdListener() {
                    override fun onAdLoaded() {
                        Log.d(TAG, "Banner ad loaded successfully")
                        visibility = View.VISIBLE
                        
                        // Log analytics
                        AnalyticsManager.logAdEvent("banner", "load", true)
                        CrashlyticsManager.logAdEvent("banner", "load", true)
                        
                        // Schedule next refresh
                        scheduleRefresh()
                    }
                    
                    override fun onAdFailedToLoad(adError: LoadAdError) {
                        Log.e(TAG, "Banner ad failed to load: ${adError.message}")
                        visibility = View.GONE
                        
                        // Log analytics
                        AnalyticsManager.logAdEvent("banner", "load", false)
                        CrashlyticsManager.logAdEvent("banner", "load", false)
                        
                        // Retry after delay
                        scheduleRefresh(30000) // Retry after 30 seconds
                    }
                    
                    override fun onAdClicked() {
                        Log.d(TAG, "Banner ad clicked")
                        AnalyticsManager.logAdEvent("banner", "click", true)
                        CrashlyticsManager.logAdEvent("banner", "click", true)
                    }
                    
                    override fun onAdOpened() {
                        Log.d(TAG, "Banner ad opened")
                        AnalyticsManager.logAdEvent("banner", "open", true)
                    }
                    
                    override fun onAdClosed() {
                        Log.d(TAG, "Banner ad closed")
                        AnalyticsManager.logAdEvent("banner", "close", true)
                    }
                }
            }
            
            // Load initial ad
            loadAd()
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize banner ad: ${e.message}")
            CrashlyticsManager.logException(e, "Banner ad initialization failed")
        }
    }
    
    /**
     * Load banner ad with Facebook Bidding support
     */
    private fun loadAd() {
        try {
            if (isDestroyed) return

            // Configure AdRequest for Facebook Bidding
            val adRequestBuilder = AdRequest.Builder()
            val adRequest = FacebookBiddingHelper.configureAdRequestForBidding(adRequestBuilder).build()
            adView?.loadAd(adRequest)

            Log.d(TAG, "Loading banner ad with Facebook bidding...")

        } catch (e: Exception) {
            Log.e(TAG, "Failed to load banner ad: ${e.message}")
            CrashlyticsManager.logException(e, "Banner ad load failed")
        }
    }
    
    /**
     * Schedule ad refresh
     */
    private fun scheduleRefresh(delayMs: Long = REFRESH_INTERVAL_MS) {
        try {
            if (isDestroyed || refreshCount >= MAX_REFRESH_COUNT) return
            
            // Cancel previous refresh
            cancelRefresh()
            
            refreshHandler = Handler(Looper.getMainLooper())
            refreshRunnable = Runnable {
                if (!isDestroyed && refreshCount < MAX_REFRESH_COUNT) {
                    refreshCount++
                    Log.d(TAG, "Refreshing banner ad (count: $refreshCount)")
                    loadAd()
                }
            }
            
            refreshHandler?.postDelayed(refreshRunnable!!, delayMs)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to schedule refresh: ${e.message}")
        }
    }
    
    /**
     * Cancel scheduled refresh
     */
    private fun cancelRefresh() {
        try {
            refreshRunnable?.let { runnable ->
                refreshHandler?.removeCallbacks(runnable)
            }
            refreshRunnable = null
            refreshHandler = null
        } catch (e: Exception) {
            Log.e(TAG, "Failed to cancel refresh: ${e.message}")
        }
    }
    
    /**
     * Pause ad refresh
     */
    fun pause() {
        try {
            adView?.pause()
            cancelRefresh()
            Log.d(TAG, "Banner ad paused")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to pause banner ad: ${e.message}")
        }
    }
    
    /**
     * Resume ad refresh
     */
    fun resume() {
        try {
            adView?.resume()
            if (!isDestroyed && refreshCount < MAX_REFRESH_COUNT) {
                scheduleRefresh()
            }
            Log.d(TAG, "Banner ad resumed")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to resume banner ad: ${e.message}")
        }
    }
    
    /**
     * Destroy banner ad
     */
    fun destroy() {
        try {
            isDestroyed = true
            cancelRefresh()
            adView?.destroy()
            adView = null
            Log.d(TAG, "Banner ad destroyed")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to destroy banner ad: ${e.message}")
        }
    }
    
    /**
     * Get current refresh count
     */
    fun getRefreshCount(): Int = refreshCount
    
    /**
     * Reset refresh count
     */
    fun resetRefreshCount() {
        refreshCount = 0
        Log.d(TAG, "Refresh count reset")
    }
    
    /**
     * Force refresh ad
     */
    fun forceRefresh() {
        if (!isDestroyed && refreshCount < MAX_REFRESH_COUNT) {
            refreshCount++
            loadAd()
            Log.d(TAG, "Banner ad force refreshed")
        }
    }
}
