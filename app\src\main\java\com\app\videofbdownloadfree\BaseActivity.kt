package com.app.videofbdownloadfree

import android.content.res.Configuration
import android.os.Bundle
import android.util.Log
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat

abstract class BaseActivity : AppCompatActivity() {
    private val TAG = "BaseActivity"

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdgeCompat(this)

    }

    protected fun enableImmersiveMode() {
        try {
            // Allow content to go edge-to-edge
            WindowCompat.setDecorFitsSystemWindows(window, false)

            val controller = WindowInsetsControllerCompat(window, window.decorView)

            // Hide the navigation bars and status bar
            controller.hide(WindowInsetsCompat.Type.systemBars())

            // Allow user to swipe to show bars temporarily
            controller.systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE

            // Optional: use light or dark appearance for icons
            controller.isAppearanceLightStatusBars = true
            controller.isAppearanceLightNavigationBars = true

            // Optional: make status and nav bars transparent (avoid deprecated setStatusBarColor)
            window.statusBarColor = android.graphics.Color.TRANSPARENT
            window.navigationBarColor = android.graphics.Color.TRANSPARENT

        } catch (e: Exception) {
            Log.e(TAG, "Error in Hide Bar : $e")
        }
    }
    fun enableEdgeToEdgeCompat(activity: AppCompatActivity) {
        WindowCompat.setDecorFitsSystemWindows(activity.window, false)
    }
    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)
        if (hasFocus) enableImmersiveMode()
    }

    override fun onResume() {
        super.onResume()
        enableImmersiveMode()
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        enableImmersiveMode()
    }
}
