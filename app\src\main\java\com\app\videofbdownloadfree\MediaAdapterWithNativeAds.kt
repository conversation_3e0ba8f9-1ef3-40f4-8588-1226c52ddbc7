package com.app.videofbdownloadfree

import android.content.Context
import android.net.Uri
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.AdLoader
import com.google.android.gms.ads.nativead.NativeAd
import java.io.File

/**
 * Simplified Media Adapter with Native Ads for RecyclerView
 */
class MediaAdapterWithNativeAds(
    private val context: Context,
    private var mediaFiles: List<File>,
    private val onItemClick: (File) -> Unit
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        private const val TYPE_MEDIA = 0
        private const val TYPE_NATIVE_AD = 1
        private const val AD_FREQUENCY = 6 // Show ad every 6 items
        private const val TAG = "MediaAdapterWithNativeAds"
    }

    private val nativeAdManager = NativeAdManager(context)
    private val nativeAds = mutableMapOf<Int, NativeAd>()

    // Glide options for better performance
    private val glideOptions = RequestOptions()
        .diskCacheStrategy(DiskCacheStrategy.ALL)
        .skipMemoryCache(false)
        .override(200, 200)

    inner class MediaViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val thumbnail: ImageView = itemView.findViewById(R.id.mediaThumbnail)
        val playIcon: ImageView = itemView.findViewById(R.id.playIcon)
        val videoDuration: android.widget.TextView = itemView.findViewById(R.id.videoDuration)
        val profileImage: ImageView = itemView.findViewById(R.id.profileImage)
        val downloadDate: android.widget.TextView = itemView.findViewById(R.id.downloadDate)
        val fileInfo: android.widget.TextView = itemView.findViewById(R.id.fileInfo)
        val actionButton: ImageView = itemView.findViewById(R.id.actionButton)
    }

    inner class NativeAdViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val adContainer: ViewGroup = itemView as ViewGroup
    }

    override fun getItemViewType(position: Int): Int {
        return if (isAdPosition(position)) TYPE_NATIVE_AD else TYPE_MEDIA
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            TYPE_NATIVE_AD -> {
                val adView = LayoutInflater.from(parent.context)
                    .inflate(R.layout.native_ad_small_layout, parent, false)
                NativeAdViewHolder(adView)
            }
            else -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_media, parent, false)
                MediaViewHolder(view)
            }
        }
    }

    override fun getItemCount(): Int {
        // Add extra items for ads
        val adCount = mediaFiles.size / AD_FREQUENCY
        return mediaFiles.size + adCount
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder.itemViewType) {
            TYPE_NATIVE_AD -> {
                val adHolder = holder as NativeAdViewHolder
                loadNativeAd(adHolder, position)
            }
            TYPE_MEDIA -> {
                val mediaHolder = holder as MediaViewHolder
                val mediaPosition = getMediaPosition(position)
                if (mediaPosition < mediaFiles.size) {
                    bindMediaItem(mediaHolder, mediaFiles[mediaPosition])
                }
            }
        }
    }

    private fun bindMediaItem(holder: MediaViewHolder, file: File) {
        // Clear previous image to prevent recycling issues
        holder.thumbnail.setImageDrawable(null)

        // Load thumbnail with Glide
        Glide.with(context)
            .load(Uri.fromFile(file))
            .apply(glideOptions)
            .centerCrop()
            .into(holder.thumbnail)

        // Set download date
        holder.downloadDate.text = formatDownloadDate(file.lastModified())

        // Set file type and size
        holder.fileInfo.text = generateFileDescription(file)

        // Determine file type and set appropriate elements
        val isVideo = isVideoFile(file)
        if (isVideo) {
            holder.playIcon.visibility = View.VISIBLE
            holder.videoDuration.visibility = View.VISIBLE
            holder.videoDuration.text = "01"
        } else {
            holder.playIcon.visibility = View.GONE
            holder.videoDuration.visibility = View.GONE
        }

        // Load profile image
        loadProfileImage(holder.profileImage)

        // Set click listeners
        holder.itemView.setOnClickListener {
            onItemClick(file)
        }

        holder.actionButton.setOnClickListener {
            showFileActionMenu(file)
        }

        // This code has been moved to the new bindMediaItem function above

        holder.itemView.setOnClickListener {
            onItemClick(file)
        }
    }

    private fun loadNativeAd(holder: NativeAdViewHolder, position: Int) {
        try {
            // Check if we already have an ad for this position
            nativeAds[position]?.let { nativeAd ->
                try {
                    val adView = nativeAdManager.createSimpleNativeAdView(nativeAd)
                    holder.adContainer.removeAllViews()
                    holder.adContainer.addView(adView)
                    Log.d(TAG, "Reused existing native ad for position $position")
                    return
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to reuse native ad for position $position: ${e.message}")
                    nativeAds.remove(position) // Remove corrupted ad
                }
            }

            // Load new native ad
            val adUnitId = AdConfig.nativeAdUnitId
            val adLoader = AdLoader.Builder(context, adUnitId)
                .forNativeAd { nativeAd ->
                    try {
                        Log.d(TAG, "Native ad loaded for position $position")
                        nativeAds[position] = nativeAd

                        val adView = nativeAdManager.createSimpleNativeAdView(nativeAd)

                        // Ensure we're on the main thread
                        (context as? android.app.Activity)?.runOnUiThread {
                            try {
                                holder.adContainer.removeAllViews()
                                holder.adContainer.addView(adView)
                                Log.d(TAG, "Native ad view added for position $position")
                            } catch (e: Exception) {
                                Log.e(TAG, "Failed to add native ad view for position $position: ${e.message}")
                            }
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Exception processing native ad for position $position: ${e.message}")
                    }
                }
                .withAdListener(object : com.google.android.gms.ads.AdListener() {
                    override fun onAdFailedToLoad(adError: com.google.android.gms.ads.LoadAdError) {
                        Log.e(TAG, "Native ad failed to load for position $position: ${adError.message}")
                    }
                })
                .build()

            Log.d(TAG, "Loading native ad for position $position...")
            adLoader.loadAd(AdRequest.Builder().build())
        } catch (e: Exception) {
            Log.e(TAG, "Exception in loadNativeAd for position $position: ${e.message}")
        }
    }

    private fun isAdPosition(position: Int): Boolean {
        return (position + 1) % (AD_FREQUENCY + 1) == 0 && position > 0
    }

    private fun getMediaPosition(position: Int): Int {
        val adsBefore = position / (AD_FREQUENCY + 1)
        return position - adsBefore
    }

    /**
     * Update data
     */
    fun updateData(newFiles: List<File>) {
        mediaFiles = newFiles
        notifyDataSetChanged()
    }

    /**
     * Clean up native ads when adapter is destroyed
     */
    fun destroy() {
        nativeAds.values.forEach { nativeAd ->
            nativeAd.destroy()
        }
        nativeAds.clear()
        nativeAdManager.destroy()
    }

    // Helper functions
    private fun isVideoFile(file: File): Boolean {
        val videoExtensions = listOf("mp4", "mov", "avi", "mkv", "webm", "3gp")
        return videoExtensions.contains(file.extension.lowercase())
    }

    private fun formatDownloadDate(timestamp: Long): String {
        val now = System.currentTimeMillis()
        val diff = now - timestamp

        return when {
            diff < 60000 -> "📅 Just now"
            diff < 3600000 -> {
                val minutes = (diff / 60000).toInt()
                if (minutes == 1) "📅 1 minute ago" else "📅 ${minutes} minutes ago"
            }
            diff < 86400000 -> {
                val hours = (diff / 3600000).toInt()
                if (hours == 1) "📅 1 hour ago" else "📅 ${hours} hours ago"
            }
            diff < 604800000 -> {
                val days = (diff / 86400000).toInt()
                when (days) {
                    1 -> "📅 Yesterday"
                    2 -> "📅 2 days ago"
                    else -> "📅 ${days} days ago"
                }
            }
            diff < 2592000000L -> {
                val weeks = (diff / 604800000).toInt()
                if (weeks == 1) "📅 1 week ago" else "📅 ${weeks} weeks ago"
            }
            else -> {
                val dateFormat = java.text.SimpleDateFormat("📅 MMM dd, yyyy", java.util.Locale.getDefault())
                dateFormat.format(java.util.Date(timestamp))
            }
        }
    }

    private fun generateFileDescription(file: File): String {
        val fileType = getFileTypeWithIcon(file)
        val fileSize = formatFileSize(file.length())
        return "$fileType • $fileSize"
    }

    private fun getFileTypeWithIcon(file: File): String {
        val extension = file.extension.lowercase()
        return when (extension) {
            "mp4" -> "🎬 MP4 Video"
            "mov" -> "🎬 MOV Video"
            "avi" -> "🎬 AVI Video"
            "mkv" -> "🎬 MKV Video"
            "webm" -> "🎬 WebM Video"
            "3gp" -> "🎬 3GP Video"
            "jpg", "jpeg" -> "📷 JPEG Image"
            "png" -> "📷 PNG Image"
            "gif" -> "📷 GIF Image"
            "webp" -> "📷 WebP Image"
            "bmp" -> "📷 BMP Image"
            "tiff" -> "📷 TIFF Image"
            else -> {
                if (isVideoFile(file)) "🎬 Video File"
                else "📷 Image File"
            }
        }
    }

    private fun formatFileSize(bytes: Long): String {
        if (bytes <= 0) return "0 B"

        val kb = bytes / 1024.0
        val mb = kb / 1024.0
        val gb = mb / 1024.0

        return when {
            gb >= 1 -> String.format("%.1f GB", gb)
            mb >= 1 -> String.format("%.1f MB", mb)
            kb >= 1 -> String.format("%.1f KB", kb)
            else -> "$bytes B"
        }
    }

    private fun loadProfileImage(imageView: ImageView) {
        val colors = listOf(
            "#E91E63", "#9C27B0", "#673AB7", "#3F51B5",
            "#2196F3", "#00BCD4", "#4CAF50", "#FF9800"
        )
        val randomColor = colors.random()

        try {
            imageView.setBackgroundColor(android.graphics.Color.parseColor(randomColor))
            imageView.setImageResource(R.drawable.folder)
            imageView.scaleType = ImageView.ScaleType.CENTER
        } catch (e: Exception) {
            imageView.setImageResource(R.drawable.folder)
        }
    }

    private fun showFileActionMenu(file: File) {
        Log.d(TAG, "Action menu requested for file: ${file.name}")
    }
}
