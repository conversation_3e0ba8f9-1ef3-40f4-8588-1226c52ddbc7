package com.app.videofbdownloadfree

import android.content.Context
import android.net.Uri
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.AdLoader
import com.google.android.gms.ads.nativead.NativeAd
import java.io.File

/**
 * Simplified Media Adapter with Native Ads for RecyclerView
 */
class MediaAdapterWithNativeAds(
    private val context: Context,
    private var mediaFiles: List<File>,
    private val onItemClick: (File) -> Unit
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        private const val TYPE_MEDIA = 0
        private const val TYPE_NATIVE_AD = 1
        private const val AD_FREQUENCY = 6 // Show ad every 6 items
        private const val TAG = "MediaAdapterWithNativeAds"
    }

    private val nativeAdManager = NativeAdManager(context)
    private val nativeAds = mutableMapOf<Int, NativeAd>()

    // Glide options for better performance
    private val glideOptions = RequestOptions()
        .diskCacheStrategy(DiskCacheStrategy.ALL)
        .skipMemoryCache(false)
        .override(200, 200)

    inner class MediaViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val thumbnail: ImageView = itemView.findViewById(R.id.mediaThumbnail)
        val playIconContainer: View = itemView.findViewById(R.id.playIconContainer)
        val durationBadge: android.widget.TextView = itemView.findViewById(R.id.durationBadge)
        val mediaTypeIcon: ImageView = itemView.findViewById(R.id.mediaTypeIcon)
    }

    inner class NativeAdViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val adContainer: ViewGroup = itemView as ViewGroup
    }

    override fun getItemViewType(position: Int): Int {
        return if (isAdPosition(position)) TYPE_NATIVE_AD else TYPE_MEDIA
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            TYPE_NATIVE_AD -> {
                val adView = LayoutInflater.from(parent.context)
                    .inflate(R.layout.native_ad_small_layout, parent, false)
                NativeAdViewHolder(adView)
            }
            else -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_media, parent, false)
                MediaViewHolder(view)
            }
        }
    }

    override fun getItemCount(): Int {
        // Add extra items for ads
        val adCount = mediaFiles.size / AD_FREQUENCY
        return mediaFiles.size + adCount
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder.itemViewType) {
            TYPE_NATIVE_AD -> {
                val adHolder = holder as NativeAdViewHolder
                loadNativeAd(adHolder, position)
            }
            TYPE_MEDIA -> {
                val mediaHolder = holder as MediaViewHolder
                val mediaPosition = getMediaPosition(position)
                if (mediaPosition < mediaFiles.size) {
                    bindMediaItem(mediaHolder, mediaFiles[mediaPosition])
                }
            }
        }
    }

    private fun bindMediaItem(holder: MediaViewHolder, file: File) {
        // Clear previous image to prevent recycling issues
        holder.thumbnail.setImageDrawable(null)

        // Load thumbnail with Glide
        Glide.with(context)
            .load(Uri.fromFile(file))
            .apply(glideOptions)
            .centerCrop()
            .into(holder.thumbnail)

        val extension = file.extension.lowercase()
        if (extension in listOf("mp4", "mov", "avi", "mkv")) {
            // For videos
            holder.playIconContainer.visibility = View.VISIBLE
            holder.durationBadge.visibility = View.VISIBLE
            holder.mediaTypeIcon.setImageResource(android.R.drawable.ic_media_play)
            holder.durationBadge.text = "Video"
        } else if (extension in listOf("jpg", "jpeg", "png", "webp")) {
            // For images
            holder.playIconContainer.visibility = View.GONE
            holder.durationBadge.visibility = View.GONE
            holder.mediaTypeIcon.setImageResource(android.R.drawable.ic_menu_camera)
        } else {
            // Unknown file type
            holder.playIconContainer.visibility = View.GONE
            holder.durationBadge.visibility = View.VISIBLE
            holder.durationBadge.text = "File"
            holder.mediaTypeIcon.setImageResource(android.R.drawable.ic_menu_gallery)
        }

        holder.itemView.setOnClickListener {
            onItemClick(file)
        }
    }

    private fun loadNativeAd(holder: NativeAdViewHolder, position: Int) {
        try {
            // Check if we already have an ad for this position
            nativeAds[position]?.let { nativeAd ->
                try {
                    val adView = nativeAdManager.createSimpleNativeAdView(nativeAd)
                    holder.adContainer.removeAllViews()
                    holder.adContainer.addView(adView)
                    Log.d(TAG, "Reused existing native ad for position $position")
                    return
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to reuse native ad for position $position: ${e.message}")
                    nativeAds.remove(position) // Remove corrupted ad
                }
            }

            // Load new native ad
            val adUnitId = AdConfig.nativeAdUnitId
            val adLoader = AdLoader.Builder(context, adUnitId)
                .forNativeAd { nativeAd ->
                    try {
                        Log.d(TAG, "Native ad loaded for position $position")
                        nativeAds[position] = nativeAd

                        val adView = nativeAdManager.createSimpleNativeAdView(nativeAd)

                        // Ensure we're on the main thread
                        (context as? android.app.Activity)?.runOnUiThread {
                            try {
                                holder.adContainer.removeAllViews()
                                holder.adContainer.addView(adView)
                                Log.d(TAG, "Native ad view added for position $position")
                            } catch (e: Exception) {
                                Log.e(TAG, "Failed to add native ad view for position $position: ${e.message}")
                            }
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Exception processing native ad for position $position: ${e.message}")
                    }
                }
                .withAdListener(object : com.google.android.gms.ads.AdListener() {
                    override fun onAdFailedToLoad(adError: com.google.android.gms.ads.LoadAdError) {
                        Log.e(TAG, "Native ad failed to load for position $position: ${adError.message}")
                    }
                })
                .build()

            Log.d(TAG, "Loading native ad for position $position...")
            adLoader.loadAd(AdRequest.Builder().build())
        } catch (e: Exception) {
            Log.e(TAG, "Exception in loadNativeAd for position $position: ${e.message}")
        }
    }

    private fun isAdPosition(position: Int): Boolean {
        return (position + 1) % (AD_FREQUENCY + 1) == 0 && position > 0
    }

    private fun getMediaPosition(position: Int): Int {
        val adsBefore = position / (AD_FREQUENCY + 1)
        return position - adsBefore
    }

    /**
     * Update data
     */
    fun updateData(newFiles: List<File>) {
        mediaFiles = newFiles
        notifyDataSetChanged()
    }

    /**
     * Clean up native ads when adapter is destroyed
     */
    fun destroy() {
        nativeAds.values.forEach { nativeAd ->
            nativeAd.destroy()
        }
        nativeAds.clear()
        nativeAdManager.destroy()
    }
}
